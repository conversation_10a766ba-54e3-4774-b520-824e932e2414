<?php

class TemplateEngine {
    private $skinPath;
    private $globalData = [];

    public function __construct($skinPath) {
        $this->skinPath = $skinPath;
    }

    public function setGlobalData($key, $value) {
        $this->globalData[$key] = $value;
    }

    public function render($template, $data = []) {
        // Połącz dane globalne z danymi szablonu
        $data = array_merge($this->globalData, $data);
        
        // Wyodrębnij zmienne do lokalnego zakresu
        extract($data);
        
        // Sprawdź czy szablon istnieje
        $templateFile = $this->skinPath . '/templates/' . $template . '.php';
        
        if (!file_exists($templateFile)) {
            // Fallback - spróbuj znaleźć szablon w katalogu głównym skórki
            $templateFile = $this->skinPath . '/' . $template . '.php';
            
            if (!file_exists($templateFile)) {
                throw new Exception("Template not found: " . $template);
            }
        }
        
        // Rozpocznij buforowanie wyjścia
        ob_start();
        
        // Załaduj szablon
        include $templateFile;
        
        // Pobierz zawartość bufora i wyczyść go
        $content = ob_get_clean();
        
        // Wyświetl zawartość
        echo $content;
    }

    public function renderPartial($partial, $data = []) {
        // Połącz dane globalne z danymi częściowego szablonu
        $data = array_merge($this->globalData, $data);
        
        // Wyodrębnij zmienne do lokalnego zakresu
        extract($data);
        
        // Sprawdź czy częściowy szablon istnieje
        $partialFile = $this->skinPath . '/partials/' . $partial . '.php';
        
        if (!file_exists($partialFile)) {
            throw new Exception("Partial template not found: " . $partial);
        }
        
        // Załaduj częściowy szablon
        include $partialFile;
    }

    public function getPartial($partial, $data = []) {
        // Połącz dane globalne z danymi częściowego szablonu
        $data = array_merge($this->globalData, $data);
        
        // Wyodrębnij zmienne do lokalnego zakresu
        extract($data);
        
        // Sprawdź czy częściowy szablon istnieje
        $partialFile = $this->skinPath . '/partials/' . $partial . '.php';
        
        if (!file_exists($partialFile)) {
            throw new Exception("Partial template not found: " . $partial);
        }
        
        // Rozpocznij buforowanie wyjścia
        ob_start();
        
        // Załaduj częściowy szablon
        include $partialFile;
        
        // Pobierz zawartość bufora i wyczyść go
        return ob_get_clean();
    }

    public function getSkinPath() {
        return $this->skinPath;
    }

    public function getSkinUrl() {
        // Zwróć URL do zasobów skórki - względem katalogu frontend
        return '/skins/' . basename($this->skinPath);
    }
}
