/* Custom CSS for ZRYWBUD */

/* Fonts */
body {
    font-family: 'Roboto', sans-serif;
}

h1,
h2,
h3,
h4,
h5,
h6,
.navbar-brand,
.btn {
    font-family: 'Montserrat', sans-serif;
}

/* Custom Colors */
:root {
    --zrywbud-red: #ea384c;
    --zrywbud-gray: #333333;
    --zrywbud-lightgray: #f5f5f5;
    --zrywbud-darkgray: #222222;
}

.text-zrywbud-red {
    color: var(--zrywbud-red) !important;
}

.text-zrywbud-gray {
    color: var(--zrywbud-gray) !important;
}

.bg-zrywbud-lightgray {
    background-color: var(--zrywbud-lightgray) !important;
}

.bg-zrywbud-darkgray {
    background-color: var(--zrywbud-darkgray) !important;
}

.btn-zrywbud-red {
    background-color: var(--zrywbud-red);
    border-color: var(--zrywbud-red);
    color: white;
}

.btn-zrywbud-red:hover {
    background-color: #d32f43;
    border-color: #d32f43;
    color: white;
}

/* Header Styles */
.navbar {
    transition: all 0.3s ease;
    padding: 1rem 0;
}

.navbar.scrolled {
    background-color: white !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 0.5rem 0;
}

.navbar.scrolled .navbar-brand,
.navbar.scrolled .nav-link {
    color: var(--zrywbud-gray) !important;
}

.navbar.scrolled .nav-link:hover {
    color: var(--zrywbud-red) !important;
}

.navbar-toggler {
    border: none;
    padding: 0.25rem 0.5rem;
}

.navbar-toggler:focus {
    box-shadow: none;
}

/* Hero Section */
.hero-section {
    min-height: 100vh;
    background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.7)), url('hero-roofing.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    position: relative;
}

.scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
}

.scroll-indicator a {
    text-decoration: none;
    transition: color 0.3s ease;
}

.scroll-indicator a:hover {
    color: var(--zrywbud-red) !important;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounce {

    0%,
    20%,
    50%,
    80%,
    100% {
        transform: translateY(0);
    }

    40% {
        transform: translateY(-10px);
    }

    60% {
        transform: translateY(-5px);
    }
}

.fade-in-up {
    animation: fadeInUp 0.8s ease-out;
    animation-fill-mode: both;
}

.bounce-animation {
    animation: bounce 2s infinite;
}

.hover-scale {
    transition: transform 0.3s ease;
}

.hover-scale:hover {
    transform: scale(1.05);
}

/* Section Divider */
.divider {
    width: 60px;
    height: 4px;
    background-color: var(--zrywbud-red);
}

/* About Cards */
.about-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.about-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.icon-wrapper i {
    font-size: 3rem;
}

/* Service Items */
.service-item {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease;
}

.service-item.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Gallery */
.gallery-card {
    position: relative;
    overflow: hidden;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.gallery-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.gallery-card img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, transparent, rgba(0, 0, 0, 0.8));
    color: white;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.gallery-card:hover .gallery-overlay {
    opacity: 1;
}

.gallery-card:hover img {
    transform: scale(1.1);
}

.filter-btn {
    transition: all 0.3s ease;
}

.filter-btn.active {
    background-color: var(--zrywbud-red) !important;
    border-color: var(--zrywbud-red) !important;
    color: white !important;
}

/* Testimonials */
.testimonial-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    position: relative;
}

.quote-icon {
    position: absolute;
    top: -10px;
    left: 20px;
    background: white;
    padding: 0 10px;
}

.quote-icon i {
    font-size: 2rem;
    color: var(--zrywbud-red);
    opacity: 0.3;
}

.testimonial-text {
    margin-bottom: 1.5rem;
    font-style: italic;
    color: #666;
}

/* Contact Form */
.contact-input {
    background-color: var(--zrywbud-gray);
    border: 1px solid #555;
    color: white;
    border-radius: 5px;
}

.contact-input:focus {
    background-color: var(--zrywbud-gray);
    border-color: var(--zrywbud-red);
    color: white;
    box-shadow: 0 0 0 0.2rem rgba(234, 56, 76, 0.25);
}

.contact-input::placeholder {
    color: #ccc;
}

.contact-info-card {
    background-color: var(--zrywbud-gray);
    padding: 2rem;
    border-radius: 10px;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1.5rem;
}

.contact-item i {
    font-size: 1.5rem;
    margin-right: 1rem;
    margin-top: 0.25rem;
}

/* Footer */
.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    text-decoration: none;
    transition: color 0.3s ease;
    color: #ccc !important;
}

.footer-links a:hover {
    color: var(--zrywbud-red) !important;
}

.hover-red:hover {
    color: var(--zrywbud-red) !important;
}

.newsletter-input {
    background-color: #444;
    border: 1px solid #666;
    color: white;
}

.newsletter-input:focus {
    background-color: #444;
    border-color: var(--zrywbud-red);
    color: white;
    box-shadow: 0 0 0 0.2rem rgba(234, 56, 76, 0.25);
}

.newsletter-input::placeholder {
    color: #bbb;
}

/* Footer text colors */
footer .text-muted {
    color: #ccc !important;
}

footer h5 {
    color: white !important;
}

footer p {
    color: #ccc !important;
}

/* Social links in footer */
.social-links a {
    color: #ccc !important;
    transition: color 0.3s ease;
}

.social-links a:hover {
    color: var(--zrywbud-red) !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .hero-section {
        background-attachment: scroll;
    }

    .display-4 {
        font-size: 2.5rem;
    }

    .gallery-card img {
        height: 200px;
    }

    .contact-info-card {
        margin-top: 2rem;
    }
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Loading animations */
.fade-in {
    opacity: 0;
    animation: fadeIn 0.6s ease-out forwards;
}

@keyframes fadeIn {
    to {
        opacity: 1;
    }
}

/* Scroll animations */
.scroll-animate {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease;
}

.scroll-animate.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Gallery Page Styles */
.gallery-hero {
    min-height: 60vh;
    background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.8)), url('hero-roofing.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
}

.breadcrumb {
    background: none;
    padding: 0;
}

.breadcrumb-item a {
    color: white;
    text-decoration: none;
}

.breadcrumb-item a:hover {
    color: var(--zrywbud-red);
}

.breadcrumb-item.active {
    color: var(--zrywbud-red);
}

/* Enhanced Gallery Cards */
.gallery-card .badge {
    position: absolute;
    top: 15px;
    left: 15px;
    z-index: 2;
}

/* Load More Button */
#loadMoreBtn {
    transition: all 0.3s ease;
}

#loadMoreBtn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Modal Enhancements */
.modal-xl .modal-dialog {
    max-width: 1200px;
}

/* Filter Button Enhancements */
.btn-group .btn {
    margin: 0.25rem;
}

@media (max-width: 768px) {
    .btn-group {
        flex-direction: column;
        width: 100%;
    }

    .btn-group .btn {
        margin: 0.25rem 0;
    }

    .gallery-hero {
        background-attachment: scroll;
        min-height: 50vh;
    }
}