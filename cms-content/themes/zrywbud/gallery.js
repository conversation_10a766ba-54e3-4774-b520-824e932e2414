// Gallery Page Specific JavaScript

// Enhanced gallery modal functionality
function initEnhancedGalleryModal() {
    const modal = document.getElementById('galleryModal');
    const modalTitle = document.getElementById('modalTitle');
    const modalImage = document.getElementById('modalImage');
    const modalDescription = document.getElementById('modalDescription');
    const modalCategory = document.getElementById('modalCategory');
    
    // Handle modal trigger buttons
    document.querySelectorAll('[data-bs-toggle="modal"]').forEach(button => {
        button.addEventListener('click', () => {
            const title = button.getAttribute('data-title');
            const image = button.getAttribute('data-image');
            const description = button.getAttribute('data-description');
            const category = button.getAttribute('data-category');
            
            modalTitle.textContent = title;
            modalImage.src = image;
            modalImage.alt = title;
            modalDescription.textContent = description;
            
            // Set category badge
            modalCategory.textContent = category;
            modalCategory.className = 'badge mb-3';
            
            switch(category) {
                case 'Dach':
                    modalCategory.classList.add('bg-danger');
                    break;
                case 'Elewacja':
                    modalCategory.classList.add('bg-success');
                    break;
                case 'Kompleksowe':
                    modalCategory.classList.add('bg-warning');
                    break;
                default:
                    modalCategory.classList.add('bg-secondary');
            }
        });
    });
}

// Load more functionality
function initLoadMore() {
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    const galleryContainer = document.getElementById('galleryContainer');
    
    // Additional projects data
    const additionalProjects = [
        {
            category: 'dachy',
            badge: 'Dach',
            badgeClass: 'bg-danger',
            title: 'Remont dachu mansardowego',
            description: 'Kompleksowy remont dachu mansardowego z izolacją.',
            image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
            fullDescription: 'Kompleksowy remont dachu mansardowego z izolacją termiczną i akustyczną. Projekt obejmował wymianę konstrukcji, montaż okien dachowych oraz wykończenie poddasza.'
        },
        {
            category: 'elewacje',
            badge: 'Elewacja',
            badgeClass: 'bg-success',
            title: 'Elewacja z cegły klinkierowej',
            description: 'Wykończenie elewacji cegłą klinkierową.',
            image: 'https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
            fullDescription: 'Wykończenie elewacji cegłą klinkierową z zastosowaniem nowoczesnych technik murowania. Projekt charakteryzuje się wysoką estetyką i trwałością.'
        },
        {
            category: 'kompleksowe',
            badge: 'Kompleksowe',
            badgeClass: 'bg-warning',
            title: 'Rewitalizacja kamienicy',
            description: 'Kompleksowa rewitalizacja zabytkowej kamienicy.',
            image: 'https://images.unsplash.com/photo-1600566753190-17f0baa2a6c3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
            fullDescription: 'Kompleksowa rewitalizacja zabytkowej kamienicy z zachowaniem historycznego charakteru. Prace obejmowały remont dachu, elewacji oraz modernizację instalacji.'
        },
        {
            category: 'dachy',
            badge: 'Dach',
            badgeClass: 'bg-danger',
            title: 'Dach z blachy trapezowej',
            description: 'Montaż nowoczesnego dachu z blachy trapezowej.',
            image: 'https://images.unsplash.com/photo-1600607687644-c7171b42498b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
            fullDescription: 'Montaż nowoczesnego dachu z blachy trapezowej z pełną izolacją termiczną. Rozwiązanie idealne dla budynków przemysłowych i mieszkalnych.'
        },
        {
            category: 'elewacje',
            badge: 'Elewacja',
            badgeClass: 'bg-success',
            title: 'Elewacja wentylowana',
            description: 'Montaż nowoczesnej elewacji wentylowanej.',
            image: 'https://images.unsplash.com/photo-1600607687920-4e2a09cf159d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
            fullDescription: 'Montaż nowoczesnej elewacji wentylowanej z płyt kompozytowych. System zapewnia doskonałą izolację termiczną i estetyczny wygląd.'
        },
        {
            category: 'kompleksowe',
            badge: 'Kompleksowe',
            badgeClass: 'bg-warning',
            title: 'Modernizacja bloku mieszkalnego',
            description: 'Kompleksowa modernizacja budynku wielorodzinnego.',
            image: 'https://images.unsplash.com/photo-1600607688969-a5bfcd646154?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
            fullDescription: 'Kompleksowa modernizacja budynku wielorodzinnego obejmująca termomodernizację, wymianę dachu oraz modernizację klatek schodowych.'
        }
    ];
    
    let currentIndex = 0;
    const itemsPerLoad = 3;
    
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', () => {
            const itemsToLoad = additionalProjects.slice(currentIndex, currentIndex + itemsPerLoad);
            
            itemsToLoad.forEach(project => {
                const projectHTML = `
                    <div class="col-lg-4 col-md-6 gallery-item" data-category="${project.category}">
                        <div class="gallery-card">
                            <img src="${project.image}" alt="${project.title}" class="img-fluid">
                            <div class="gallery-overlay">
                                <span class="badge ${project.badgeClass} mb-2">${project.badge}</span>
                                <h5>${project.title}</h5>
                                <p>${project.description}</p>
                                <button class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#galleryModal"
                                        data-title="${project.title}"
                                        data-image="${project.image.replace('w=800', 'w=1200')}"
                                        data-description="${project.fullDescription}"
                                        data-category="${project.badge}">
                                    Zobacz szczegóły
                                </button>
                            </div>
                        </div>
                    </div>
                `;
                
                galleryContainer.insertAdjacentHTML('beforeend', projectHTML);
            });
            
            currentIndex += itemsPerLoad;
            
            // Hide button if no more items
            if (currentIndex >= additionalProjects.length) {
                loadMoreBtn.style.display = 'none';
            }
            
            // Re-initialize modal for new items
            initEnhancedGalleryModal();
            
            // Add scroll animation to new items
            const newItems = galleryContainer.querySelectorAll('.gallery-item:not(.scroll-animate)');
            newItems.forEach(item => {
                item.classList.add('scroll-animate');
                setTimeout(() => {
                    item.classList.add('visible');
                }, 100);
            });
            
            // Update filter functionality for new items
            initGalleryFilter();
        });
    }
}

// Enhanced filter with animation
function initEnhancedGalleryFilter() {
    const filterButtons = document.querySelectorAll('.filter-btn');
    const galleryItems = document.querySelectorAll('.gallery-item');
    
    filterButtons.forEach(button => {
        button.addEventListener('click', () => {
            const filter = button.getAttribute('data-filter');
            
            // Update active button
            filterButtons.forEach(btn => {
                btn.classList.remove('active', 'btn-zrywbud-red');
                btn.classList.add('btn-outline-secondary');
            });
            button.classList.remove('btn-outline-secondary');
            button.classList.add('active', 'btn-zrywbud-red');
            
            // Filter gallery items with animation
            galleryItems.forEach((item, index) => {
                const category = item.getAttribute('data-category');
                
                if (filter === 'all' || category === filter) {
                    item.style.display = 'block';
                    setTimeout(() => {
                        item.style.opacity = '1';
                        item.style.transform = 'scale(1) translateY(0)';
                    }, index * 50);
                } else {
                    item.style.opacity = '0';
                    item.style.transform = 'scale(0.8) translateY(20px)';
                    setTimeout(() => {
                        item.style.display = 'none';
                    }, 300);
                }
            });
            
            // Update counter
            updateProjectCounter(filter);
        });
    });
}

// Project counter
function updateProjectCounter(filter) {
    const galleryItems = document.querySelectorAll('.gallery-item');
    let visibleCount = 0;
    
    galleryItems.forEach(item => {
        const category = item.getAttribute('data-category');
        if (filter === 'all' || category === filter) {
            visibleCount++;
        }
    });
    
    // Create or update counter
    let counter = document.getElementById('projectCounter');
    if (!counter) {
        counter = document.createElement('div');
        counter.id = 'projectCounter';
        counter.className = 'text-center text-muted mt-3';
        document.querySelector('.btn-group').parentNode.appendChild(counter);
    }
    
    const filterName = filter === 'all' ? 'wszystkich kategorii' : 
                      filter === 'dachy' ? 'kategorii Dachy' :
                      filter === 'elewacje' ? 'kategorii Elewacje' : 'kategorii Kompleksowe';
    
    counter.innerHTML = `<small>Wyświetlane: <strong>${visibleCount}</strong> projektów z ${filterName}</small>`;
}

// Lazy loading for images
function initLazyLoading() {
    const images = document.querySelectorAll('.gallery-card img');
    
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.classList.add('fade-in');
                observer.unobserve(img);
            }
        });
    });
    
    images.forEach(img => {
        imageObserver.observe(img);
    });
}

// Search functionality
function initGallerySearch() {
    // Create search input
    const searchHTML = `
        <div class="row mb-4">
            <div class="col-md-6 mx-auto">
                <div class="input-group">
                    <span class="input-group-text"><i class="bi bi-search"></i></span>
                    <input type="text" class="form-control" id="gallerySearch" placeholder="Szukaj projektów...">
                </div>
            </div>
        </div>
    `;
    
    const container = document.querySelector('.container .text-center');
    container.insertAdjacentHTML('afterend', searchHTML);
    
    const searchInput = document.getElementById('gallerySearch');
    
    searchInput.addEventListener('input', (e) => {
        const searchTerm = e.target.value.toLowerCase();
        const galleryItems = document.querySelectorAll('.gallery-item');
        
        galleryItems.forEach(item => {
            const title = item.querySelector('h5').textContent.toLowerCase();
            const description = item.querySelector('p').textContent.toLowerCase();
            
            if (title.includes(searchTerm) || description.includes(searchTerm)) {
                item.style.display = 'block';
                item.style.opacity = '1';
                item.style.transform = 'scale(1)';
            } else {
                item.style.opacity = '0';
                item.style.transform = 'scale(0.8)';
                setTimeout(() => {
                    item.style.display = 'none';
                }, 300);
            }
        });
    });
}

// Initialize gallery page functionality
document.addEventListener('DOMContentLoaded', () => {
    // Only run on gallery page
    if (document.getElementById('galleryContainer')) {
        initEnhancedGalleryModal();
        initEnhancedGalleryFilter();
        initLoadMore();
        initLazyLoading();
        initGallerySearch();
        
        // Initial counter update
        updateProjectCounter('all');
        
        // Add scroll animations to initial items
        document.querySelectorAll('.gallery-item').forEach(item => {
            item.classList.add('scroll-animate');
        });
        
        // Initialize scroll animations
        initScrollAnimations();
    }
});
