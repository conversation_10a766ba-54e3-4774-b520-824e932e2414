<?php
// Rozpocznij buforowanie zawartości
ob_start();
?>

<section class="py-5" style="margin-top: 100px;">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h2 mb-0">Artykuły</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="/">Strona główna</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Artykuły</li>
                        </ol>
                    </nav>
                </div>

                <?php if (!empty($articles)): ?>
                    <div class="row g-4">
                        <?php foreach ($articles as $article): ?>
                            <div class="col-md-6">
                                <article class="card h-100 border-0 shadow-sm">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <span class="badge bg-zrywbud-red"><?= htmlspecialchars($article['category_name'] ?? 'Bez kategorii') ?></span>
                                            <small class="text-muted"><?= date('d.m.Y', strtotime($article['created_at'])) ?></small>
                                        </div>

                                        <h5 class="card-title">
                                            <a href="/artykuly/<?= htmlspecialchars($article['slug']) ?>" class="text-decoration-none text-dark">
                                                <?= htmlspecialchars($article['title']) ?>
                                            </a>
                                        </h5>

                                        <?php if ($article['excerpt']): ?>
                                            <p class="card-text text-muted"><?= htmlspecialchars(substr($article['excerpt'], 0, 150)) ?>...</p>
                                        <?php endif; ?>

                                        <?php if (!empty($article['tags'])): ?>
                                            <div class="mb-3">
                                                <?php foreach ($article['tags'] as $tag): ?>
                                                    <span class="badge bg-light text-dark me-1">#<?= htmlspecialchars($tag) ?></span>
                                                <?php endforeach; ?>
                                            </div>
                                        <?php endif; ?>

                                        <a href="/artykuly/<?= htmlspecialchars($article['slug']) ?>" class="btn btn-outline-primary btn-sm">
                                            Czytaj więcej <i class="bi bi-arrow-right ms-1"></i>
                                        </a>
                                    </div>
                                </article>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <!-- Pagination placeholder -->
                    <?php if (isset($current_page) && $current_page > 1): ?>
                        <nav aria-label="Paginacja artykułów" class="mt-5">
                            <ul class="pagination justify-content-center">
                                <li class="page-item">
                                    <a class="page-link" href="/artykuly?page=<?= $current_page - 1 ?>">Poprzednia</a>
                                </li>
                                <li class="page-item active">
                                    <span class="page-link"><?= $current_page ?></span>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="/artykuly?page=<?= $current_page + 1 ?>">Następna</a>
                                </li>
                            </ul>
                        </nav>
                    <?php endif; ?>

                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="bi bi-newspaper display-1 text-muted mb-4"></i>
                        <h3>Brak artykułów</h3>
                        <p class="text-muted">Obecnie nie ma opublikowanych artykułów.</p>
                        <a href="/" class="btn btn-zrywbud-red">Wróć do strony głównej</a>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <div class="sticky-top" style="top: 120px;">
                    <!-- Categories -->
                    <?php if (!empty($categories)): ?>
                        <div class="card border-0 shadow-sm mb-4">
                            <div class="card-header bg-zrywbud-red text-white">
                                <h6 class="mb-0"><i class="bi bi-folder me-2"></i>Kategorie</h6>
                            </div>
                            <div class="card-body">
                                <div class="list-group list-group-flush">
                                    <?php foreach ($categories as $category): ?>
                                        <a href="/kategoria/<?= htmlspecialchars($category['slug']) ?>"
                                            class="list-group-item list-group-item-action border-0 px-0">
                                            <?= htmlspecialchars($category['name']) ?>
                                        </a>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Contact CTA -->
                    <div class="card border-0 shadow-sm bg-zrywbud-red text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-telephone display-4 mb-3"></i>
                            <h5>Potrzebujesz pomocy?</h5>
                            <p class="mb-3">Skontaktuj się z nami, aby otrzymać bezpłatną wycenę.</p>
                            <a href="/kontakt" class="btn btn-light btn-sm">Skontaktuj się</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php
// Pobierz zawartość bufora
$content = ob_get_clean();

// Załaduj layout bezpośrednio
extract([
    'title' => $title,
    'meta_description' => $meta_description,
    'content' => $content
]);

include __DIR__ . '/layout.php';
?>