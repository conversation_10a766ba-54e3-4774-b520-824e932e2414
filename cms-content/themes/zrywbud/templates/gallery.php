<?php
// Rozpocznij buforowanie zawartości
ob_start();
?>

<!-- Hero Section -->
<section class="gallery-hero d-flex align-items-center">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h1 class="display-4 fw-bold text-white mb-4 fade-in-up">
                    Nasze <span class="text-zrywbud-red">Realizacje</span>
                </h1>
                <p class="lead text-white mb-5 fade-in-up" style="animation-delay: 0.4s;">
                    Poznaj nasze najlepsze projekty remontów dachów i elewacji. Każda realizacja to dowód naszego doświadczenia i dbałości o szczegóły.
                </p>
                <nav aria-label="breadcrumb" class="fade-in-up" style="animation-delay: 0.6s;">
                    <ol class="breadcrumb justify-content-center">
                        <li class="breadcrumb-item"><a href="/" class="text-white">Strona główna</a></li>
                        <li class="breadcrumb-item active text-zrywbud-red" aria-current="page">Galeria</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</section>

<!-- Gallery Section -->
<section class="py-5">
    <div class="container">
        <!-- Filter Buttons -->
        <?php if (!empty($categories)): ?>
            <div class="text-center mb-5">
                <div class="btn-group flex-wrap" role="group">
                    <button type="button" class="btn btn-zrywbud-red filter-btn active" data-filter="all">
                        <i class="bi bi-grid-3x3-gap me-2"></i>Wszystkie
                    </button>
                    <?php foreach ($categories as $category): ?>
                        <button type="button" class="btn btn-outline-secondary filter-btn" data-filter="<?= htmlspecialchars($category['slug']) ?>">
                            <i class="bi bi-folder me-2"></i><?= htmlspecialchars($category['name']) ?>
                        </button>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>

        <!-- Gallery Grid -->
        <div class="row g-4" id="galleryContainer">
            <?php if (!empty($images)): ?>
                <?php foreach ($images as $image): ?>
                    <div class="col-lg-4 col-md-6 gallery-item" data-category="<?= htmlspecialchars($image['category_slug'] ?? 'all') ?>">
                        <div class="gallery-card">
                            <img src="<?= htmlspecialchars($image['filepath']) ?>"
                                alt="<?= htmlspecialchars($image['title'] ?? 'Realizacja ZRYWBUD') ?>"
                                class="img-fluid">
                            <div class="gallery-overlay">
                                <?php if ($image['category_name']): ?>
                                    <span class="badge bg-zrywbud-red mb-2"><?= htmlspecialchars($image['category_name']) ?></span>
                                <?php endif; ?>
                                <h5><?= htmlspecialchars($image['title'] ?? 'Realizacja ZRYWBUD') ?></h5>
                                <?php if ($image['description']): ?>
                                    <p><?= htmlspecialchars($image['description']) ?></p>
                                <?php endif; ?>
                                <button class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#galleryModal"
                                    data-title="<?= htmlspecialchars($image['title'] ?? 'Realizacja ZRYWBUD') ?>"
                                    data-image="<?= htmlspecialchars($image['filepath']) ?>"
                                    data-description="<?= htmlspecialchars($image['description'] ?? '') ?>"
                                    data-category="<?= htmlspecialchars($image['category_name'] ?? '') ?>">
                                    Zobacz szczegóły
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="col-12 text-center">
                    <div class="alert alert-info">
                        <h4>Brak zdjęć w galerii</h4>
                        <p>Obecnie nie ma żadnych zdjęć do wyświetlenia w galerii.</p>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Load More Button -->
        <?php if (!empty($images) && count($images) >= 12): ?>
            <div class="text-center mt-5">
                <button class="btn btn-outline-secondary btn-lg" id="loadMoreBtn">
                    <i class="bi bi-plus-circle me-2"></i>Załaduj więcej projektów
                </button>
            </div>
        <?php endif; ?>
    </div>
</section>

<!-- Gallery Modal -->
<div class="modal fade" id="galleryModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle"></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-lg-8">
                        <img id="modalImage" src="" alt="" class="img-fluid rounded">
                    </div>
                    <div class="col-lg-4">
                        <span id="modalCategory" class="badge mb-3"></span>
                        <p id="modalDescription" class="text-muted"></p>
                        <div class="mt-4">
                            <h6>Szczegóły projektu:</h6>
                            <ul class="list-unstyled">
                                <li><i class="bi bi-check-circle text-success me-2"></i>Profesjonalne materiały</li>
                                <li><i class="bi bi-check-circle text-success me-2"></i>Gwarancja na wykonane prace</li>
                                <li><i class="bi bi-check-circle text-success me-2"></i>Terminowa realizacja</li>
                                <li><i class="bi bi-check-circle text-success me-2"></i>Doświadczony zespół</li>
                            </ul>
                        </div>
                        <a href="/kontakt" class="btn btn-zrywbud-red w-100 mt-3" data-bs-dismiss="modal">
                            <i class="bi bi-envelope me-2"></i>Zapytaj o podobny projekt
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Pobierz zawartość bufora
$content = ob_get_clean();

// Dodatkowe skrypty dla galerii
$additional_scripts = '<script src="' . $skin_url . '/gallery.js"></script>';

// Załaduj layout bezpośrednio
extract([
    'title' => $title,
    'meta_description' => $meta_description,
    'content' => $content,
    'additional_scripts' => $additional_scripts
]);

include __DIR__ . '/layout.php';
?>