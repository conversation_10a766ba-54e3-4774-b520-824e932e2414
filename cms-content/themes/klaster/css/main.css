/* 
 * Główny plik CSS dla projektu Innowacyjna Medycyna
 * Zawiera style używane w całym projekcie
 */

:root {
  --primary: #447c74;
  --primary-light: #28b9b2;
  --secondary: #8eb6b4;
  --light: #c1dada;
  --dark: #333;
  --text-dark: #333;
  --text-light: #666;
  --white: #fff;
  --gray-light: #f8f9fa;
}

body {
  font-family: 'Roboto', sans-serif;
  color: var(--text-dark);
  background-color: #f8f9fa;
  line-height: 1.6;
}

/* Nagłówki */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1rem;
}

/* Przyciski niestandardowe */
.btn-primary-custom {
  background-color: var(--primary);
  border-color: var(--primary);
  color: var(--white);
}

.btn-primary-custom:hover {
  background-color: var(--primary-light);
  border-color: var(--primary-light);
  color: var(--white);
}

.btn-outline-primary-custom {
  background-color: transparent;
  border-color: var(--white);
  color: var(--white);
}

.btn-outline-primary-custom:hover {
  background-color: var(--white);
  color: var(--primary);
}

/* Karty */
.card {
  transition: transform 0.3s, box-shadow 0.3s;
  border: none;
  border-radius: 0.5rem;
  overflow: hidden;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.card-img-top {
  height: 200px;
  object-fit: cover;
}

/* Tagi i kategorie */
.badge-category {
  background-color: var(--primary);
  color: var(--white);
  font-weight: 500;
  padding: 0.35em 0.65em;
  border-radius: 50rem;
}

.badge-type {
  background-color: rgba(0, 0, 0, 0.7);
  color: var(--white);
  font-weight: 500;
  padding: 0.25em 0.5em;
  border-radius: 50rem;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

.tag {
  background-color: #f0f0f0;
  color: var(--text-light);
  padding: 0.25em 0.75em;
  border-radius: 50rem;
  display: inline-block;
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  transition: background-color 0.2s;
}

.tag:hover {
  background-color: #e0e0e0;
  cursor: pointer;
}

/* Nawigacja */
.navbar-custom {
  background-color: var(--white);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.navbar-custom .navbar-brand img {
  height: 48px;
}

.navbar-custom .nav-link {
  color: var(--text-dark);
  font-weight: 500;
  padding: 0.5rem 1rem;
  transition: color 0.2s;
}

.navbar-custom .nav-link:hover,
.navbar-custom .nav-link.active {
  color: var(--primary-light);
}

.navbar-custom .nav-link.active {
  border-bottom: 2px solid var(--primary-light);
}

/* Stopka */
.footer {
  background-color: var(--primary);
  color: var(--white);
  padding: 3rem 0;
}

.footer h5 {
  color: var(--secondary);
  font-weight: 600;
  margin-bottom: 1.5rem;
}

.footer a {
  color: var(--light);
  transition: color 0.2s;
}

.footer a:hover {
  color: var(--white);
  text-decoration: none;
}

.footer-bottom {
  border-top: 1px solid var(--primary-light);
  padding-top: 1.5rem;
  margin-top: 3rem;
}

.social-icon {
  color: var(--secondary);
  font-size: 1.5rem;
  margin-right: 1rem;
  transition: color 0.2s;
}

.social-icon:hover {
  color: var(--white);
}

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 50%, var(--secondary) 100%);
  color: var(--white);
  position: relative;
  padding: 6rem 0;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.1);
}

.hero-content {
  position: relative;
  z-index: 1;
}

/* Sekcje */
.section {
  padding: 4rem 0;
}

.section-light {
  background-color: var(--white);
}

.section-colored {
  background-color: var(--light);
}

/* CTA Section */
.cta-section {
  background: linear-gradient(to right, var(--primary), var(--primary-light));
  color: var(--white);
  padding: 4rem 0;
  text-align: center;
}

.cta-section h2 {
  color: var(--secondary);
  margin-bottom: 1.5rem;
}

/* Responsywność */
@media (max-width: 768px) {
  .hero-section {
    padding: 4rem 0;
  }
  
  h1 {
    font-size: 2.5rem;
  }
  
  h2 {
    font-size: 2rem;
  }
} 