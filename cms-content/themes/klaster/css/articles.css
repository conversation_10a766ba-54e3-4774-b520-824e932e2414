/* 
 * Plik CSS dla strony z listą artykułów
 * Zawiera style specyficzne dla strony z listą artykułów
 */

/* Nagłówek strony */
.articles-header {
  background-color: var(--white);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding: 3rem 0;
  text-align: center;
}

/* Filtry */
.filters-section {
  background-color: var(--white);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding: 1.5rem 0;
}

.search-container {
  position: relative;
}

.search-container .search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--primary);
  z-index: 1;
}

.search-input {
  padding-left: 2.5rem;
  border-color: var(--light);
}

.search-input:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 0.25rem rgba(68, 124, 116, 0.25);
}

.category-filter {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.category-btn {
  padding: 0.5rem 1rem;
  border-radius: 50rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s;
  border: 1px solid var(--light);
  color: var(--text-dark);
}

.category-btn:hover {
  background-color: var(--light);
  color: var(--text-dark);
}

.category-btn.active {
  background-color: var(--primary);
  border-color: var(--primary);
  color: var(--white);
}

/* Link "Czytaj więcej" */
.card-link {
  color: var(--primary);
  text-decoration: none;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  transition: color 0.2s;
}

.card-link:hover {
  color: var(--primary-light);
  text-decoration: none;
}

.card-link-icon {
  margin-left: 0.5rem;
  transition: transform 0.2s;
}

.card-link:hover .card-link-icon {
  transform: translateX(3px);
}

/* Brak wyników */
.no-results {
  text-align: center;
  padding: 3rem 0;
}

.no-results-icon {
  color: #ccc;
  font-size: 3rem;
  margin-bottom: 1rem;
}

/* Responsywność */
@media (max-width: 768px) {
  .filters-section .row {
    flex-direction: column;
  }
  
  .search-container {
    margin-bottom: 1rem;
  }
  
  .category-filter {
    justify-content: center;
  }
} 