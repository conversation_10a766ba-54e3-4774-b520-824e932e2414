/* 
 * Plik CSS dla strony pojedynczego artykułu
 * Zawiera style specyficzne dla strony pojedynczego artykułu
 */

/* Breadcrumb */
.breadcrumb-custom {
  background-color: var(--white);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding: 1rem 0;
}

.breadcrumb-custom .breadcrumb {
  margin-bottom: 0;
}

.breadcrumb-custom .breadcrumb-item a {
  color: var(--text-light);
  transition: color 0.2s;
}

.breadcrumb-custom .breadcrumb-item a:hover {
  color: var(--primary);
  text-decoration: none;
}

.breadcrumb-custom .breadcrumb-item.active {
  color: var(--text-dark);
  font-weight: 500;
}

/* Nagłówek artykułu */
.article-header {
  margin-bottom: 2rem;
}

.article-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.article-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1.5rem;
  color: var(--text-light);
  font-size: 0.875rem;
}

.article-meta-item {
  display: flex;
  align-items: center;
}

.article-meta-icon {
  margin-right: 0.5rem;
}

.article-image {
  width: 100%;
  height: auto;
  border-radius: 0.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Treść artykułu */
.article-content {
  font-size: 1.125rem;
  line-height: 1.8;
  margin-bottom: 3rem;
}

.article-content h2 {
  font-size: 1.75rem;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.article-content h3 {
  font-size: 1.5rem;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
}

.article-content p {
  margin-bottom: 1.5rem;
}

.article-content blockquote {
  border-left: 4px solid var(--primary-light);
  padding: 1rem 1.5rem;
  margin: 1.5rem 0;
  background-color: rgba(40, 185, 178, 0.05);
  font-style: italic;
}

/* Tagi */
.tags-section {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding-top: 2rem;
  margin-bottom: 3rem;
}

/* Media społecznościowe */
.social-share {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding-top: 2rem;
  margin-bottom: 3rem;
}

.social-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  color: var(--white);
  font-weight: 500;
  transition: background-color 0.2s;
}

.social-btn-twitter {
  background-color: #1DA1F2;
}

.social-btn-twitter:hover {
  background-color: #0c85d0;
  color: var(--white);
  text-decoration: none;
}

.social-btn-linkedin {
  background-color: #0077B5;
}

.social-btn-linkedin:hover {
  background-color: #005582;
  color: var(--white);
  text-decoration: none;
}

/* Zakładki treści */
.content-tabs .nav-tabs {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.content-tabs .nav-link {
  border: none;
  color: var(--text-light);
  font-weight: 500;
  padding: 1rem 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.content-tabs .nav-link.active {
  color: var(--primary);
  border-bottom: 2px solid var(--primary);
}

.content-tabs .tab-content {
  padding-top: 2rem;
}

/* Podobne artykuły */
.related-articles {
  background-color: var(--white);
  padding: 3rem 0;
}

.related-article-card {
  transition: transform 0.3s;
}

.related-article-card:hover {
  transform: translateY(-5px);
}

.related-article-image {
  border-radius: 0.5rem;
  margin-bottom: 1rem;
  transition: opacity 0.2s;
}

.related-article-card:hover .related-article-image {
  opacity: 0.9;
}

.related-article-title {
  font-weight: 600;
  margin-bottom: 0.5rem;
  transition: color 0.2s;
}

.related-article-card:hover .related-article-title {
  color: var(--primary);
}

.related-article-date {
  color: var(--text-light);
  font-size: 0.875rem;
}

/* Responsywność */
@media (max-width: 768px) {
  .article-title {
    font-size: 2rem;
  }
  
  .article-meta {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .content-tabs .nav-link {
    padding: 0.75rem;
  }
} 