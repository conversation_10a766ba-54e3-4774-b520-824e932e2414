/**
 * Główny plik JavaScript dla projektu Innowacyjna Medycyna
 * Zawiera funkcje używane w całym projekcie
 */

document.addEventListener('DOMContentLoaded', function() {
    // Inicjalizacja tooltipów Bootstrap
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Inicjalizacja popovers Bootstrap
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // Obsługa aktywnych linków w nawigacji
    markActiveNavLink();
    
    // Obsługa przewijania do góry strony
    setupScrollToTop();
});

/**
 * Funkcja oznaczająca aktywny link w nawigacji na podstawie aktualnej strony
 */
function markActiveNavLink() {
    // Pobierz aktualną ścieżkę
    var currentPath = window.location.pathname;
    var filename = currentPath.split('/').pop();
    
    // Jeśli nie ma nazwy pliku, to jesteśmy na stronie głównej
    if (!filename || filename === '') {
        filename = 'index.html';
    }
    
    // Znajdź i oznacz aktywny link
    var navLinks = document.querySelectorAll('.navbar-nav .nav-link');
    navLinks.forEach(function(link) {
        if (link.getAttribute('href') === filename) {
            link.classList.add('active');
        } else {
            link.classList.remove('active');
        }
    });
}

/**
 * Funkcja ustawiająca przycisk przewijania do góry strony
 */
function setupScrollToTop() {
    // Dodaj przycisk do DOM, jeśli nie istnieje
    if (!document.getElementById('scrollToTopBtn')) {
        var button = document.createElement('button');
        button.id = 'scrollToTopBtn';
        button.innerHTML = '<i class="fas fa-arrow-up"></i>';
        button.className = 'scroll-to-top-btn';
        document.body.appendChild(button);
        
        // Dodaj style CSS dla przycisku
        var style = document.createElement('style');
        style.textContent = `
            .scroll-to-top-btn {
                position: fixed;
                bottom: 20px;
                right: 20px;
                width: 40px;
                height: 40px;
                border-radius: 50%;
                background-color: var(--primary);
                color: white;
                border: none;
                display: none;
                justify-content: center;
                align-items: center;
                cursor: pointer;
                z-index: 1000;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
                transition: background-color 0.3s, transform 0.3s;
            }
            
            .scroll-to-top-btn:hover {
                background-color: var(--primary-light);
                transform: translateY(-3px);
            }
            
            .scroll-to-top-btn.show {
                display: flex;
            }
        `;
        document.head.appendChild(style);
    }
    
    // Pobierz przycisk
    var scrollToTopBtn = document.getElementById('scrollToTopBtn');
    
    // Funkcja pokazująca/ukrywająca przycisk w zależności od pozycji przewijania
    function toggleScrollToTopBtn() {
        if (document.body.scrollTop > 300 || document.documentElement.scrollTop > 300) {
            scrollToTopBtn.classList.add('show');
        } else {
            scrollToTopBtn.classList.remove('show');
        }
    }
    
    // Funkcja przewijająca do góry strony
    function scrollToTop() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    }
    
    // Dodaj nasłuchiwanie zdarzeń
    window.addEventListener('scroll', toggleScrollToTopBtn);
    scrollToTopBtn.addEventListener('click', scrollToTop);
}

/**
 * Funkcja do formatowania daty w formacie polskim
 * @param {string} dateString - Data w formacie YYYY-MM-DD
 * @return {string} - Sformatowana data (np. 15.06.2024)
 */
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('pl-PL');
}

/**
 * Funkcja do pobierania parametrów z adresu URL
 * @param {string} name - Nazwa parametru
 * @return {string|null} - Wartość parametru lub null
 */
function getUrlParameter(name) {
    name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
    var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
    var results = regex.exec(location.search);
    return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
} 