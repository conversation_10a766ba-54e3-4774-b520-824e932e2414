<?php
// Ustawienia dla strony kategorii
$page_css = 'articles.css';
$current_page = 'category';
?>

<!-- Breadcrumb -->
<nav class="breadcrumb-custom" aria-label="breadcrumb">
    <div class="container">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?= $base_url ?>">Strona główna</a></li>
            <li class="breadcrumb-item"><a href="<?= $base_url ?>/artykuly">Artykuły</a></li>
            <li class="breadcrumb-item active" aria-current="page"><?= htmlspecialchars($category['name']) ?></li>
        </ol>
    </div>
</nav>

<!-- Nagłówek kategorii -->
<section class="category-header" style="background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 50%, var(--secondary) 100%); color: var(--white); padding: 4rem 0;">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h1 class="display-4 fw-bold mb-3"><?= htmlspecialchars($category['name']) ?></h1>
                <?php if (!empty($category['description'])): ?>
                    <p class="lead" style="color: var(--light);">
                        <?= htmlspecialchars($category['description']) ?>
                    </p>
                <?php endif; ?>
                <div class="category-meta">
                    <span class="badge bg-light text-dark">
                        <i class="fas fa-file-alt me-1"></i>
                        <?= count($articles ?? []) ?> artykułów
                    </span>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Filtry -->
<section class="filters-section">
    <div class="container">
        <div class="row align-items-center">
            <!-- Wyszukiwarka -->
            <div class="col-lg-6">
                <div class="search-container">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" id="searchInput" class="form-control search-input"
                        placeholder="Szukaj w kategorii <?= htmlspecialchars($category['name']) ?>..."
                        value="<?= htmlspecialchars($search_term ?? '') ?>">
                </div>
            </div>

            <!-- Sortowanie -->
            <div class="col-lg-6">
                <div class="d-flex justify-content-lg-end">
                    <div class="d-flex align-items-center">
                        <label for="sortSelect" class="form-label me-2 mb-0">Sortuj:</label>
                        <select class="form-select" id="sortSelect" style="width: auto;">
                            <option value="newest" <?= ($sort_by ?? 'newest') === 'newest' ? 'selected' : '' ?>>Najnowsze</option>
                            <option value="oldest" <?= ($sort_by ?? '') === 'oldest' ? 'selected' : '' ?>>Najstarsze</option>
                            <option value="title" <?= ($sort_by ?? '') === 'title' ? 'selected' : '' ?>>Tytuł A-Z</option>
                            <option value="popular" <?= ($sort_by ?? '') === 'popular' ? 'selected' : '' ?>>Najpopularniejsze</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Lista artykułów -->
<section class="section">
    <div class="container">
        <!-- Informacja o wynikach -->
        <div class="mb-4" id="resultsInfo">
            <p class="text-muted">
                Znaleziono <?= count($articles ?? []) ?> artykułów w kategorii "<?= htmlspecialchars($category['name']) ?>"
                <?php if (!empty($search_term)): ?>
                    dla frazy "<?= htmlspecialchars($search_term) ?>"
                <?php endif; ?>
            </p>
        </div>

        <!-- Siatka artykułów -->
        <div class="row g-4" id="articlesContainer">
            <?php if (!empty($articles)): ?>
                <?php foreach ($articles as $article): ?>
                    <div class="col-md-6 col-lg-4">
                        <div class="card news-card h-100">
                            <div class="card-img-container">
                                <?php if (!empty($article['featured_image'])): ?>
                                    <img src="<?= $article['featured_image'] ?>"
                                        class="card-img-top" alt="<?= htmlspecialchars($article['title']) ?>">
                                <?php else: ?>
                                    <img src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=400&h=300&fit=crop"
                                        class="card-img-top" alt="<?= htmlspecialchars($article['title']) ?>">
                                <?php endif; ?>
                                <div class="badge-container">
                                    <span class="badge badge-category"><?= htmlspecialchars($category['name']) ?></span>
                                </div>
                                <div class="type-badge">
                                    <span class="badge badge-type">
                                        <i class="far fa-file-alt me-1"></i> Artykuł
                                    </span>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="card-meta">
                                    <span><?= date('d.m.Y', strtotime($article['created_at'])) ?></span>
                                    <span class="card-meta-divider">•</span>
                                    <span><?= ceil(str_word_count(strip_tags($article['content'])) / 200) ?> min czytania</span>
                                    <?php if (!empty($article['views'])): ?>
                                        <span class="card-meta-divider">•</span>
                                        <span><?= $article['views'] ?> wyświetleń</span>
                                    <?php endif; ?>
                                </div>
                                <h5 class="card-title"><?= htmlspecialchars($article['title']) ?></h5>
                                <p class="card-text">
                                    <?= htmlspecialchars(substr(strip_tags($article['excerpt'] ?: $article['content']), 0, 120)) ?>...
                                </p>
                                <?php if (!empty($article['tags'])): ?>
                                    <div class="card-tags mb-2">
                                        <?php foreach (array_slice($article['tags'], 0, 3) as $tag): ?>
                                            <span class="badge bg-light text-dark me-1">
                                                <?= htmlspecialchars($tag['name']) ?>
                                            </span>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                                <a href="<?= $base_url ?>/artykuly/<?= htmlspecialchars($article['slug']) ?>" class="card-link">
                                    Czytaj więcej <i class="fas fa-arrow-right card-link-icon"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <!-- Brak artykułów -->
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h3 class="text-muted">Brak artykułów</h3>
                        <p class="text-muted">
                            <?php if (!empty($search_term)): ?>
                                Nie znaleziono artykułów w tej kategorii dla frazy "<?= htmlspecialchars($search_term) ?>".
                            <?php else: ?>
                                Ta kategoria nie zawiera jeszcze żadnych artykułów.
                            <?php endif; ?>
                        </p>
                        <?php if (!empty($search_term)): ?>
                            <a href="<?= $base_url ?>/kategoria/<?= htmlspecialchars($category['slug']) ?>" class="btn btn-primary-custom">
                                Pokaż wszystkie artykuły w kategorii
                            </a>
                        <?php else: ?>
                            <a href="<?= $base_url ?>/artykuly" class="btn btn-primary-custom">
                                Przeglądaj wszystkie artykuły
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Paginacja -->
        <?php if (!empty($pagination) && $pagination['total_pages'] > 1): ?>
            <nav aria-label="Nawigacja po stronach" class="mt-5">
                <ul class="pagination justify-content-center">
                    <?php if ($pagination['current_page'] > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="<?= $base_url ?>/kategoria/<?= htmlspecialchars($category['slug']) ?>?strona=<?= $pagination['current_page'] - 1 ?><?= !empty($search_term) ? '&szukaj=' . urlencode($search_term) : '' ?><?= !empty($sort_by) ? '&sortuj=' . urlencode($sort_by) : '' ?>">
                                <i class="fas fa-chevron-left"></i> Poprzednia
                            </a>
                        </li>
                    <?php endif; ?>

                    <?php for ($i = max(1, $pagination['current_page'] - 2); $i <= min($pagination['total_pages'], $pagination['current_page'] + 2); $i++): ?>
                        <li class="page-item <?= ($i === $pagination['current_page']) ? 'active' : '' ?>">
                            <a class="page-link" href="<?= $base_url ?>/kategoria/<?= htmlspecialchars($category['slug']) ?>?strona=<?= $i ?><?= !empty($search_term) ? '&szukaj=' . urlencode($search_term) : '' ?><?= !empty($sort_by) ? '&sortuj=' . urlencode($sort_by) : '' ?>">
                                <?= $i ?>
                            </a>
                        </li>
                    <?php endfor; ?>

                    <?php if ($pagination['current_page'] < $pagination['total_pages']): ?>
                        <li class="page-item">
                            <a class="page-link" href="<?= $base_url ?>/kategoria/<?= htmlspecialchars($category['slug']) ?>?strona=<?= $pagination['current_page'] + 1 ?><?= !empty($search_term) ? '&szukaj=' . urlencode($search_term) : '' ?><?= !empty($sort_by) ? '&sortuj=' . urlencode($sort_by) : '' ?>">
                                Następna <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
        <?php endif; ?>
    </div>
</section>

<!-- Inne kategorie -->
<?php if (!empty($other_categories)): ?>
    <section class="section section-light">
        <div class="container">
            <h2 class="h3 mb-4 text-center">Inne kategorie</h2>
            <div class="row g-3">
                <?php foreach ($other_categories as $other_category): ?>
                    <div class="col-md-6 col-lg-3">
                        <a href="<?= $base_url ?>/kategoria/<?= htmlspecialchars($other_category['slug']) ?>"
                            class="category-card text-decoration-none">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <h5 class="card-title"><?= htmlspecialchars($other_category['name']) ?></h5>
                                    <p class="card-text text-muted">
                                        <?= $other_category['article_count'] ?? 0 ?> artykułów
                                    </p>
                                </div>
                            </div>
                        </a>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
<?php endif; ?>

<!-- Style dla kategorii -->
<style>
    .category-header {
        margin-bottom: 0;
    }

    .category-meta .badge {
        font-size: 0.875rem;
    }

    .card-tags {
        font-size: 0.875rem;
    }

    .category-card:hover .card {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
    }

    .category-card .card {
        transition: transform 0.3s, box-shadow 0.3s;
    }

    .breadcrumb-custom {
        background: #f8f9fa;
        padding: 1rem 0;
    }

    .filters-section {
        background: #f8f9fa;
        padding: 2rem 0;
    }

    .search-container {
        position: relative;
    }

    .search-icon {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
    }

    .search-input {
        padding-left: 3rem;
    }
</style>

<!-- JavaScript dla kategorii -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('searchInput');
        const sortSelect = document.getElementById('sortSelect');

        // Obsługa wyszukiwania
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(function() {
                performSearch();
            }, 500);
        });

        // Obsługa sortowania
        sortSelect.addEventListener('change', function() {
            performSearch();
        });

        function performSearch() {
            const searchTerm = searchInput.value.trim();
            const sortBy = sortSelect.value;

            let url = '<?= $base_url ?>/kategoria/<?= htmlspecialchars($category['slug']) ?>?';
            const params = [];

            if (searchTerm) {
                params.push('szukaj=' + encodeURIComponent(searchTerm));
            }

            if (sortBy && sortBy !== 'newest') {
                params.push('sortuj=' + encodeURIComponent(sortBy));
            }

            if (params.length > 0) {
                url += params.join('&');
            }

            window.location.href = url;
        }
    });
</script>