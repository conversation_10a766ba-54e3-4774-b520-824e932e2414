<?php
// Ustawienia dla strony kategorii galerii
$current_page = 'gallery';

// Rozpocznij buforowanie wyjścia
ob_start();
?>

<!-- Breadcrumb -->
<nav class="breadcrumb-custom" aria-label="breadcrumb">
    <div class="container">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?= $base_url ?>">Strona główna</a></li>
            <li class="breadcrumb-item"><a href="<?= $base_url ?>/galeria">Galeria</a></li>
            <li class="breadcrumb-item active" aria-current="page"><?= htmlspecialchars($category['name']) ?></li>
        </ol>
    </div>
</nav>

<!-- Nagłówek kategorii -->
<section class="category-header" style="background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 50%, var(--secondary) 100%); color: var(--white); padding: 3rem 0;">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="display-5 fw-bold mb-3"><?= htmlspecialchars($category['name']) ?></h1>
                <?php if (!empty($category['description'])): ?>
                    <p class="lead" style="color: var(--light);">
                        <?= htmlspecialchars($category['description']) ?>
                    </p>
                <?php endif; ?>
                <div class="category-meta">
                    <span class="badge bg-light text-dark me-2">
                        <i class="fas fa-images me-1"></i>
                        <?= count($images ?? []) ?> zdjęć
                    </span>
                    <?php if (!empty($category['updated_at'])): ?>
                        <span class="badge bg-light text-dark">
                            <i class="fas fa-calendar me-1"></i>
                            Ostatnia aktualizacja: <?= date('d.m.Y', strtotime($category['updated_at'])) ?>
                        </span>
                    <?php endif; ?>
                </div>
            </div>
            <div class="col-md-4 text-md-end">
                <a href="<?= $base_url ?>/galeria" class="btn btn-outline-light">
                    <i class="fas fa-arrow-left me-2"></i>Powrót do galerii
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Galeria zdjęć -->
<section class="section">
    <div class="container">
        <?php if (!empty($images)): ?>
            <!-- Filtry sortowania -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="d-flex align-items-center">
                        <label for="sortSelect" class="form-label me-2 mb-0">Sortuj:</label>
                        <select class="form-select form-select-sm" id="sortSelect" style="width: auto;">
                            <option value="newest">Najnowsze</option>
                            <option value="oldest">Najstarsze</option>
                            <option value="name">Nazwa A-Z</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="btn-group" role="group" aria-label="Widok galerii">
                        <button type="button" class="btn btn-outline-secondary btn-sm active" id="gridView">
                            <i class="fas fa-th"></i>
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" id="listView">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Siatka zdjęć -->
            <div class="row g-3" id="imageGrid">
                <?php foreach ($images as $image): ?>
                    <div class="col-6 col-md-4 col-lg-3 image-item"
                        data-date="<?= $image['created_at'] ?>"
                        data-name="<?= htmlspecialchars($image['filename']) ?>">
                        <div class="gallery-image-item">
                            <a href="<?= $image['url'] ?>"
                                data-bs-toggle="modal"
                                data-bs-target="#imageModal"
                                data-image-url="<?= $image['url'] ?>"
                                data-image-title="<?= htmlspecialchars($image['title'] ?? $image['filename']) ?>"
                                data-image-description="<?= htmlspecialchars($image['description'] ?? '') ?>">
                                <img src="<?= $image['thumbnail'] ?? $image['url'] ?>"
                                    alt="<?= htmlspecialchars($image['title'] ?? $image['filename']) ?>"
                                    class="img-fluid gallery-thumbnail">
                                <div class="image-overlay">
                                    <i class="fas fa-search-plus"></i>
                                </div>
                            </a>
                            <?php if (!empty($image['title'])): ?>
                                <div class="image-title">
                                    <?= htmlspecialchars($image['title']) ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- Paginacja -->
            <?php if (!empty($pagination) && $pagination['total_pages'] > 1): ?>
                <nav aria-label="Nawigacja po stronach galerii" class="mt-5">
                    <ul class="pagination justify-content-center">
                        <?php if ($pagination['current_page'] > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="<?= $base_url ?>/galeria/<?= htmlspecialchars($category['slug']) ?>?strona=<?= $pagination['current_page'] - 1 ?>">
                                    <i class="fas fa-chevron-left"></i> Poprzednia
                                </a>
                            </li>
                        <?php endif; ?>

                        <?php for ($i = max(1, $pagination['current_page'] - 2); $i <= min($pagination['total_pages'], $pagination['current_page'] + 2); $i++): ?>
                            <li class="page-item <?= ($i === $pagination['current_page']) ? 'active' : '' ?>">
                                <a class="page-link" href="<?= $base_url ?>/galeria/<?= htmlspecialchars($category['slug']) ?>?strona=<?= $i ?>">
                                    <?= $i ?>
                                </a>
                            </li>
                        <?php endfor; ?>

                        <?php if ($pagination['current_page'] < $pagination['total_pages']): ?>
                            <li class="page-item">
                                <a class="page-link" href="<?= $base_url ?>/galeria/<?= htmlspecialchars($category['slug']) ?>?strona=<?= $pagination['current_page'] + 1 ?>">
                                    Następna <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            <?php endif; ?>

        <?php else: ?>
            <!-- Brak zdjęć -->
            <div class="text-center py-5">
                <i class="fas fa-images fa-3x text-muted mb-3"></i>
                <h3 class="text-muted">Brak zdjęć</h3>
                <p class="text-muted">Ta kategoria nie zawiera jeszcze żadnych zdjęć.</p>
                <a href="<?= $base_url ?>/galeria" class="btn btn-primary-custom">
                    Powrót do galerii
                </a>
            </div>
        <?php endif; ?>
    </div>
</section>

<!-- Modal do wyświetlania zdjęć -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalLabel">Zdjęcie</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Zamknij"></button>
            </div>
            <div class="modal-body text-center">
                <img src="" alt="" class="img-fluid" id="modalImage">
                <p class="mt-3 text-muted" id="modalDescription"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Zamknij</button>
                <a href="" class="btn btn-primary-custom" id="downloadImage" download>
                    <i class="fas fa-download me-2"></i>Pobierz
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Style dla galerii -->
<style>
    .gallery-image-item {
        position: relative;
        overflow: hidden;
        border-radius: 0.5rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s, box-shadow 0.3s;
    }

    .gallery-image-item:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    }

    .gallery-thumbnail {
        width: 100%;
        height: 200px;
        object-fit: cover;
        transition: transform 0.3s;
    }

    .gallery-image-item:hover .gallery-thumbnail {
        transform: scale(1.05);
    }

    .image-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s;
        color: white;
        font-size: 1.5rem;
    }

    .gallery-image-item:hover .image-overlay {
        opacity: 1;
    }

    .image-title {
        padding: 0.5rem;
        font-size: 0.875rem;
        font-weight: 500;
        text-align: center;
        background: white;
        border-top: 1px solid #eee;
    }

    .breadcrumb-custom {
        background: #f8f9fa;
        padding: 1rem 0;
    }

    .category-header {
        margin-bottom: 0;
    }

    .category-meta .badge {
        font-size: 0.875rem;
    }
</style>

<!-- JavaScript dla galerii -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Modal dla zdjęć
        const imageModal = document.getElementById('imageModal');
        if (imageModal) {
            imageModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const imageUrl = button.getAttribute('data-image-url');
                const imageTitle = button.getAttribute('data-image-title');
                const imageDescription = button.getAttribute('data-image-description');

                const modalImage = document.getElementById('modalImage');
                const modalTitle = document.getElementById('imageModalLabel');
                const modalDescription = document.getElementById('modalDescription');
                const downloadLink = document.getElementById('downloadImage');

                modalImage.src = imageUrl;
                modalImage.alt = imageTitle;
                modalTitle.textContent = imageTitle;
                modalDescription.textContent = imageDescription;
                downloadLink.href = imageUrl;
            });
        }

        // Sortowanie
        const sortSelect = document.getElementById('sortSelect');
        if (sortSelect) {
            sortSelect.addEventListener('change', function() {
                const sortBy = this.value;
                const container = document.getElementById('imageGrid');
                const items = Array.from(container.children);

                items.sort((a, b) => {
                    if (sortBy === 'newest') {
                        return new Date(b.dataset.date) - new Date(a.dataset.date);
                    } else if (sortBy === 'oldest') {
                        return new Date(a.dataset.date) - new Date(b.dataset.date);
                    } else if (sortBy === 'name') {
                        return a.dataset.name.localeCompare(b.dataset.name);
                    }
                });

                items.forEach(item => container.appendChild(item));
            });
        }
    });
</script>

<?php
// Pobierz zawartość bufora
$content = ob_get_clean();

// Załaduj layout bezpośrednio
extract([
    'title' => htmlspecialchars($category['name']) . ' - Galeria - Innowacyjna Medycyna',
    'meta_description' => htmlspecialchars($category['description'] ?? 'Galeria zdjęć z kategorii ' . $category['name']),
    'content' => $content,
    'current_page' => $current_page
]);

include __DIR__ . '/layout.php';
?>