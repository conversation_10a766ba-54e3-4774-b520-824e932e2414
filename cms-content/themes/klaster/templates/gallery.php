<?php
// Ustawienia dla strony galerii
$current_page = 'gallery';

// Rozpocznij buforowanie wyjścia
ob_start();
?>

<!-- Nagłówek strony -->
<section class="gallery-header" style="background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 50%, var(--secondary) 100%); color: var(--white); padding: 4rem 0;">
    <div class="container">
        <h1 class="display-4 fw-bold">Galeria</h1>
        <p class="lead mx-auto" style="max-width: 700px; color: var(--light);">
            Poznaj nasze projekty, realizacje i wydarzenia w obrazach
        </p>
    </div>
</section>

<!-- Kategorie galerii -->
<section class="section">
    <div class="container">
        <?php if (!empty($gallery_categories)): ?>
            <div class="row g-4">
                <?php foreach ($gallery_categories as $category): ?>
                    <div class="col-md-6 col-lg-4">
                        <div class="gallery-category-card">
                            <a href="<?= $base_url ?>/galeria/<?= htmlspecialchars($category['slug']) ?>" class="text-decoration-none">
                                <div class="gallery-item">
                                    <?php if (!empty($category['featured_image'])): ?>
                                        <img src="<?= $category['featured_image'] ?>"
                                            alt="<?= htmlspecialchars($category['name']) ?>"
                                            class="gallery-image">
                                    <?php else: ?>
                                        <img src="https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=400&h=300&fit=crop"
                                            alt="<?= htmlspecialchars($category['name']) ?>"
                                            class="gallery-image">
                                    <?php endif; ?>

                                    <div class="gallery-overlay">
                                        <div class="gallery-title"><?= htmlspecialchars($category['name']) ?></div>
                                        <div class="gallery-date">
                                            <?= $category['image_count'] ?? 0 ?> zdjęć
                                            <?php if (!empty($category['updated_at'])): ?>
                                                • <?= date('d.m.Y', strtotime($category['updated_at'])) ?>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <?php if (!empty($category['is_new'])): ?>
                                        <div class="gallery-badge">Nowe</div>
                                    <?php endif; ?>
                                </div>
                            </a>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <!-- Brak kategorii -->
            <div class="text-center py-5">
                <i class="fas fa-images fa-3x text-muted mb-3"></i>
                <h3 class="text-muted">Brak galerii</h3>
                <p class="text-muted">Nie ma jeszcze żadnych kategorii galerii.</p>
            </div>
        <?php endif; ?>
    </div>
</section>

<!-- Style dla galerii -->
<style>
    .gallery-category-card {
        height: 100%;
    }

    .gallery-item {
        position: relative;
        overflow: hidden;
        border-radius: 0.5rem;
        height: 250px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s, box-shadow 0.3s;
    }

    .gallery-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .gallery-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s;
    }

    .gallery-item:hover .gallery-image {
        transform: scale(1.05);
    }

    .gallery-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
        color: white;
        padding: 1rem;
        transform: translateY(100%);
        transition: transform 0.3s;
    }

    .gallery-item:hover .gallery-overlay {
        transform: translateY(0);
    }

    .gallery-title {
        font-weight: 600;
        margin-bottom: 0.25rem;
        font-size: 1.1rem;
    }

    .gallery-date {
        font-size: 0.875rem;
        opacity: 0.8;
    }

    .gallery-badge {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background-color: var(--primary);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 50rem;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .gallery-header {
        margin-bottom: 0;
    }

    .section {
        padding: 4rem 0;
    }
</style>

<?php
// Pobierz zawartość bufora
$content = ob_get_clean();

// Załaduj layout bezpośrednio
extract([
    'title' => 'Galeria - Innowacyjna Medycyna',
    'meta_description' => 'Poznaj nasze projekty, realizacje i wydarzenia w obrazach',
    'content' => $content,
    'current_page' => $current_page
]);

include __DIR__ . '/layout.php';
?>