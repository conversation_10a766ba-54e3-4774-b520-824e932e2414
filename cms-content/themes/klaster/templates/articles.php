<?php
// Ustawienia dla strony artykułów
$page_css = 'articles.css';
$current_page = 'articles';

// Rozpocznij buforowanie wyjścia
ob_start();
?>

<!-- Nagłówek strony -->
<section class="articles-header" style="background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 50%, var(--secondary) 100%); color: var(--white); padding: 4rem 0;">
    <div class="container">
        <h1 class="display-4 fw-bold">Artykuły medyczne</h1>
        <p class="lead mx-auto" style="max-width: 700px; color: var(--light);">
            Najn<PERSON>ze publikacje, badania i analizy ze świata medycyny i technologii zdrowotnych
        </p>
    </div>
</section>

<!-- Filtry -->
<section class="filters-section">
    <div class="container">
        <div class="row align-items-center">
            <!-- Wyszukiwarka -->
            <div class="col-lg-4">
                <div class="search-container">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" id="searchInput" class="form-control search-input"
                        placeholder="Szukaj artykułów..." value="<?= htmlspecialchars($search_term ?? '') ?>">
                </div>
            </div>

            <!-- Kategorie -->
            <div class="col-lg-8">
                <div class="category-filter" id="categoryFilter">
                    <button class="btn category-btn <?= empty($selected_category) ? 'active' : '' ?>"
                        data-category="wszystkie">Wszystkie</button>
                    <?php if (!empty($categories)): ?>
                        <?php foreach ($categories as $category): ?>
                            <button class="btn category-btn <?= ($selected_category === $category['slug']) ? 'active' : '' ?>"
                                data-category="<?= htmlspecialchars($category['slug']) ?>">
                                <?= htmlspecialchars($category['name']) ?>
                            </button>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <!-- Fallback categories -->
                        <button class="btn category-btn" data-category="badania">Badania</button>
                        <button class="btn category-btn" data-category="technologia">Technologia</button>
                        <button class="btn category-btn" data-category="digital-health">Digital Health</button>
                        <button class="btn category-btn" data-category="farmakologia">Farmakologia</button>
                        <button class="btn category-btn" data-category="diagnostyka">Diagnostyka</button>
                        <button class="btn category-btn" data-category="terapie">Terapie</button>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Lista artykułów -->
<section class="section">
    <div class="container">
        <!-- Informacja o wynikach -->
        <div class="mb-4" id="resultsInfo">
            <p class="text-muted">
                Znaleziono <?= count($articles ?? []) ?> artykułów
                <?php if (!empty($selected_category)): ?>
                    w kategorii "<?= htmlspecialchars($selected_category_name ?? $selected_category) ?>"
                <?php endif; ?>
                <?php if (!empty($search_term)): ?>
                    dla frazy "<?= htmlspecialchars($search_term) ?>"
                <?php endif; ?>
            </p>
        </div>

        <!-- Siatka artykułów -->
        <div class="row g-4" id="articlesContainer">
            <?php if (!empty($articles)): ?>
                <?php foreach ($articles as $article): ?>
                    <div class="col-md-6 col-lg-4" data-category="<?= htmlspecialchars($article['category_slug'] ?? '') ?>">
                        <div class="card news-card h-100">
                            <div class="card-img-container">
                                <?php if (!empty($article['featured_image'])): ?>
                                    <img src="<?= $article['featured_image'] ?>"
                                        class="card-img-top" alt="<?= htmlspecialchars($article['title']) ?>">
                                <?php else: ?>
                                    <img src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=400&h=300&fit=crop"
                                        class="card-img-top" alt="<?= htmlspecialchars($article['title']) ?>">
                                <?php endif; ?>
                                <div class="badge-container">
                                    <span class="badge badge-category"><?= htmlspecialchars($article['category_name'] ?? 'Artykuł') ?></span>
                                </div>
                                <div class="type-badge">
                                    <span class="badge badge-type">
                                        <i class="far fa-file-alt me-1"></i> Artykuł
                                    </span>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="card-meta">
                                    <span><?= date('d.m.Y', strtotime($article['created_at'])) ?></span>
                                    <span class="card-meta-divider">•</span>
                                    <span><?= ceil(str_word_count(strip_tags($article['content'])) / 200) ?> min czytania</span>
                                </div>
                                <h5 class="card-title"><?= htmlspecialchars($article['title']) ?></h5>
                                <p class="card-text">
                                    <?= htmlspecialchars(substr(strip_tags($article['excerpt'] ?: $article['content']), 0, 120)) ?>...
                                </p>
                                <a href="<?= $base_url ?>/artykuly/<?= htmlspecialchars($article['slug']) ?>" class="card-link">
                                    Czytaj więcej <i class="fas fa-arrow-right card-link-icon"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <!-- Brak artykułów -->
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h3 class="text-muted">Brak artykułów</h3>
                        <p class="text-muted">
                            <?php if (!empty($search_term) || !empty($selected_category)): ?>
                                Nie znaleziono artykułów spełniających kryteria wyszukiwania.
                            <?php else: ?>
                                Nie ma jeszcze żadnych opublikowanych artykułów.
                            <?php endif; ?>
                        </p>
                        <?php if (!empty($search_term) || !empty($selected_category)): ?>
                            <a href="<?= $base_url ?>/artykuly" class="btn btn-primary-custom">
                                Pokaż wszystkie artykuły
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Paginacja -->
        <?php if (!empty($pagination) && $pagination['total_pages'] > 1): ?>
            <nav aria-label="Nawigacja po stronach" class="mt-5">
                <ul class="pagination justify-content-center">
                    <?php if ($pagination['current_page'] > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="<?= $base_url ?>/artykuly?strona=<?= $pagination['current_page'] - 1 ?><?= !empty($search_term) ? '&szukaj=' . urlencode($search_term) : '' ?><?= !empty($selected_category) ? '&kategoria=' . urlencode($selected_category) : '' ?>">
                                <i class="fas fa-chevron-left"></i> Poprzednia
                            </a>
                        </li>
                    <?php endif; ?>

                    <?php for ($i = max(1, $pagination['current_page'] - 2); $i <= min($pagination['total_pages'], $pagination['current_page'] + 2); $i++): ?>
                        <li class="page-item <?= ($i === $pagination['current_page']) ? 'active' : '' ?>">
                            <a class="page-link" href="<?= $base_url ?>/artykuly?strona=<?= $i ?><?= !empty($search_term) ? '&szukaj=' . urlencode($search_term) : '' ?><?= !empty($selected_category) ? '&kategoria=' . urlencode($selected_category) : '' ?>">
                                <?= $i ?>
                            </a>
                        </li>
                    <?php endfor; ?>

                    <?php if ($pagination['current_page'] < $pagination['total_pages']): ?>
                        <li class="page-item">
                            <a class="page-link" href="<?= $base_url ?>/artykuly?strona=<?= $pagination['current_page'] + 1 ?><?= !empty($search_term) ? '&szukaj=' . urlencode($search_term) : '' ?><?= !empty($selected_category) ? '&kategoria=' . urlencode($selected_category) : '' ?>">
                                Następna <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
        <?php endif; ?>
    </div>
</section>

<!-- JavaScript dla filtrowania -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('searchInput');
        const categoryButtons = document.querySelectorAll('.category-btn');

        // Obsługa wyszukiwania
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(function() {
                performSearch();
            }, 500);
        });

        // Obsługa filtrowania kategorii
        categoryButtons.forEach(button => {
            button.addEventListener('click', function() {
                categoryButtons.forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');
                performSearch();
            });
        });

        function performSearch() {
            const searchTerm = searchInput.value.trim();
            const activeCategory = document.querySelector('.category-btn.active').dataset.category;

            let url = '<?= $base_url ?>/artykuly?';
            const params = [];

            if (searchTerm) {
                params.push('szukaj=' + encodeURIComponent(searchTerm));
            }

            if (activeCategory && activeCategory !== 'wszystkie') {
                params.push('kategoria=' + encodeURIComponent(activeCategory));
            }

            if (params.length > 0) {
                url += params.join('&');
            }

            window.location.href = url;
        }
    });
</script>

<?php
// Pobierz zawartość bufora
$content = ob_get_clean();

// Załaduj layout bezpośrednio
extract([
    'title' => 'Artykuły medyczne - Innowacyjna Medycyna',
    'meta_description' => 'Najnowsze publikacje, badania i analizy ze świata medycyny i technologii zdrowotnych',
    'content' => $content,
    'page_css' => $page_css,
    'current_page' => $current_page
]);

include __DIR__ . '/layout.php';
?>