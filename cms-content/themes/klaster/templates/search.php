<?php
// Ustawienia dla strony wyszukiwania
$page_css = 'articles.css';
$current_page = 'search';

// Rozpocznij buforowanie wyjścia
ob_start();
?>

<!-- Nagłówek wyszukiwania -->
<section class="search-header" style="background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 50%, var(--secondary) 100%); color: var(--white); padding: 4rem 0;">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h1 class="display-4 fw-bold mb-3">Wyniki wyszukiwania</h1>
                <?php if (!empty($search_term)): ?>
                    <p class="lead" style="color: var(--light);">
                        Wyniki dla frazy: "<strong><?= htmlspecialchars($search_term) ?></strong>"
                    </p>
                <?php else: ?>
                    <p class="lead" style="color: var(--light);">
                        Wyszukaj artykuły, strony i inne treści
                    </p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<!-- Formularz wyszukiwania -->
<section class="search-form-section">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <form method="GET" action="<?= $base_url ?>/szukaj" class="search-form">
                    <div class="input-group input-group-lg">
                        <input type="text"
                            name="q"
                            class="form-control"
                            placeholder="Wpisz szukaną frazę..."
                            value="<?= htmlspecialchars($search_term ?? '') ?>"
                            required>
                        <button class="btn btn-primary-custom" type="submit">
                            <i class="fas fa-search me-2"></i>Szukaj
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- Wyniki wyszukiwania -->
<?php if (!empty($search_term)): ?>
    <section class="section">
        <div class="container">
            <!-- Informacja o wynikach -->
            <div class="mb-4">
                <p class="text-muted">
                    Znaleziono <?= $total_results ?? 0 ?> wyników dla frazy "<?= htmlspecialchars($search_term) ?>"
                    <?php if (!empty($search_time)): ?>
                        (<?= number_format($search_time, 3) ?> sekund)
                    <?php endif; ?>
                </p>
            </div>

            <!-- Filtry wyników -->
            <div class="search-filters mb-4">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="btn-group" role="group" aria-label="Typ treści">
                            <input type="radio" class="btn-check" name="content_type" id="all_content" value="all" <?= ($content_type ?? 'all') === 'all' ? 'checked' : '' ?>>
                            <label class="btn btn-outline-secondary" for="all_content">
                                Wszystko (<?= $total_results ?? 0 ?>)
                            </label>

                            <input type="radio" class="btn-check" name="content_type" id="articles_only" value="articles" <?= ($content_type ?? '') === 'articles' ? 'checked' : '' ?>>
                            <label class="btn btn-outline-secondary" for="articles_only">
                                Artykuły (<?= $articles_count ?? 0 ?>)
                            </label>

                            <input type="radio" class="btn-check" name="content_type" id="pages_only" value="pages" <?= ($content_type ?? '') === 'pages' ? 'checked' : '' ?>>
                            <label class="btn btn-outline-secondary" for="pages_only">
                                Strony (<?= $pages_count ?? 0 ?>)
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <select class="form-select" id="sortSelect" style="width: auto; display: inline-block;">
                            <option value="relevance" <?= ($sort_by ?? 'relevance') === 'relevance' ? 'selected' : '' ?>>Trafność</option>
                            <option value="date" <?= ($sort_by ?? '') === 'date' ? 'selected' : '' ?>>Data</option>
                            <option value="title" <?= ($sort_by ?? '') === 'title' ? 'selected' : '' ?>>Tytuł</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Lista wyników -->
            <div class="search-results">
                <?php if (!empty($results)): ?>
                    <?php foreach ($results as $result): ?>
                        <div class="search-result-item">
                            <div class="result-header">
                                <h3 class="result-title">
                                    <a href="<?= $result['url'] ?>">
                                        <?= $result['highlighted_title'] ?? htmlspecialchars($result['title']) ?>
                                    </a>
                                </h3>
                                <div class="result-meta">
                                    <span class="result-type badge badge-<?= $result['type'] === 'article' ? 'primary' : 'secondary' ?>">
                                        <?= $result['type'] === 'article' ? 'Artykuł' : 'Strona' ?>
                                    </span>
                                    <?php if (!empty($result['category'])): ?>
                                        <span class="result-category">
                                            w <a href="<?= $base_url ?>/kategoria/<?= htmlspecialchars($result['category_slug']) ?>">
                                                <?= htmlspecialchars($result['category']) ?>
                                            </a>
                                        </span>
                                    <?php endif; ?>
                                    <span class="result-date">
                                        <?= date('d.m.Y', strtotime($result['date'])) ?>
                                    </span>
                                </div>
                            </div>
                            <div class="result-content">
                                <p><?= $result['highlighted_excerpt'] ?? htmlspecialchars(substr(strip_tags($result['content']), 0, 200)) ?>...</p>
                            </div>
                            <?php if (!empty($result['tags'])): ?>
                                <div class="result-tags">
                                    <?php foreach ($result['tags'] as $tag): ?>
                                        <span class="badge bg-light text-dark me-1">
                                            <?= htmlspecialchars($tag['name']) ?>
                                        </span>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>

                    <!-- Paginacja -->
                    <?php if (!empty($pagination) && $pagination['total_pages'] > 1): ?>
                        <nav aria-label="Nawigacja po wynikach wyszukiwania" class="mt-5">
                            <ul class="pagination justify-content-center">
                                <?php if ($pagination['current_page'] > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="<?= $base_url ?>/szukaj?q=<?= urlencode($search_term) ?>&strona=<?= $pagination['current_page'] - 1 ?><?= !empty($content_type) && $content_type !== 'all' ? '&typ=' . urlencode($content_type) : '' ?><?= !empty($sort_by) && $sort_by !== 'relevance' ? '&sortuj=' . urlencode($sort_by) : '' ?>">
                                            <i class="fas fa-chevron-left"></i> Poprzednia
                                        </a>
                                    </li>
                                <?php endif; ?>

                                <?php for ($i = max(1, $pagination['current_page'] - 2); $i <= min($pagination['total_pages'], $pagination['current_page'] + 2); $i++): ?>
                                    <li class="page-item <?= ($i === $pagination['current_page']) ? 'active' : '' ?>">
                                        <a class="page-link" href="<?= $base_url ?>/szukaj?q=<?= urlencode($search_term) ?>&strona=<?= $i ?><?= !empty($content_type) && $content_type !== 'all' ? '&typ=' . urlencode($content_type) : '' ?><?= !empty($sort_by) && $sort_by !== 'relevance' ? '&sortuj=' . urlencode($sort_by) : '' ?>">
                                            <?= $i ?>
                                        </a>
                                    </li>
                                <?php endfor; ?>

                                <?php if ($pagination['current_page'] < $pagination['total_pages']): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="<?= $base_url ?>/szukaj?q=<?= urlencode($search_term) ?>&strona=<?= $pagination['current_page'] + 1 ?><?= !empty($content_type) && $content_type !== 'all' ? '&typ=' . urlencode($content_type) : '' ?><?= !empty($sort_by) && $sort_by !== 'relevance' ? '&sortuj=' . urlencode($sort_by) : '' ?>">
                                            Następna <i class="fas fa-chevron-right"></i>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    <?php endif; ?>

                <?php else: ?>
                    <!-- Brak wyników -->
                    <div class="no-results text-center py-5">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h3 class="text-muted">Brak wyników</h3>
                        <p class="text-muted">
                            Nie znaleziono żadnych wyników dla frazy "<?= htmlspecialchars($search_term) ?>".
                        </p>
                        <div class="search-suggestions">
                            <h5>Spróbuj:</h5>
                            <ul class="list-unstyled">
                                <li>• Sprawdzić pisownię</li>
                                <li>• Użyć innych słów kluczowych</li>
                                <li>• Użyć bardziej ogólnych terminów</li>
                                <li>• Skrócić frazę wyszukiwania</li>
                            </ul>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Popularne wyszukiwania -->
    <?php if (!empty($popular_searches)): ?>
        <section class="section section-light">
            <div class="container">
                <h2 class="h4 mb-3">Popularne wyszukiwania</h2>
                <div class="popular-searches">
                    <?php foreach ($popular_searches as $popular): ?>
                        <a href="<?= $base_url ?>/szukaj?q=<?= urlencode($popular['term']) ?>"
                            class="btn btn-outline-secondary btn-sm me-2 mb-2">
                            <?= htmlspecialchars($popular['term']) ?>
                            <span class="badge bg-secondary ms-1"><?= $popular['count'] ?></span>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>
    <?php endif; ?>

<?php else: ?>
    <!-- Strona wyszukiwania bez frazy -->
    <section class="section">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h2>Wyszukaj treści</h2>
                    <p class="text-muted">Użyj formularza powyżej, aby wyszukać artykuły, strony i inne treści na naszej stronie.</p>
                </div>
            </div>
        </div>
    </section>
<?php endif; ?>

<!-- Style dla wyszukiwania -->
<style>
    .search-header {
        margin-bottom: 0;
    }

    .search-form-section {
        background: #f8f9fa;
        padding: 2rem 0;
    }

    .search-form .input-group {
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        border-radius: 0.5rem;
        overflow: hidden;
    }

    .search-result-item {
        padding: 1.5rem 0;
        border-bottom: 1px solid #e9ecef;
    }

    .search-result-item:last-child {
        border-bottom: none;
    }

    .result-title a {
        color: var(--primary);
        text-decoration: none;
        font-size: 1.25rem;
        font-weight: 600;
    }

    .result-title a:hover {
        color: var(--primary-light);
    }

    .result-meta {
        margin: 0.5rem 0;
        font-size: 0.875rem;
        color: #6c757d;
    }

    .result-meta .badge {
        font-size: 0.75rem;
    }

    .result-category a {
        color: var(--primary);
        text-decoration: none;
    }

    .result-content {
        margin: 1rem 0;
        line-height: 1.6;
    }

    .result-tags {
        margin-top: 0.5rem;
    }

    .search-filters {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 0.5rem;
    }

    .search-suggestions {
        margin-top: 2rem;
    }

    .search-suggestions ul {
        text-align: left;
        display: inline-block;
    }

    .popular-searches .btn {
        margin-bottom: 0.5rem;
    }

    /* Podświetlanie wyników */
    .highlight {
        background-color: #fff3cd;
        padding: 0.1rem 0.2rem;
        border-radius: 0.2rem;
        font-weight: 600;
    }
</style>

<!-- JavaScript dla wyszukiwania -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const contentTypeRadios = document.querySelectorAll('input[name="content_type"]');
        const sortSelect = document.getElementById('sortSelect');

        // Obsługa filtrów typu treści
        contentTypeRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                updateSearchResults();
            });
        });

        // Obsługa sortowania
        if (sortSelect) {
            sortSelect.addEventListener('change', function() {
                updateSearchResults();
            });
        }

        function updateSearchResults() {
            const searchTerm = '<?= htmlspecialchars($search_term ?? '') ?>';
            if (!searchTerm) return;

            const contentType = document.querySelector('input[name="content_type"]:checked').value;
            const sortBy = sortSelect ? sortSelect.value : 'relevance';

            let url = '<?= $base_url ?>/szukaj?q=' + encodeURIComponent(searchTerm);

            if (contentType && contentType !== 'all') {
                url += '&typ=' + encodeURIComponent(contentType);
            }

            if (sortBy && sortBy !== 'relevance') {
                url += '&sortuj=' + encodeURIComponent(sortBy);
            }

            window.location.href = url;
        }
    });
</script>

<?php
// Pobierz zawartość bufora
$content = ob_get_clean();

// Załaduj layout bezpośrednio
extract([
    'title' => 'Wyszukiwanie - Innowacyjna Medycyna',
    'meta_description' => 'Wyszukaj artykuły, strony i inne treści na naszej stronie',
    'content' => $content,
    'page_css' => $page_css,
    'current_page' => $current_page
]);

include __DIR__ . '/layout.php';
?>