<?php
// Ustawienia dla strony artykułu
$page_css = 'article.css';
$current_page = 'article';
?>

<!-- Breadcrumb -->
<nav class="breadcrumb-custom" aria-label="breadcrumb">
    <div class="container">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?= $base_url ?>">Strona główna</a></li>
            <li class="breadcrumb-item"><a href="<?= $base_url ?>/artykuly">Artykuły</a></li>
            <?php if (!empty($article['category_name'])): ?>
                <li class="breadcrumb-item">
                    <a href="<?= $base_url ?>/kategoria/<?= htmlspecialchars($article['category_slug']) ?>">
                        <?= htmlspecialchars($article['category_name']) ?>
                    </a>
                </li>
            <?php endif; ?>
            <li class="breadcrumb-item active" aria-current="page"><?= htmlspecialchars($article['title']) ?></li>
        </ol>
    </div>
</nav>

<!-- Treść artykułu -->
<article class="section">
    <div class="container">
        <!-- Nagłówek artykułu -->
        <header class="article-header">
            <?php if (!empty($article['category_name'])): ?>
                <div class="mb-4">
                    <span class="badge badge-category"><?= htmlspecialchars($article['category_name']) ?></span>
                </div>
            <?php endif; ?>

            <h1 class="article-title"><?= htmlspecialchars($article['title']) ?></h1>

            <div class="article-meta">
                <?php if (!empty($article['author'])): ?>
                    <div class="article-meta-item">
                        <i class="fas fa-user article-meta-icon"></i>
                        <?= htmlspecialchars($article['author']) ?>
                    </div>
                <?php endif; ?>
                <div class="article-meta-item">
                    <i class="fas fa-calendar article-meta-icon"></i>
                    <?= date('d.m.Y', strtotime($article['created_at'])) ?>
                </div>
                <div class="article-meta-item">
                    <i class="fas fa-clock article-meta-icon"></i>
                    <?= ceil(str_word_count(strip_tags($article['content'])) / 200) ?> min czytania
                </div>
                <?php if (!empty($article['tags'])): ?>
                    <div class="article-meta-item">
                        <i class="fas fa-tags article-meta-icon"></i>
                        <?php foreach ($article['tags'] as $index => $tag): ?>
                            <a href="<?= $base_url ?>/tag/<?= htmlspecialchars($tag['slug']) ?>" class="tag-link">
                                <?= htmlspecialchars($tag['name']) ?>
                            </a><?= $index < count($article['tags']) - 1 ? ', ' : '' ?>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>

            <?php if (!empty($article['featured_image'])): ?>
                <img src="<?= $article['featured_image'] ?>"
                    alt="<?= htmlspecialchars($article['title']) ?>"
                    class="article-image">
            <?php endif; ?>
        </header>

        <!-- Zakładki treści -->
        <div class="content-tabs mb-5">
            <ul class="nav nav-tabs" id="articleTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="article-tab" data-bs-toggle="tab" data-bs-target="#article-content"
                        type="button" role="tab" aria-controls="article-content" aria-selected="true">
                        <i class="far fa-file-alt me-2"></i> Artykuł
                    </button>
                </li>
                <?php if (!empty($article['video_url'])): ?>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="video-tab" data-bs-toggle="tab" data-bs-target="#video-content"
                            type="button" role="tab" aria-controls="video-content" aria-selected="false">
                            <i class="fas fa-video me-2"></i> Video
                        </button>
                    </li>
                <?php endif; ?>
                <?php if (!empty($article['audio_url'])): ?>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="audio-tab" data-bs-toggle="tab" data-bs-target="#audio-content"
                            type="button" role="tab" aria-controls="audio-content" aria-selected="false">
                            <i class="fas fa-headphones me-2"></i> Audio
                        </button>
                    </li>
                <?php endif; ?>
                <?php if (!empty($article['files'])): ?>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="files-tab" data-bs-toggle="tab" data-bs-target="#files-content"
                            type="button" role="tab" aria-controls="files-content" aria-selected="false">
                            <i class="fas fa-download me-2"></i> Materiały
                        </button>
                    </li>
                <?php endif; ?>
            </ul>

            <div class="tab-content" id="articleTabsContent">
                <!-- Zakładka z artykułem -->
                <div class="tab-pane fade show active" id="article-content" role="tabpanel" aria-labelledby="article-tab">
                    <div class="article-content">
                        <?php if (!empty($article['excerpt'])): ?>
                            <div class="article-excerpt">
                                <p class="lead"><?= $article['excerpt'] ?></p>
                            </div>
                        <?php endif; ?>

                        <?= $article['content'] ?>
                    </div>
                </div>

                <!-- Zakładka z video -->
                <?php if (!empty($article['video_url'])): ?>
                    <div class="tab-pane fade" id="video-content" role="tabpanel" aria-labelledby="video-tab">
                        <div class="video-container">
                            <div class="ratio ratio-16x9">
                                <iframe src="<?= htmlspecialchars($article['video_url']) ?>"
                                    title="Video dla artykułu: <?= htmlspecialchars($article['title']) ?>"
                                    allowfullscreen></iframe>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Zakładka z audio -->
                <?php if (!empty($article['audio_url'])): ?>
                    <div class="tab-pane fade" id="audio-content" role="tabpanel" aria-labelledby="audio-tab">
                        <div class="audio-container">
                            <audio controls class="w-100">
                                <source src="<?= htmlspecialchars($article['audio_url']) ?>" type="audio/mpeg">
                                Twoja przeglądarka nie obsługuje elementu audio.
                            </audio>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Zakładka z materiałami -->
                <?php if (!empty($article['files'])): ?>
                    <div class="tab-pane fade" id="files-content" role="tabpanel" aria-labelledby="files-tab">
                        <div class="files-container">
                            <h4>Materiały do pobrania</h4>
                            <div class="row g-3">
                                <?php foreach ($article['files'] as $file): ?>
                                    <div class="col-md-6">
                                        <div class="file-item">
                                            <div class="file-icon">
                                                <i class="fas fa-file-<?= $file['icon'] ?? 'alt' ?>"></i>
                                            </div>
                                            <div class="file-info">
                                                <h6><?= htmlspecialchars($file['name']) ?></h6>
                                                <p class="text-muted"><?= $file['size'] ?? '' ?></p>
                                            </div>
                                            <a href="<?= $file['url'] ?>" class="btn btn-outline-primary-custom btn-sm" download>
                                                <i class="fas fa-download"></i> Pobierz
                                            </a>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Udostępnianie -->
        <div class="article-sharing">
            <h5>Udostępnij artykuł</h5>
            <div class="sharing-buttons">
                <a href="https://www.facebook.com/sharer/sharer.php?u=<?= urlencode($current_url) ?>"
                    target="_blank" class="btn btn-outline-primary btn-sm">
                    <i class="fab fa-facebook-f"></i> Facebook
                </a>
                <a href="https://twitter.com/intent/tweet?url=<?= urlencode($current_url) ?>&text=<?= urlencode($article['title']) ?>"
                    target="_blank" class="btn btn-outline-info btn-sm">
                    <i class="fab fa-twitter"></i> Twitter
                </a>
                <a href="https://www.linkedin.com/sharing/share-offsite/?url=<?= urlencode($current_url) ?>"
                    target="_blank" class="btn btn-outline-primary btn-sm">
                    <i class="fab fa-linkedin-in"></i> LinkedIn
                </a>
                <a href="mailto:?subject=<?= urlencode($article['title']) ?>&body=<?= urlencode($current_url) ?>"
                    class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-envelope"></i> Email
                </a>
            </div>
        </div>
    </div>
</article>

<!-- Powiązane artykuły -->
<?php if (!empty($related_articles)): ?>
    <section class="section section-light">
        <div class="container">
            <h2 class="h3 mb-4">Powiązane artykuły</h2>
            <div class="row g-4">
                <?php foreach (array_slice($related_articles, 0, 3) as $related): ?>
                    <div class="col-md-4">
                        <div class="card news-card h-100">
                            <div class="card-img-container">
                                <?php if (!empty($related['featured_image'])): ?>
                                    <img src="<?= $related['featured_image'] ?>"
                                        class="card-img-top" alt="<?= htmlspecialchars($related['title']) ?>">
                                <?php else: ?>
                                    <img src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=400&h=300&fit=crop"
                                        class="card-img-top" alt="<?= htmlspecialchars($related['title']) ?>">
                                <?php endif; ?>
                                <div class="badge-container">
                                    <span class="badge badge-category"><?= htmlspecialchars($related['category_name'] ?? 'Artykuł') ?></span>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="card-meta">
                                    <span><?= date('d.m.Y', strtotime($related['created_at'])) ?></span>
                                    <span class="card-meta-divider">•</span>
                                    <span><?= ceil(str_word_count(strip_tags($related['content'])) / 200) ?> min czytania</span>
                                </div>
                                <h5 class="card-title"><?= htmlspecialchars($related['title']) ?></h5>
                                <p class="card-text">
                                    <?= htmlspecialchars(substr(strip_tags($related['excerpt'] ?: $related['content']), 0, 100)) ?>...
                                </p>
                                <a href="<?= $base_url ?>/artykuly/<?= htmlspecialchars($related['slug']) ?>" class="card-link">
                                    Czytaj więcej <i class="fas fa-arrow-right card-link-icon"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
<?php endif; ?>