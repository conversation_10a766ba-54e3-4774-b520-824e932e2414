<?php
// Ustawienia dla strony 404
$current_page = '404';
?>

<!-- <PERSON><PERSON><PERSON><PERSON> 404 -->
<section class="error-section">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <!-- <PERSON><PERSON> 404 -->
                <div class="error-graphic mb-4">
                    <div class="error-number">404</div>
                    <div class="error-icon">
                        <i class="fas fa-search fa-3x text-muted"></i>
                    </div>
                </div>
                
                <!-- Treść błędu -->
                <h1 class="error-title">Strona nie została znaleziona</h1>
                <p class="error-description lead text-muted">
                    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ale strona której szukasz nie istnieje lub została przeniesiona.
                    Sprawdź adres URL lub skorzystaj z wyszukiwarki poniżej.
                </p>
                
                <!-- Wyszukiwarka -->
                <div class="error-search mb-5">
                    <form method="GET" action="<?= $base_url ?>/szukaj" class="search-form">
                        <div class="input-group input-group-lg">
                            <input type="text" 
                                   name="q" 
                                   class="form-control" 
                                   placeholder="Wyszukaj treści..." 
                                   required>
                            <button class="btn btn-primary-custom" type="submit">
                                <i class="fas fa-search me-2"></i>Szukaj
                            </button>
                        </div>
                    </form>
                </div>
                
                <!-- Przyciski nawigacji -->
                <div class="error-actions">
                    <a href="<?= $base_url ?>" class="btn btn-primary-custom btn-lg me-3">
                        <i class="fas fa-home me-2"></i>Strona główna
                    </a>
                    <a href="<?= $base_url ?>/artykuly" class="btn btn-outline-primary-custom btn-lg">
                        <i class="fas fa-file-alt me-2"></i>Artykuły
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Popularne treści -->
<section class="section section-light">
    <div class="container">
        <h2 class="h3 mb-4 text-center">Popularne treści</h2>
        <div class="row g-4">
            <!-- Popularne artykuły -->
            <div class="col-md-6">
                <h4 class="h5 mb-3">
                    <i class="fas fa-fire text-danger me-2"></i>
                    Popularne artykuły
                </h4>
                <?php if (!empty($popular_articles)): ?>
                    <div class="list-group list-group-flush">
                        <?php foreach (array_slice($popular_articles, 0, 5) as $article): ?>
                            <a href="<?= $base_url ?>/artykul/<?= htmlspecialchars($article['slug']) ?>" 
                               class="list-group-item list-group-item-action border-0 px-0">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1"><?= htmlspecialchars($article['title']) ?></h6>
                                    <small class="text-muted"><?= date('d.m.Y', strtotime($article['created_at'])) ?></small>
                                </div>
                                <p class="mb-1 text-muted">
                                    <?= htmlspecialchars(substr(strip_tags($article['excerpt'] ?: $article['content']), 0, 100)) ?>...
                                </p>
                                <small class="text-primary"><?= htmlspecialchars($article['category_name'] ?? '') ?></small>
                            </a>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="text-muted">
                        <p>Brak dostępnych artykułów.</p>
                    </div>
                <?php endif; ?>
            </div>
            
            <!-- Popularne strony -->
            <div class="col-md-6">
                <h4 class="h5 mb-3">
                    <i class="fas fa-star text-warning me-2"></i>
                    Ważne strony
                </h4>
                <?php if (!empty($important_pages)): ?>
                    <div class="list-group list-group-flush">
                        <?php foreach (array_slice($important_pages, 0, 5) as $page): ?>
                            <a href="<?= $base_url ?>/<?= htmlspecialchars($page['slug']) ?>" 
                               class="list-group-item list-group-item-action border-0 px-0">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1"><?= htmlspecialchars($page['title']) ?></h6>
                                </div>
                                <?php if (!empty($page['excerpt'])): ?>
                                    <p class="mb-1 text-muted">
                                        <?= htmlspecialchars(substr($page['excerpt'], 0, 100)) ?>...
                                    </p>
                                <?php endif; ?>
                            </a>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <!-- Fallback strony -->
                    <div class="list-group list-group-flush">
                        <a href="<?= $base_url ?>/o-nas" class="list-group-item list-group-item-action border-0 px-0">
                            <h6 class="mb-1">O nas</h6>
                            <p class="mb-1 text-muted">Poznaj naszą misję i zespół</p>
                        </a>
                        <a href="<?= $base_url ?>/czlonkowie" class="list-group-item list-group-item-action border-0 px-0">
                            <h6 class="mb-1">Członkowie</h6>
                            <p class="mb-1 text-muted">Lista członków klastra</p>
                        </a>
                        <a href="<?= $base_url ?>/projekty" class="list-group-item list-group-item-action border-0 px-0">
                            <h6 class="mb-1">Projekty</h6>
                            <p class="mb-1 text-muted">Nasze realizacje i badania</p>
                        </a>
                        <a href="<?= $base_url ?>/kontakt" class="list-group-item list-group-item-action border-0 px-0">
                            <h6 class="mb-1">Kontakt</h6>
                            <p class="mb-1 text-muted">Skontaktuj się z nami</p>
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<!-- Kategorie -->
<?php if (!empty($categories)): ?>
<section class="section">
    <div class="container">
        <h2 class="h3 mb-4 text-center">Przeglądaj kategorie</h2>
        <div class="row g-3">
            <?php foreach (array_slice($categories, 0, 6) as $category): ?>
                <div class="col-md-4 col-lg-2">
                    <a href="<?= $base_url ?>/kategoria/<?= htmlspecialchars($category['slug']) ?>" 
                       class="category-card text-decoration-none">
                        <div class="card h-100 border-0 shadow-sm text-center">
                            <div class="card-body">
                                <h6 class="card-title"><?= htmlspecialchars($category['name']) ?></h6>
                                <p class="card-text text-muted small">
                                    <?= $category['article_count'] ?? 0 ?> artykułów
                                </p>
                            </div>
                        </div>
                    </a>
                </div>
            <?php endforeach; ?>
        </div>
        <div class="text-center mt-4">
            <a href="<?= $base_url ?>/artykuly" class="btn btn-outline-primary-custom">
                Zobacz wszystkie kategorie
            </a>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Kontakt -->
<section class="section section-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h2 class="h3 mb-3">Potrzebujesz pomocy?</h2>
                <p class="text-muted mb-4">
                    Jeśli nie możesz znaleźć tego czego szukasz, skontaktuj się z nami.
                    Chętnie pomożemy Ci znaleźć odpowiednie informacje.
                </p>
                <div class="contact-options">
                    <a href="<?= $base_url ?>/kontakt" class="btn btn-outline-primary-custom me-3">
                        <i class="fas fa-envelope me-2"></i>Skontaktuj się
                    </a>
                    <a href="tel:+48123456789" class="btn btn-outline-secondary">
                        <i class="fas fa-phone me-2"></i>+48 123 456 789
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Style dla strony 404 -->
<style>
.error-section {
    padding: 6rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.error-graphic {
    position: relative;
    margin-bottom: 3rem;
}

.error-number {
    font-size: 8rem;
    font-weight: 900;
    color: var(--primary);
    opacity: 0.1;
    line-height: 1;
    margin-bottom: -2rem;
}

.error-icon {
    position: relative;
    z-index: 1;
}

.error-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
}

.error-description {
    font-size: 1.2rem;
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.error-search {
    max-width: 500px;
    margin: 0 auto 3rem;
}

.error-search .input-group {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-radius: 0.5rem;
    overflow: hidden;
}

.error-actions {
    margin-bottom: 2rem;
}

.category-card:hover .card {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

.category-card .card {
    transition: transform 0.3s, box-shadow 0.3s;
}

.list-group-item:hover {
    background-color: #f8f9fa;
}

.contact-options {
    margin-top: 2rem;
}

@media (max-width: 768px) {
    .error-number {
        font-size: 6rem;
    }
    
    .error-title {
        font-size: 2rem;
    }
    
    .error-description {
        font-size: 1.1rem;
    }
    
    .error-actions .btn {
        display: block;
        width: 100%;
        margin-bottom: 1rem;
    }
    
    .error-actions .btn:last-child {
        margin-bottom: 0;
    }
}
</style>

<!-- JavaScript dla strony 404 -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animacja liczby 404
    const errorNumber = document.querySelector('.error-number');
    if (errorNumber) {
        errorNumber.style.opacity = '0.1';
        errorNumber.style.transform = 'scale(0.8)';
        
        setTimeout(() => {
            errorNumber.style.transition = 'all 0.8s ease-out';
            errorNumber.style.opacity = '0.1';
            errorNumber.style.transform = 'scale(1)';
        }, 300);
    }
    
    // Focus na wyszukiwarce
    const searchInput = document.querySelector('.error-search input[type="text"]');
    if (searchInput) {
        setTimeout(() => {
            searchInput.focus();
        }, 1000);
    }
});
</script>
