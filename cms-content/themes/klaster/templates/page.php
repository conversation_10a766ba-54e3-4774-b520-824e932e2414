<?php
// Ustawienia dla strony statycznej
$current_page = 'page';
?>

<!-- Breadcrumb -->
<?php if (!empty($show_breadcrumb)): ?>
<nav class="breadcrumb-custom" aria-label="breadcrumb">
    <div class="container">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?= $base_url ?>">Strona główna</a></li>
            <?php if (!empty($page['parent_title'])): ?>
                <li class="breadcrumb-item">
                    <a href="<?= $base_url ?>/<?= htmlspecialchars($page['parent_slug']) ?>">
                        <?= htmlspecialchars($page['parent_title']) ?>
                    </a>
                </li>
            <?php endif; ?>
            <li class="breadcrumb-item active" aria-current="page"><?= htmlspecialchars($page['title']) ?></li>
        </ol>
    </div>
</nav>
<?php endif; ?>

<!-- Nagłówek strony -->
<section class="page-header" style="background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 50%, var(--secondary) 100%); color: var(--white); padding: 4rem 0;">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h1 class="display-4 fw-bold mb-3"><?= htmlspecialchars($page['title']) ?></h1>
                <?php if (!empty($page['excerpt'])): ?>
                    <p class="lead" style="color: var(--light);">
                        <?= htmlspecialchars($page['excerpt']) ?>
                    </p>
                <?php endif; ?>
                <?php if (!empty($page['updated_at'])): ?>
                    <div class="page-meta">
                        <span class="badge bg-light text-dark">
                            <i class="fas fa-calendar me-1"></i>
                            Ostatnia aktualizacja: <?= date('d.m.Y', strtotime($page['updated_at'])) ?>
                        </span>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<!-- Treść strony -->
<article class="section">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <!-- Główna treść -->
                <div class="page-content">
                    <?php if (!empty($page['featured_image'])): ?>
                        <div class="page-featured-image mb-4">
                            <img src="<?= $page['featured_image'] ?>" 
                                 alt="<?= htmlspecialchars($page['title']) ?>" 
                                 class="img-fluid rounded shadow">
                        </div>
                    <?php endif; ?>
                    
                    <?= $page['content'] ?>
                </div>
                
                <!-- Pliki do pobrania -->
                <?php if (!empty($page['files'])): ?>
                    <div class="page-files mt-5">
                        <h3 class="h4 mb-3">
                            <i class="fas fa-download me-2 text-primary"></i>
                            Materiały do pobrania
                        </h3>
                        <div class="row g-3">
                            <?php foreach ($page['files'] as $file): ?>
                                <div class="col-md-6">
                                    <div class="file-item">
                                        <div class="file-icon">
                                            <i class="fas fa-file-<?= $file['icon'] ?? 'alt' ?> text-primary"></i>
                                        </div>
                                        <div class="file-info">
                                            <h6 class="mb-1"><?= htmlspecialchars($file['name']) ?></h6>
                                            <p class="text-muted mb-0">
                                                <?= $file['size'] ?? '' ?>
                                                <?php if (!empty($file['description'])): ?>
                                                    • <?= htmlspecialchars($file['description']) ?>
                                                <?php endif; ?>
                                            </p>
                                        </div>
                                        <a href="<?= $file['url'] ?>" 
                                           class="btn btn-outline-primary-custom btn-sm" 
                                           download>
                                            <i class="fas fa-download"></i>
                                        </a>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>
                
                <!-- Galeria zdjęć strony -->
                <?php if (!empty($page['images'])): ?>
                    <div class="page-gallery mt-5">
                        <h3 class="h4 mb-3">
                            <i class="fas fa-images me-2 text-primary"></i>
                            Galeria
                        </h3>
                        <div class="row g-3">
                            <?php foreach ($page['images'] as $image): ?>
                                <div class="col-md-4">
                                    <div class="page-image-item">
                                        <a href="<?= $image['url'] ?>" 
                                           data-bs-toggle="modal" 
                                           data-bs-target="#imageModal"
                                           data-image-url="<?= $image['url'] ?>"
                                           data-image-title="<?= htmlspecialchars($image['title'] ?? $image['filename']) ?>"
                                           data-image-description="<?= htmlspecialchars($image['description'] ?? '') ?>">
                                            <img src="<?= $image['thumbnail'] ?? $image['url'] ?>" 
                                                 alt="<?= htmlspecialchars($image['title'] ?? $image['filename']) ?>" 
                                                 class="img-fluid rounded">
                                            <div class="image-overlay">
                                                <i class="fas fa-search-plus"></i>
                                            </div>
                                        </a>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>
                
                <!-- Udostępnianie -->
                <div class="page-sharing mt-5">
                    <h5>Udostępnij stronę</h5>
                    <div class="sharing-buttons">
                        <a href="https://www.facebook.com/sharer/sharer.php?u=<?= urlencode($current_url) ?>" 
                           target="_blank" class="btn btn-outline-primary btn-sm">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://twitter.com/intent/tweet?url=<?= urlencode($current_url) ?>&text=<?= urlencode($page['title']) ?>" 
                           target="_blank" class="btn btn-outline-info btn-sm">
                            <i class="fab fa-twitter"></i> Twitter
                        </a>
                        <a href="https://www.linkedin.com/sharing/share-offsite/?url=<?= urlencode($current_url) ?>" 
                           target="_blank" class="btn btn-outline-primary btn-sm">
                            <i class="fab fa-linkedin-in"></i> LinkedIn
                        </a>
                        <a href="mailto:?subject=<?= urlencode($page['title']) ?>&body=<?= urlencode($current_url) ?>" 
                           class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-envelope"></i> Email
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Sidebar -->
            <div class="col-lg-4">
                <div class="page-sidebar">
                    <!-- Spis treści -->
                    <?php if (!empty($page['table_of_contents'])): ?>
                        <div class="sidebar-widget">
                            <h5 class="widget-title">Spis treści</h5>
                            <nav class="toc">
                                <?= $page['table_of_contents'] ?>
                            </nav>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Powiązane strony -->
                    <?php if (!empty($related_pages)): ?>
                        <div class="sidebar-widget">
                            <h5 class="widget-title">Powiązane strony</h5>
                            <ul class="related-pages">
                                <?php foreach ($related_pages as $related): ?>
                                    <li>
                                        <a href="<?= $base_url ?>/<?= htmlspecialchars($related['slug']) ?>">
                                            <?= htmlspecialchars($related['title']) ?>
                                        </a>
                                        <?php if (!empty($related['excerpt'])): ?>
                                            <p class="text-muted small">
                                                <?= htmlspecialchars(substr($related['excerpt'], 0, 80)) ?>...
                                            </p>
                                        <?php endif; ?>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Najnowsze artykuły -->
                    <?php if (!empty($recent_articles)): ?>
                        <div class="sidebar-widget">
                            <h5 class="widget-title">Najnowsze artykuły</h5>
                            <div class="recent-articles">
                                <?php foreach (array_slice($recent_articles, 0, 3) as $article): ?>
                                    <div class="recent-article-item">
                                        <a href="<?= $base_url ?>/artykul/<?= htmlspecialchars($article['slug']) ?>">
                                            <h6><?= htmlspecialchars($article['title']) ?></h6>
                                        </a>
                                        <p class="text-muted small">
                                            <?= date('d.m.Y', strtotime($article['created_at'])) ?>
                                        </p>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</article>

<!-- Modal do wyświetlania zdjęć -->
<?php if (!empty($page['images'])): ?>
<div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalLabel">Zdjęcie</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Zamknij"></button>
            </div>
            <div class="modal-body text-center">
                <img src="" alt="" class="img-fluid" id="modalImage">
                <p class="mt-3 text-muted" id="modalDescription"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Zamknij</button>
                <a href="" class="btn btn-primary-custom" id="downloadImage" download>
                    <i class="fas fa-download me-2"></i>Pobierz
                </a>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Style dla strony -->
<style>
.page-header {
    margin-bottom: 0;
}

.page-content {
    font-size: 1.1rem;
    line-height: 1.7;
}

.page-content h2,
.page-content h3,
.page-content h4 {
    margin-top: 2rem;
    margin-bottom: 1rem;
    color: var(--primary);
}

.file-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    background: #f8f9fa;
    transition: background-color 0.3s;
}

.file-item:hover {
    background: #e9ecef;
}

.file-icon {
    font-size: 1.5rem;
    margin-right: 1rem;
}

.file-info {
    flex: 1;
}

.page-image-item {
    position: relative;
    overflow: hidden;
    border-radius: 0.5rem;
}

.page-image-item img {
    transition: transform 0.3s;
}

.page-image-item:hover img {
    transform: scale(1.05);
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s;
    color: white;
    font-size: 1.5rem;
}

.page-image-item:hover .image-overlay {
    opacity: 1;
}

.page-sidebar {
    position: sticky;
    top: 2rem;
}

.sidebar-widget {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 0.5rem;
    margin-bottom: 2rem;
}

.widget-title {
    color: var(--primary);
    margin-bottom: 1rem;
    font-weight: 600;
}

.related-pages {
    list-style: none;
    padding: 0;
}

.related-pages li {
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.related-pages li:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.recent-article-item {
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.recent-article-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.sharing-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.breadcrumb-custom {
    background: #f8f9fa;
    padding: 1rem 0;
}

.toc ul {
    list-style: none;
    padding-left: 1rem;
}

.toc a {
    color: var(--text-dark);
    text-decoration: none;
    font-size: 0.9rem;
}

.toc a:hover {
    color: var(--primary);
}
</style>

<!-- JavaScript dla strony -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Modal dla zdjęć
    const imageModal = document.getElementById('imageModal');
    if (imageModal) {
        imageModal.addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget;
            const imageUrl = button.getAttribute('data-image-url');
            const imageTitle = button.getAttribute('data-image-title');
            const imageDescription = button.getAttribute('data-image-description');
            
            const modalImage = document.getElementById('modalImage');
            const modalTitle = document.getElementById('imageModalLabel');
            const modalDescription = document.getElementById('modalDescription');
            const downloadLink = document.getElementById('downloadImage');
            
            modalImage.src = imageUrl;
            modalImage.alt = imageTitle;
            modalTitle.textContent = imageTitle;
            modalDescription.textContent = imageDescription;
            downloadLink.href = imageUrl;
        });
    }
});
</script>
