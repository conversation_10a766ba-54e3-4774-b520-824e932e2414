{"name": "<PERSON><PERSON><PERSON>", "description": "Nowoczesny szablon medyczny dla klastra innowacyjnej medycyny", "version": "1.0.0", "author": "CMS Team", "screenshot": "screenshot.png", "tags": ["medical", "modern", "responsive", "bootstrap"], "supports": {"menus": true, "widgets": true, "custom_logo": true, "featured_images": true, "galleries": true, "banners": true, "search": true, "breadcrumbs": true, "social_sharing": true, "comments": false}, "templates": {"home": {"name": "Strona główna", "description": "Szablon strony głównej z hero section, statystykami i najnowszymi artykułami", "file": "templates/home.php"}, "articles": {"name": "Lista artykułów", "description": "Szablon listy artykułów z filtrami i wyszukiwaniem", "file": "templates/articles.php"}, "article": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Szablon pojedynczego artykułu z zakładkami treści", "file": "templates/article.php"}, "category": {"name": "Kategoria artykułów", "description": "Szablon kategorii artykułów", "file": "templates/category.php"}, "page": {"name": "Strona statyczna", "description": "Szablon strony statycznej", "file": "templates/page.php"}, "gallery": {"name": "Galeria", "description": "Szablon głównej strony galerii", "file": "templates/gallery.php"}, "gallery-category": {"name": "<PERSON><PERSON><PERSON> galerii", "description": "Szablon kategorii galerii ze zdjęciami", "file": "templates/gallery-category.php"}, "search": {"name": "Wyszukiwanie", "description": "Szablon wyników wyszukiwania", "file": "templates/search.php"}, "404": {"name": "Strona błędu 404", "description": "Szablon strony nie znaleziono", "file": "templates/404.php"}}, "menus": {"main": {"name": "<PERSON><PERSON>", "description": "Główne menu nawigacyjne w nagłówku"}, "footer": {"name": "<PERSON><PERSON> stop<PERSON>", "description": "<PERSON>u w stopce strony"}}, "widget_areas": {"sidebar": {"name": "Pasek boczny", "description": "Główny pasek boczny"}, "footer_1": {"name": "Stopka - kolumna 1", "description": "Pierwsza kolumna w stopce"}, "footer_2": {"name": "Stopka - kolumna 2", "description": "Druga kolumna w stopce"}, "footer_3": {"name": "Stopka - kolumna 3", "description": "Trzecia kolumna w stopce"}}, "customizer": {"colors": {"primary": {"label": "<PERSON><PERSON> głów<PERSON>", "default": "#447c74"}, "secondary": {"label": "<PERSON><PERSON>", "default": "#28b9b2"}, "accent": {"label": "<PERSON><PERSON>", "default": "#8eb6b4"}}, "typography": {"body_font": {"label": "Czcionka treści", "default": "Roboto"}, "heading_font": {"label": "Czcionka nagłówków", "default": "Roboto"}}, "layout": {"container_width": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> kontenera", "default": "1200px"}, "sidebar_position": {"label": "Pozycja paska bocznego", "default": "right", "options": ["left", "right", "none"]}}}, "assets": {"css": ["css/main.css", "css/home.css", "css/articles.css"], "js": ["js/main.js"], "fonts": ["https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap"], "external": {"bootstrap": "5.3.2", "fontawesome": "6.4.2"}}, "features": {"responsive": true, "rtl_support": false, "accessibility": true, "seo_optimized": true, "performance_optimized": true, "translation_ready": true}, "requirements": {"php": ">=8.0", "cms_version": ">=1.0.0"}}