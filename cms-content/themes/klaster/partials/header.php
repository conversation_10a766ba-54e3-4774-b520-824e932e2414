<!-- Nag<PERSON><PERSON>ek -->
<header class="navbar navbar-expand-lg navbar-custom sticky-top">
    <div class="container">
        <a class="navbar-brand" href="<?= $base_url ?>">
            <img src="http://innowacyjnamedycyna.eu/wp-content/uploads/2013/11/logo.png" alt="Innowacyjna Medycyna">
        </a>
        
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarMain" 
                aria-controls="navbarMain" aria-expanded="false" aria-label="Przełącz nawigację">
            <span class="navbar-toggler-icon"></span>
        </button>
        
        <div class="collapse navbar-collapse" id="navbarMain">
            <ul class="navbar-nav ms-auto">
                <li class="nav-item">
                    <a class="nav-link <?= ($current_page === 'home') ? 'active' : '' ?>" href="<?= $base_url ?>">Strona główna</a>
                </li>
                
                <?php if (!empty($main_menu)): ?>
                    <?php foreach ($main_menu as $item): ?>
                        <?php if (!empty($item['children'])): ?>
                            <!-- Menu dropdown -->
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown<?= $item['id'] ?>" role="button" 
                                   data-bs-toggle="dropdown" aria-expanded="false">
                                    <?= htmlspecialchars($item['title']) ?>
                                </a>
                                <ul class="dropdown-menu" aria-labelledby="navbarDropdown<?= $item['id'] ?>">
                                    <?php foreach ($item['children'] as $child): ?>
                                        <li><a class="dropdown-item" href="<?= htmlspecialchars($child['url']) ?>"><?= htmlspecialchars($child['title']) ?></a></li>
                                    <?php endforeach; ?>
                                </ul>
                            </li>
                        <?php else: ?>
                            <!-- Zwykły link -->
                            <li class="nav-item">
                                <a class="nav-link" href="<?= htmlspecialchars($item['url']) ?>"><?= htmlspecialchars($item['title']) ?></a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                <?php else: ?>
                    <!-- Fallback menu -->
                    <li class="nav-item">
                        <a class="nav-link <?= ($current_page === 'articles') ? 'active' : '' ?>" href="<?= $base_url ?>/artykuly">Artykuły</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= ($current_page === 'gallery') ? 'active' : '' ?>" href="<?= $base_url ?>/galeria">Galeria</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= $base_url ?>/kontakt">Kontakt</a>
                    </li>
                <?php endif; ?>
            </ul>
        </div>
    </div>
</header>
