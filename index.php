<?php

/**
 * Główny router frontendu CMS
 * Punkt wejścia dla wszystkich żądań frontendowych
 */

// Załaduj główną konfigurację CMS
require_once __DIR__ . '/cms-config.php';

// Załaduj klasy backendu (modele)
require_once CMS_ADMIN_PATH . '/app/Database.php';
require_once CMS_ADMIN_PATH . '/models/Article.php';
require_once CMS_ADMIN_PATH . '/models/StaticPage.php';
require_once CMS_ADMIN_PATH . '/models/Gallery.php';
require_once CMS_ADMIN_PATH . '/models/Menu.php';
require_once CMS_ADMIN_PATH . '/models/Category.php';
require_once CMS_ADMIN_PATH . '/models/Banner.php';

// Załaduj klasy frontendu
require_once CMS_CONTENT_PATH . '/app/FrontendRouter.php';
require_once CMS_CONTENT_PATH . '/app/TemplateEngine.php';
require_once CMS_CONTENT_PATH . '/app/FrontendController.php';

// Inicjalizacja bazy danych
$database = new Database();
$db = $database->getConnection();

// Określ aktywną skórkę (domyślnie z konfiguracji)
$activeSkin = $_GET['skin'] ?? DEFAULT_THEME;

// Walidacja skórki - sprawdź czy katalog istnieje
$skinPath = cms_get_theme_path($activeSkin);
if (!is_dir($skinPath)) {
    $activeSkin = DEFAULT_THEME; // fallback do domyślnej skórki
    $skinPath = cms_get_theme_path($activeSkin);
}

// Inicjalizacja template engine
$templateEngine = new TemplateEngine($skinPath);

// Obsługa żądania - sprawdź URI na początku
$method = $_SERVER['REQUEST_METHOD'];
$uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

// Przekieruj żądania do panelu administracyjnego
if (strpos($uri, '/cms-admin') === 0) {
    // Usuń /cms-admin z URI i przekieruj do routera admina
    $adminUri = substr($uri, 10); // usuń '/cms-admin'
    if (empty($adminUri)) {
        $adminUri = '/';
    }

    // Przekieruj do routera admina
    $_SERVER['REQUEST_URI'] = $adminUri;
    require_once CMS_ADMIN_PATH . '/public/index.php';
    exit;
}

// Inicjalizacja routera frontendu
$router = new FrontendRouter($db, $templateEngine, $activeSkin);

// Definicja tras frontendu
$router->addRoute('GET', '/', 'FrontendController@home');
$router->addRoute('GET', '/artykuly', 'FrontendController@articles');
$router->addRoute('GET', '/artykuly/{slug}', 'FrontendController@article');
$router->addRoute('GET', '/kategoria/{slug}', 'FrontendController@category');
$router->addRoute('GET', '/galeria', 'FrontendController@gallery');
$router->addRoute('GET', '/galeria/{category}', 'FrontendController@galleryCategory');
$router->addRoute('GET', '/szukaj', 'FrontendController@search');
$router->addRoute('GET', '/sitemap.xml', 'FrontendController@sitemap');
$router->addRoute('GET', '/rss.xml', 'FrontendController@rss');
$router->addRoute('GET', '/{slug}', 'FrontendController@page'); // strony statyczne na końcu

// Obsługa plików statycznych z themes
if (strpos($uri, '/cms-content/themes/') === 0) {
    $filePath = CMS_ROOT . $uri;
    if (file_exists($filePath) && is_file($filePath)) {
        // Ustaw odpowiedni Content-Type
        $ext = pathinfo($filePath, PATHINFO_EXTENSION);
        $contentTypes = [
            'css' => 'text/css',
            'js' => 'application/javascript',
            'png' => 'image/png',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'gif' => 'image/gif',
            'svg' => 'image/svg+xml',
            'webp' => 'image/webp'
        ];

        if (isset($contentTypes[$ext])) {
            header('Content-Type: ' . $contentTypes[$ext]);
        }

        readfile($filePath);
        exit;
    }
}

// Obsługa plików uploads
if (strpos($uri, '/cms-content/uploads/') === 0) {
    $filePath = CMS_ROOT . $uri;
    if (file_exists($filePath) && is_file($filePath)) {
        // Ustaw odpowiedni Content-Type
        $ext = pathinfo($filePath, PATHINFO_EXTENSION);
        $contentTypes = [
            'png' => 'image/png',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'gif' => 'image/gif',
            'svg' => 'image/svg+xml',
            'webp' => 'image/webp',
            'pdf' => 'application/pdf',
            'zip' => 'application/zip',
            'doc' => 'application/msword',
            'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'txt' => 'text/plain'
        ];

        if (isset($contentTypes[$ext])) {
            header('Content-Type: ' . $contentTypes[$ext]);
        }

        readfile($filePath);
        exit;
    }
}

$router->handleRequest($method, $uri);
