# Test funkcjonalności uploadu plików

## Jak przetestować upload plików w artykułach

### 1. Uruchom aplikację
```bash
cd php/public
php -S localhost:8000
```

### 2. Zaloguj się do panelu
- URL: http://localhost:8000/login
- Login: admin
- Hasło: password

### 3. Przejdź do zarządzania artykułami
- Klik<PERSON>j "Artykuły" w menu bocznym
- Kliknij "Nowy artykuł" lub edytuj istniejący

### 4. Przetestuj upload plików

#### <PERSON><PERSON><PERSON>
- <PERSON><PERSON><PERSON><PERSON> "Dodaj nowe zdjęcia"
- W<PERSON><PERSON>rz pliki: JPG, PNG, GIF, WebP
- Sprawdź podgląd miniatur
- Zapisz artykuł

#### Dokumenty
- Kliknij "Dodaj nowe pliki"
- W<PERSON><PERSON><PERSON> pliki: PDF, DOC, XLS, ZIP, TXT
- Sprawdź ikony typów plików
- Zapisz artykuł

#### Usuwanie plików
- W edycji artykułu kliknij ikonę kosza przy pliku
- Potwierdź usunięcie
- Plik zostanie usunięty z serwera i bazy danych

### 5. Sprawdź pliki na serwerze
```bash
ls -la php/public/uploads/articles/
```

### 6. Sprawdź bazę danych
```bash
cd php
sqlite3 database.sqlite "SELECT * FROM article_files;"
```

## Ograniczenia i walidacja

### Dozwolone typy plików
- **Obrazy**: JPEG, PNG, GIF, WebP
- **Dokumenty**: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX
- **Archiwa**: ZIP, RAR, 7Z
- **Tekstowe**: TXT, CSV

### Limity
- Maksymalny rozmiar pliku: 10MB
- Walidacja typu MIME po stronie serwera
- Unikalne nazwy plików (uniqid + oryginalna nazwa)

### Bezpieczeństwo
- Sprawdzanie typu MIME przez finfo
- Walidacja rozszerzenia pliku
- Ochrona przed przesłaniem szkodliwych plików
- Pliki przechowywane poza katalogiem głównym aplikacji

## Struktura plików

```
public/uploads/articles/
├── 64a1b2c3d4e5f_dokument.pdf
├── 64a1b2c3d4e5g_zdjecie.jpg
└── 64a1b2c3d4e5h_arkusz.xlsx
```

## Troubleshooting

### Błędy uploadu
1. Sprawdź uprawnienia do katalogu uploads/
2. Sprawdź limity PHP (upload_max_filesize, post_max_size)
3. Sprawdź logi błędów PHP

### Błędy AJAX
1. Sprawdź konsolę przeglądarki (F12)
2. Sprawdź czy żądanie ma nagłówek X-Requested-With
3. Sprawdź odpowiedź serwera w zakładce Network 