<?php
$translations = include __DIR__ . '/../../lang/pl.php';

$title = isset($tag) ? $translations['tags.edit_title'] : $translations['tags.create_title'];
$current_page = 'tags';

ob_start();
?>

<div class="container mt-4">
    <h1><?php echo isset($tag) ? 'Edytuj Tag' : 'Dodaj Nowy Tag'; ?></h1>

    <form action="<?php echo isset($tag) ? '/tags/update/' . $tag['id'] : '/tags/store'; ?>" method="POST">
        <div class="mb-3">
            <label for="name" class="form-label">Nazwa tagu</label>
            <input type="text" class="form-control" id="name" name="name" value="<?php echo htmlspecialchars($tag['name'] ?? ''); ?>" required>
        </div>
        <div class="mb-3">
            <label for="slug" class="form-label">Slug</label>
            <input type="text" class="form-control" id="slug" name="slug" value="<?php echo htmlspecialchars($tag['slug'] ?? ''); ?>" required>
        </div>

        <button type="submit" class="btn btn-primary">Zapisz tag</button>
        <a href="/tags" class="btn btn-secondary">Anuluj</a>
    </form>
</div>

<!-- Bootstrap Bundle with Popper -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
    // Basic slug generation based on name input (client-side, for user convenience)
    document.getElementById('name').addEventListener('input', function() {
        const nameInput = this.value;
        const slugInput = document.getElementById('slug');
        if (slugInput.value === '' || !slugInput.dataset.edited) {
            const slug = nameInput
                .toLowerCase()
                .replace(/[^a-z0-9]+/g, '-')
                .replace(/(^-|-$)/g, '');
            slugInput.value = slug;
        }
    });

    document.getElementById('slug').addEventListener('input', function() {
        this.dataset.edited = 'true'; // Mark slug as manually edited
    });

    // Prevent default form submission for demo
    document.querySelector('form').addEventListener('submit', function(e) {
        // e.preventDefault(); // Uncomment for testing form logic without full page reload
        console.log('Form submitted');
    });
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../views/LayoutView.php';
?>