<?php
$translations = include __DIR__ . '/../../lang/pl.php';
$title = $translations['menus.title'] ?? 'Menu';
$current_page = 'menus';

// Add CSS to layout head
$additional_css = <<<CSS
<style>
.sortable-item {
    cursor: grab;
    border-left: 3px solid transparent;
    transition: background-color 0.2s ease, border-left-color 0.2s ease;
}
.sortable-item:hover {
    background-color: rgba(90, 103, 216, 0.05);
    border-left-color: var(--bs-primary);
}
.sortable-item:active {
    cursor: grabbing;
    background-color: rgba(90, 103, 216, 0.1);
}
.drag-handle {
    color: #adb5bd;
    padding-right: 0.5rem;
    display: inline-block;
}
.sortable-ghost {
    background-color: rgba(90, 103, 216, 0.1);
    border-left-color: var(--bs-primary);
}
</style>
CSS;

// Add JS to layout scripts
$additional_scripts = <<<JS
<script>
// Initialize SortableJS for menu items if the list exists
document.addEventListener('DOMContentLoaded', function() {
    const menuItemsList = document.getElementById('menuItemsList');
    const saveOrderBtn = document.getElementById('saveOrderBtn');
    
    if (menuItemsList) {
        const sortable = Sortable.create(menuItemsList, {
            animation: 150,
            handle: '.drag-handle',
            ghostClass: 'sortable-ghost',
            onEnd: function(evt) {
                // Show save button when order changes
                if (saveOrderBtn) {
                    saveOrderBtn.classList.remove('d-none');
                }
            },
        });
    
        // Handle Save Order button click
        if (saveOrderBtn) {
            saveOrderBtn.addEventListener('click', function() {
                const itemOrder = sortable.toArray(); // Get the new order of item IDs
                const menuId = <?php echo isset($selectedMenuId) ? $selectedMenuId : 'null'; ?>; // Get selected menu ID from PHP
            
                if (menuId && itemOrder.length > 0) {
                    // Send the new order to the server
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = '/menus/' + menuId + '/items/order';
                
                    const input = document.createElement('input');
                    input.type = 'hidden';
                    input.name = 'item_order';
                    input.value = JSON.stringify(itemOrder);
                
                    form.appendChild(input);
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        }
    }
});
</script>
JS;

ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="page-title"><?php echo $translations['menus.title']; ?></h1>
    <a href="/menus/new" class="btn btn-primary">
        <i class="bi bi-list-nested me-1"></i> <?php echo $translations['menus.new_button']; ?>
    </a>
</div>

<?php if (isset($flash)): ?>
    <div class="alert alert-<?php echo $flash['type']; ?> alert-dismissible fade show shadow-sm" role="alert">
        <i class="bi bi-info-circle me-2"></i> <?php echo htmlspecialchars($flash['message']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<!-- Menus List Section -->
<div class="card shadow-sm mb-4">
    <div class="card-header bg-white py-3">
        <h5 class="card-title mb-0 fw-bold"><?php echo $translations['menus.available_menus']; ?></h5>
    </div>
    <div class="card-body p-0">
        <div class="p-3 border-bottom">
            <form method="GET" action="/menus" class="row g-3 align-items-center mb-0">
                <div class="col-md-8">
                    <div class="input-group">
                        <span class="input-group-text bg-light border-end-0">
                            <i class="bi bi-search text-muted"></i>
                        </span>
                        <input class="form-control border-start-0 ps-0" type="search"
                            placeholder="<?php echo $translations['menus.search_placeholder']; ?>"
                            aria-label="Search" name="search"
                            value="<?php echo htmlspecialchars($_GET['search'] ?? ''); ?>">
                    </div>
                    <?php if (isset($selectedMenuId)): ?>
                        <input type="hidden" name="menu_id" value="<?php echo $selectedMenuId; ?>">
                    <?php endif; ?>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-primary" type="submit">
                        <?php echo $translations['common.search']; ?>
                    </button>
                </div>
            </form>
        </div>

        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th scope="col"><?php echo $translations['menus.table.name']; ?></th>
                        <th scope="col"><?php echo $translations['menus.table.location']; ?></th>
                        <th scope="col"><?php echo $translations['menus.table.items_count']; ?></th>
                        <th scope="col" class="text-end"><?php echo $translations['menus.table.actions']; ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($menus)): ?>
                        <tr>
                            <td colspan="4" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="bi bi-list-ul fs-3 d-block mb-2"></i>
                                    <?php echo $translations['menus.table.no_results']; ?>
                                </div>
                            </td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($menus as $menu): ?>
                            <tr class="<?php echo (isset($selectedMenuId) && $selectedMenuId == $menu['id']) ? 'table-active' : ''; ?>">
                                <td class="align-middle fw-medium">
                                    <i class="bi bi-list me-2 text-primary"></i>
                                    <?php echo htmlspecialchars($menu['name']); ?>
                                </td>
                                <td class="align-middle">
                                    <span class="badge bg-light text-dark border">
                                        <?php echo htmlspecialchars($menu['slug'] ?? 'brak'); ?>
                                    </span>
                                </td>
                                <td class="align-middle">
                                    <span class="badge bg-secondary rounded-pill">
                                        <?php echo $menu['itemCount'] ?? 0; ?>
                                        <i class="bi bi-link-45deg ms-1"></i>
                                    </span>
                                </td>
                                <td class="align-middle text-end">
                                    <div class="btn-group">
                                        <a href="/menus?menu_id=<?php echo $menu['id']; ?>"
                                            class="btn btn-sm btn-outline-secondary">
                                            <i class="bi bi-list-check me-1"></i> <?php echo $translations['menus.items_button']; ?>
                                        </a>
                                        <a href="/menus/edit/<?php echo $menu['id']; ?>"
                                            class="btn btn-sm btn-primary">
                                            <i class="bi bi-pencil-square"></i>
                                        </a>
                                        <a href="/menus/delete/<?php echo $menu['id']; ?>"
                                            class="btn btn-sm btn-danger"
                                            onclick="return confirm('<?php echo $translations['menus.delete_confirm']; ?>');">
                                            <i class="bi bi-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Menu Items Section (visible only if a menu is selected) -->
<?php if (isset($selectedMenu)): ?>
    <div class="card shadow-sm">
        <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0 fw-bold">
                <i class="bi bi-list-nested me-2"></i>
                <?php echo $translations['menus.items_title']; ?>:
                <span class="text-primary"><?php echo htmlspecialchars($selectedMenu['name']); ?></span>
            </h5>
            <a href="/menus/<?php echo $selectedMenu['id']; ?>/items/new" class="btn btn-sm btn-primary">
                <i class="bi bi-plus-circle me-1"></i> <?php echo $translations['menus.add_item_button']; ?>
            </a>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th scope="col"><?php echo $translations['menus.items_table.name']; ?></th>
                            <th scope="col"><?php echo $translations['menus.items_table.url']; ?></th>
                            <th scope="col" class="text-end"><?php echo $translations['menus.items_table.actions']; ?></th>
                        </tr>
                    </thead>
                    <tbody id="menuItemsList">
                        <?php if (empty($menuItems)): ?>
                            <tr>
                                <td colspan="3" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="bi bi-link-45deg fs-3 d-block mb-2"></i>
                                        <?php echo $translations['menus.items_table.no_items']; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($menuItems as $item): ?>
                                <tr class="sortable-item" data-item-id="<?php echo $item['id']; ?>">
                                    <td class="align-middle">
                                        <div class="d-flex align-items-center">
                                            <span class="drag-handle">
                                                <i class="bi bi-grip-vertical"></i>
                                            </span>
                                            <span class="fw-medium"><?php echo htmlspecialchars($item['title']); ?></span>
                                        </div>
                                    </td>
                                    <td class="align-middle">
                                        <code><?php echo htmlspecialchars($item['url']); ?></code>
                                    </td>
                                    <td class="align-middle text-end">
                                        <div class="btn-group">
                                            <a href="/menus/<?php echo $selectedMenu['id']; ?>/items/edit/<?php echo $item['id']; ?>"
                                                class="btn btn-sm btn-primary">
                                                <i class="bi bi-pencil-square"></i>
                                            </a>
                                            <a href="/menus/<?php echo $selectedMenu['id']; ?>/items/delete/<?php echo $item['id']; ?>"
                                                class="btn btn-sm btn-danger"
                                                onclick="return confirm('<?php echo $translations['menus.delete_item_confirm']; ?>');">
                                                <i class="bi bi-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <?php if (!empty($menuItems)): ?>
                <div class="p-3 border-top text-end">
                    <button id="saveOrderBtn" class="btn btn-success d-none">
                        <i class="bi bi-check2-circle me-1"></i> <?php echo $translations['menus.save_order_button']; ?>
                    </button>
                </div>
            <?php endif; ?>
        </div>
    </div>
<?php endif; ?>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../views/LayoutView.php';
?>