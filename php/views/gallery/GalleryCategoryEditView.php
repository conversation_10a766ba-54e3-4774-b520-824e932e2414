<?php
$title = 'Edytuj kategorię - ' . htmlspecialchars($category['name']);
$current_page = 'gallery';
ob_start();
?>

<div class="d-flex align-items-center mb-4">
    <a href="/gallery" class="btn btn-sm btn-outline-secondary me-3">
        <i class="bi bi-arrow-left"></i>
    </a>
    <h1 class="page-title mb-0">Edytuj kategorię</h1>
</div>

<?php if (isset($flash)): ?>
    <div class="alert alert-<?php echo $flash['type']; ?> alert-dismissible fade show shadow-sm" role="alert">
        <i class="bi bi-info-circle me-2"></i> <?php echo htmlspecialchars($flash['message']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<div class="card shadow-sm">
    <div class="card-body">
        <form action="/gallery/categories/update/<?php echo $category['id']; ?>" method="POST">
            <div class="mb-3">
                <label for="categoryName" class="form-label">Nazwa kategorii</label>
                <div class="input-group">
                    <span class="input-group-text bg-light">
                        <i class="bi bi-folder text-primary"></i>
                    </span>
                    <input type="text" class="form-control" id="categoryName" name="name" 
                           value="<?php echo htmlspecialchars($category['name']); ?>" required>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="categorySlug" class="form-label">Slug</label>
                <div class="input-group">
                    <span class="input-group-text bg-light">
                        <i class="bi bi-link-45deg text-secondary"></i>
                    </span>
                    <input type="text" class="form-control font-monospace" id="categorySlug" name="slug" 
                           value="<?php echo htmlspecialchars($category['slug']); ?>" required>
                </div>
                <small class="form-text text-muted mt-1">
                    <i class="bi bi-info-circle-fill me-1"></i>
                    Unikalny identyfikator używany w adresach URL
                </small>
            </div>
            
            <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-check-lg me-1"></i>
                    Zaktualizuj kategorię
                </button>
                <a href="/gallery" class="btn btn-secondary">
                    <i class="bi bi-x-lg me-1"></i>
                    Anuluj
                </a>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-generate slug from category name (if slug is empty)
    const categoryName = document.getElementById('categoryName');
    const categorySlug = document.getElementById('categorySlug');
    
    if (categoryName && categorySlug) {
        categoryName.addEventListener('input', function() {
            // Only auto-generate if slug is empty or matches the original pattern
            if (categorySlug.value === '' || categorySlug.value === generateSlug(categoryName.dataset.originalValue || '')) {
                const slug = generateSlug(this.value);
                categorySlug.value = slug;
            }
        });
    }
    
    function generateSlug(text) {
        return text
            .toLowerCase()
            .trim()
            .replace(/[^a-z0-9-]+/g, '-')
            .replace(/^-+|-+$/g, '');
    }
});
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../LayoutView.php';
?> 