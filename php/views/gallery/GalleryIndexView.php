<?php
$title = 'Galeria';
$current_page = 'gallery';
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="page-title">Galeria</h1>
    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
        <i class="bi bi-folder-plus me-1"></i> Dodaj kategor<PERSON>
    </button>
</div>

<?php if (isset($flash)): ?>
    <div class="alert alert-<?php echo $flash['type']; ?> alert-dismissible fade show shadow-sm" role="alert">
        <i class="bi bi-info-circle me-2"></i> <?php echo htmlspecialchars($flash['message']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<?php if (empty($categories)): ?>
    <div class="text-center py-5">
        <div class="mb-4">
            <i class="bi bi-folder2-open text-muted" style="font-size: 4rem;"></i>
        </div>
        <h4 class="text-muted mb-3">Brak kategorii</h4>
        <p class="text-muted mb-4">Utwórz pierwszą kategorię aby zacząć organizować zdjęcia w galerii.</p>
        <button type="button" class="btn btn-primary btn-lg" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
            <i class="bi bi-folder-plus me-2"></i> Utwórz pierwszą kategorię
        </button>
    </div>
<?php else: ?>
    <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 row-cols-xl-4 g-4">
        <?php foreach ($categories as $category): ?>
            <div class="col">
                <div class="card h-100 shadow-sm category-card">
                    <a href="/gallery/category/<?php echo $category['id']; ?>" class="text-decoration-none">
                        <div class="card-body text-center p-4">
                            <div class="mb-3">
                                <i class="bi bi-folder-fill text-primary" style="font-size: 3rem;"></i>
                            </div>
                            <h5 class="card-title text-dark mb-2">
                                <?php echo htmlspecialchars($category['name']); ?>
                            </h5>
                            <p class="card-text text-muted">
                                <i class="bi bi-images me-1"></i>
                                <?php 
                                $count = $category['image_count'];
                                if ($count == 0) {
                                    echo 'Brak zdjęć';
                                } elseif ($count == 1) {
                                    echo '1 zdjęcie';
                                } elseif ($count < 5) {
                                    echo $count . ' zdjęcia';
                                } else {
                                    echo $count . ' zdjęć';
                                }
                                ?>
                            </p>
                        </div>
                    </a>
                    <div class="card-footer bg-transparent border-top-0 text-center pb-3">
                        <div class="btn-group btn-group-sm" role="group">
                            <a href="/gallery/category/<?php echo $category['id']; ?>" class="btn btn-outline-primary">
                                <i class="bi bi-eye me-1"></i> Zobacz
                            </a>
                            <a href="/gallery/categories/edit/<?php echo $category['id']; ?>" class="btn btn-outline-secondary">
                                <i class="bi bi-pencil"></i>
                            </a>
                            <a href="/gallery/categories/delete/<?php echo $category['id']; ?>" 
                               class="btn btn-outline-danger"
                               onclick="return confirm('Czy na pewno chcesz usunąć kategorię &quot;<?php echo htmlspecialchars($category['name']); ?>&quot;?\n\nUWAGA: Wszystkie zdjęcia w tej kategorii również zostaną usunięte!')">
                                <i class="bi bi-trash"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
<?php endif; ?>

<!-- Add Category Modal -->
<div class="modal fade" id="addCategoryModal" tabindex="-1" aria-labelledby="addCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="/gallery/categories/store" method="POST">
                <div class="modal-header">
                    <h5 class="modal-title" id="addCategoryModalLabel">
                        <i class="bi bi-folder-plus me-2"></i>Nowa kategoria
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="categoryName" class="form-label">Nazwa kategorii</label>
                        <div class="input-group">
                            <span class="input-group-text bg-light">
                                <i class="bi bi-folder text-primary"></i>
                            </span>
                            <input type="text" class="form-control" id="categoryName" name="name" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="categorySlug" class="form-label">Slug</label>
                        <div class="input-group">
                            <span class="input-group-text bg-light">
                                <i class="bi bi-link-45deg text-secondary"></i>
                            </span>
                            <input type="text" class="form-control font-monospace" id="categorySlug" name="slug" required>
                        </div>
                        <small class="form-text text-muted mt-1">
                            <i class="bi bi-info-circle-fill me-1"></i>
                            Wygenerowany automatycznie unikalny identyfikator używany w adresach URL
                        </small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x me-1"></i>Anuluj
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check2 me-1"></i>Dodaj kategorię
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.category-card {
    transition: all 0.3s ease;
    border: 1px solid #dee2e6;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1) !important;
    border-color: var(--bs-primary);
}

.category-card a:hover .card-title {
    color: var(--bs-primary) !important;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-generate slug from category name
    const categoryName = document.getElementById('categoryName');
    const categorySlug = document.getElementById('categorySlug');
    
    if (categoryName && categorySlug) {
        categoryName.addEventListener('input', function() {
            const slug = this.value
                .toLowerCase()
                .trim()
                .replace(/[^a-z0-9-]+/g, '-')
                .replace(/^-+|-+$/g, '');
            categorySlug.value = slug;
        });
    }
});
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../LayoutView.php';
?> 