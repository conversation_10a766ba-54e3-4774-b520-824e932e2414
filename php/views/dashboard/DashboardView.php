<?php
$lang = include __DIR__ . '/../../lang/pl.php';
$title = $lang['dashboard'];
$current_page = 'dashboard';
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="page-title"><?php echo $lang['dashboard']; ?></h1>
    <a href="/articles/new" class="btn btn-primary">
        <i class="bi bi-file-earmark-plus me-1"></i> Utwórz nowy artykuł
    </a>
</div>

<!-- Statistics Cards -->
<div class="row row-cols-1 row-cols-md-2 row-cols-lg-4 g-4 mb-4">
    <?php foreach ($stats as $stat): ?>
        <div class="col">
            <div class="card h-100 shadow-sm border-0">
                <div class="card-body p-4">
                    <?php
                    // Choose icon based on title
                    $icon = 'bi-collection';
                    $cardColor = 'primary';

                    if (stripos($stat['title'], 'artykuł') !== false) {
                        $icon = 'bi-file-earmark-text';
                        $cardColor = 'primary';
                    } elseif (stripos($stat['title'], 'kategor') !== false) {
                        $icon = 'bi-folder';
                        $cardColor = 'success';
                    } elseif (stripos($stat['title'], 'tag') !== false) {
                        $icon = 'bi-tag';
                        $cardColor = 'info';
                    } elseif (stripos($stat['title'], 'użytkowni') !== false) {
                        $icon = 'bi-people';
                        $cardColor = 'danger';
                    } elseif (stripos($stat['title'], 'stron') !== false) {
                        $icon = 'bi-file-earmark';
                        $cardColor = 'secondary';
                    } elseif (stripos($stat['title'], 'galer') !== false || stripos($stat['title'], 'zdjęć') !== false) {
                        $icon = 'bi-images';
                        $cardColor = 'warning';
                    } elseif (stripos($stat['title'], 'banner') !== false) {
                        $icon = 'bi-image';
                        $cardColor = 'dark';
                    } elseif (stripos($stat['title'], 'menu') !== false) {
                        $icon = 'bi-list-ul';
                        $cardColor = 'info';
                    }
                    ?>
                    <div class="d-flex justify-content-between mb-2">
                        <h6 class="card-subtitle text-muted"><?php echo htmlspecialchars($stat['title']); ?></h6>
                        <div class="rounded-circle bg-<?php echo $cardColor; ?> bg-opacity-10 p-2">
                            <i class="bi <?php echo $icon; ?> text-<?php echo $cardColor; ?>"></i>
                        </div>
                    </div>
                    <h2 class="display-5 fw-bold mb-3"><?php echo htmlspecialchars($stat['count']); ?></h2>
                    <a href="<?php echo htmlspecialchars($stat['link']); ?>" class="btn btn-sm btn-outline-<?php echo $cardColor; ?> w-100">
                        <i class="bi bi-arrow-right me-1"></i> Zarządzaj <?php echo htmlspecialchars($stat['title']); ?>
                    </a>
                </div>
            </div>
        </div>
    <?php endforeach; ?>
</div>

<div class="row g-4">
    <!-- Recent Articles Card -->
    <div class="col-lg-8">
        <div class="card shadow-sm border-0">
            <div class="card-header bg-white py-3">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0 fw-bold">
                        <i class="bi bi-clock-history me-2"></i>
                        Ostatnie <?php echo strtolower($lang['articles']); ?>
                    </h5>
                    <a href="/articles" class="btn btn-sm btn-outline-primary">
                        <i class="bi bi-grid me-1"></i> Zobacz wszystkie
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="list-group list-group-flush">
                    <?php if (!empty($recentArticles)): ?>
                        <?php foreach ($recentArticles as $article): ?>
                            <div class="list-group-item border-0 border-bottom p-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1 fw-semibold"><?php echo htmlspecialchars($article['title']); ?></h6>
                                        <div class="text-muted small d-flex align-items-center">
                                            <i class="bi bi-clock me-1"></i>
                                            <?php echo htmlspecialchars($article['updated_at'] ?? $article['created_at']); ?>

                                            <?php if (!empty($article['category_name'])): ?>
                                                <span class="mx-2">•</span>
                                                <i class="bi bi-folder me-1"></i>
                                                <?php echo htmlspecialchars($article['category_name'] ?? 'Bez kategorii'); ?>
                                            <?php endif; ?>

                                            <span class="mx-2">•</span>
                                            <span class="badge <?php echo $article['published'] ? 'bg-success' : 'bg-warning text-dark'; ?>">
                                                <?php echo $article['published'] ? 'Opublikowany' : 'Szkic'; ?>
                                            </span>
                                        </div>
                                    </div>
                                    <a href="/articles/<?php echo htmlspecialchars($article['id']); ?>" class="btn btn-sm btn-primary">
                                        <i class="bi bi-pencil-square"></i>
                                    </a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="p-4 text-center text-muted">
                            <i class="bi bi-file-earmark-x fs-3 d-block mb-2"></i>
                            <p>Brak ostatnich <?php echo strtolower($lang['articles']); ?>.</p>
                            <a href="/articles/new" class="btn btn-sm btn-primary">
                                <i class="bi bi-plus-circle me-1"></i> Utwórz pierwszy artykuł
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Links Card -->
    <div class="col-lg-4">
        <div class="card shadow-sm border-0">
            <div class="card-header bg-white py-3">
                <h5 class="mb-0 fw-bold">
                    <i class="bi bi-lightning-charge me-2"></i>
                    Szybkie akcje
                </h5>
            </div>
            <div class="card-body">
                <div class="list-group">
                    <a href="/articles/new" class="list-group-item list-group-item-action d-flex align-items-center">
                        <div class="me-3 p-2 rounded bg-primary bg-opacity-10">
                            <i class="bi bi-file-earmark-plus text-primary"></i>
                        </div>
                        <div>
                            <span class="fw-medium">Nowy artykuł</span>
                            <small class="d-block text-muted">Dodaj nowy wpis na stronę</small>
                        </div>
                    </a>
                    <a href="/pages/new" class="list-group-item list-group-item-action d-flex align-items-center">
                        <div class="me-3 p-2 rounded bg-success bg-opacity-10">
                            <i class="bi bi-file-earmark text-success"></i>
                        </div>
                        <div>
                            <span class="fw-medium">Nowa strona</span>
                            <small class="d-block text-muted">Utwórz stronę statyczną</small>
                        </div>
                    </a>
                    <a href="/gallery" class="list-group-item list-group-item-action d-flex align-items-center">
                        <div class="me-3 p-2 rounded bg-warning bg-opacity-10">
                            <i class="bi bi-images text-warning"></i>
                        </div>
                        <div>
                            <span class="fw-medium">Galeria</span>
                            <small class="d-block text-muted">Zarządzaj zdjęciami</small>
                        </div>
                    </a>
                    <a href="/categories" class="list-group-item list-group-item-action d-flex align-items-center">
                        <div class="me-3 p-2 rounded bg-info bg-opacity-10">
                            <i class="bi bi-folder text-info"></i>
                        </div>
                        <div>
                            <span class="fw-medium">Kategorie</span>
                            <small class="d-block text-muted">Organizuj treści</small>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../views/LayoutView.php';
?>