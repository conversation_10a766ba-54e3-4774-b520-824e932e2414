<?php
$translations = include __DIR__ . '/../../lang/pl.php';
$title = $translations['articles.title'] ?? 'Artykuły';
$current_page = 'articles';
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="page-title"><?php echo $translations['articles.title']; ?></h1>
    <a href="/articles/new" class="btn btn-primary">
        <i class="bi bi-file-earmark-plus me-1"></i> <?php echo $translations['articles.new_button']; ?>
    </a>
</div>

<?php if (isset($flash)): ?>
    <div class="alert alert-<?php echo $flash['type']; ?> alert-dismissible fade show shadow-sm" role="alert">
        <i class="bi bi-info-circle me-2"></i> <?php echo htmlspecialchars($flash['message']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<div class="card mb-4 shadow-sm">
    <div class="card-body">
        <form method="GET" action="/articles" class="row g-3 align-items-center mb-0">
            <div class="col-md-8">
                <div class="input-group">
                    <span class="input-group-text bg-light border-end-0">
                        <i class="bi bi-search text-muted"></i>
                    </span>
                    <input class="form-control border-start-0 ps-0" type="search"
                        placeholder="<?php echo $translations['articles.search_placeholder']; ?>"
                        aria-label="Search" name="search"
                        value="<?php echo htmlspecialchars($searchTerm ?? ''); ?>">
                </div>
            </div>
            <div class="col-md-4">
                <button class="btn btn-primary" type="submit">
                    <?php echo $translations['common.search']; ?>
                </button>
            </div>
        </form>
    </div>
</div>

<div class="card shadow-sm">
    <div class="table-responsive">
        <table class="table table-striped table-hover mb-0">
            <thead>
                <tr>
                    <th scope="col"><?php echo $translations['articles.table.title']; ?></th>
                    <th scope="col"><?php echo $translations['articles.table.category']; ?></th>
                    <th scope="col"><?php echo $translations['articles.table.tags']; ?></th>
                    <th scope="col"><?php echo $translations['articles.table.status']; ?></th>
                    <th scope="col"><?php echo $translations['articles.table.created_at']; ?></th>
                    <th scope="col" class="text-end"><?php echo $translations['articles.table.actions']; ?></th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($articles)): ?>
                    <tr>
                        <td colspan="6" class="text-center py-4">
                            <div class="text-muted">
                                <i class="bi bi-file-earmark-x fs-3 d-block mb-2"></i>
                                <?php echo $translations['articles.table.no_results']; ?>
                            </div>
                        </td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($articles as $article): ?>
                        <tr>
                            <td class="align-middle fw-medium">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-file-earmark-text me-2 text-primary"></i>
                                    <?php echo htmlspecialchars($article['title']); ?>
                                </div>
                            </td>
                            <td class="align-middle">
                                <?php if (!empty($article['category_name'])): ?>
                                    <span class="badge bg-light text-dark border">
                                        <i class="bi bi-folder me-1"></i> <?php echo htmlspecialchars($article['category_name']); ?>
                                    </span>
                                <?php else: ?>
                                    <span class="text-muted small">
                                        <i class="bi bi-dash-circle me-1"></i> Brak kategorii
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td class="align-middle">
                                <?php if (!empty($article['tags'])): ?>
                                    <div class="d-flex flex-wrap gap-1">
                                        <?php foreach ($article['tags'] as $tag): ?>
                                            <span class="badge bg-secondary rounded-pill">
                                                <i class="bi bi-tag-fill me-1"></i><?php echo htmlspecialchars($tag); ?>
                                            </span>
                                        <?php endforeach; ?>
                                    </div>
                                <?php else: ?>
                                    <span class="text-muted small">
                                        <i class="bi bi-dash-circle me-1"></i> <?php echo $translations['articles.table.no_tags']; ?>
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td class="align-middle">
                                <?php if ($article['published']): ?>
                                    <span class="badge bg-success">
                                        <i class="bi bi-check-circle me-1"></i> <?php echo $translations['articles.status.published']; ?>
                                    </span>
                                <?php else: ?>
                                    <span class="badge bg-warning text-dark">
                                        <i class="bi bi-pencil-square me-1"></i> <?php echo $translations['articles.status.draft']; ?>
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td class="align-middle">
                                <span class="text-muted small">
                                    <i class="bi bi-calendar3 me-1"></i> <?php echo htmlspecialchars($article['created_at']); ?>
                                </span>
                            </td>
                            <td class="align-middle text-end">
                                <div class="btn-group">
                                    <a href="/articles/<?php echo $article['id']; ?>" class="btn btn-sm btn-primary">
                                        <i class="bi bi-pencil-square"></i>
                                    </a>
                                    <a href="/articles/delete/<?php echo $article['id']; ?>"
                                        class="btn btn-sm btn-danger"
                                        onclick="return confirm('<?php echo $translations['articles.delete_confirm']; ?>');">
                                        <i class="bi bi-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../views/LayoutView.php';
?>