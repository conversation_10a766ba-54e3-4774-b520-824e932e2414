<?php
$lang = include __DIR__ . '/../../lang/pl.php';
$title = $lang['banners'];
$current_page = 'banners';
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="page-title"><?php echo $lang['banners']; ?></h1>
    <a href="/banners/new" class="btn btn-primary">
        <i class="bi bi-image-plus me-1"></i> Nowy <?php echo strtolower($lang['banners']); ?>
    </a>
</div>

<?php if (isset($flash)): ?>
    <div class="alert alert-<?php echo $flash['type']; ?> alert-dismissible fade show shadow-sm" role="alert">
        <i class="bi bi-info-circle me-2"></i> <?php echo htmlspecialchars($flash['message']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<div class="card mb-4 shadow-sm">
    <div class="card-body">
        <form method="GET" action="/banners" class="row g-3 align-items-center mb-0">
            <div class="col-md-8">
                <div class="input-group">
                    <span class="input-group-text bg-light border-end-0">
                        <i class="bi bi-search text-muted"></i>
                    </span>
                    <input class="form-control border-start-0 ps-0" type="search"
                        placeholder="Szukaj <?php echo strtolower($lang['banners']); ?>..."
                        aria-label="Search" name="search"
                        value="<?php echo htmlspecialchars($searchTerm ?? ''); ?>">
                </div>
            </div>
            <div class="col-md-4">
                <button class="btn btn-primary" type="submit">
                    <?php echo $lang['search']; ?>
                </button>
            </div>
        </form>
    </div>
</div>

<div class="card shadow-sm">
    <div class="table-responsive">
        <table class="table table-striped table-hover mb-0">
            <thead>
                <tr>
                    <th scope="col">Nazwa</th>
                    <th scope="col">Obraz</th>
                    <th scope="col">Status</th>
                    <th scope="col" class="text-end">Akcje</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($banners)): ?>
                    <tr>
                        <td colspan="4" class="text-center py-4">
                            <div class="text-muted">
                                <i class="bi bi-image fs-3 d-block mb-2"></i>
                                Nie znaleziono <?php echo strtolower($lang['banners']); ?>
                            </div>
                        </td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($banners as $banner): ?>
                        <tr>
                            <td class="align-middle fw-medium">
                                <i class="bi bi-image me-2 text-primary"></i>
                                <?php echo htmlspecialchars($banner['title'] ?? 'Bez tytułu'); ?>
                            </td>
                            <td class="align-middle">
                                <?php if (!empty($banner['image'])): ?>
                                    <div class="d-flex align-items-center">
                                        <div class="banner-thumbnail rounded border overflow-hidden me-2" style="width: 80px; height: 40px;">
                                            <img src="/uploads/banners/<?php echo htmlspecialchars($banner['image']); ?>"
                                                alt="<?php echo htmlspecialchars($banner['title'] ?? 'Banner'); ?>"
                                                class="w-100 h-100 object-fit-cover">
                                        </div>
                                        <a href="/uploads/banners/<?php echo htmlspecialchars($banner['image']); ?>"
                                            target="_blank"
                                            class="btn btn-sm btn-light">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                    </div>
                                <?php else: ?>
                                    <span class="text-muted">
                                        <i class="bi bi-image-alt me-1"></i> Brak obrazu
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td class="align-middle">
                                <?php if (isset($banner['active']) && $banner['active']): ?>
                                    <span class="badge bg-success">
                                        <i class="bi bi-check-circle me-1"></i> Aktywny
                                    </span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">
                                        <i class="bi bi-dash-circle me-1"></i> Nieaktywny
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td class="align-middle text-end">
                                <div class="btn-group">
                                    <a href="/banners/edit/<?php echo $banner['id']; ?>"
                                        class="btn btn-sm btn-primary">
                                        <i class="bi bi-pencil-square"></i>
                                    </a>
                                    <a href="/banners/toggle-active/<?php echo $banner['id']; ?>"
                                        class="btn btn-sm btn-warning">
                                        <?php if (isset($banner['active']) && $banner['active']): ?>
                                            <i class="bi bi-eye-slash"></i>
                                        <?php else: ?>
                                            <i class="bi bi-eye"></i>
                                        <?php endif; ?>
                                    </a>
                                    <a href="/banners/delete/<?php echo $banner['id']; ?>"
                                        class="btn btn-sm btn-danger"
                                        onclick="return confirm('<?php echo $lang['confirm_delete']; ?>');">
                                        <i class="bi bi-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../views/LayoutView.php';
?>