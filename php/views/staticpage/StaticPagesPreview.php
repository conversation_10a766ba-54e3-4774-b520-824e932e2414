<!DOCTYPE html>
<html lang="pl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Podgląd: <?php echo htmlspecialchars($staticPage['title']); ?></title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Optional: Add a CSS for rendering Markdown/HTML if needed -->
    <style>
        /* Basic styling for rendered content */
        .rendered-content img {
            max-width: 100%;
            height: auto;
        }
    </style>
</head>

<body>
    <div class="container mt-4">
        <h1><?php echo htmlspecialchars($staticPage['title']); ?></h1>
        <small class="text-muted">Ostatnia aktualizacja: <?php echo htmlspecialchars($staticPage['updated_at']); ?></small>
        <hr>
        <div class="rendered-content">
            <?php
            // In a real application, you would parse Markdown to HTML here
            // For simplicity, we'll just display raw content for now
            echo $staticPage['content'];
            ?>
        </div>
        <hr>
        <p><a href="/pages">&lt;&lt; Powrót do listy stron</a></p>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>

</html>