<?php

class Tag {
    private $db;

    public function __construct($dbConnection) {
        $this->db = $dbConnection;
    }

    public function getAllTags() {
        $stmt = $this->db->query("SELECT * FROM tags");
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }


    public function getTagById($id) {
        $stmt = $this->db->prepare("SELECT * FROM tags WHERE id = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function createTag($name, $slug) {
        $stmt = $this->db->prepare("INSERT INTO tags (name, slug) VALUES (:name, :slug)");
        $stmt->bindParam(':name', $name, PDO::PARAM_STR);
        $stmt->bindParam(':slug', $slug, PDO::PARAM_STR);
        return $stmt->execute();
    }

    public function updateTag($id, $name, $slug) {
        $stmt = $this->db->prepare("UPDATE tags SET name = :name, slug = :slug WHERE id = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->bindParam(':name', $name, PDO::PARAM_STR);
        $stmt->bindParam(':slug', $slug, PDO::PARAM_STR);
        return $stmt->execute();
    }

    public function deleteTag($id) {
        $stmt = $this->db->prepare("DELETE FROM tags WHERE id = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        return $stmt->execute();
    }

    public function getTagBySlug($slug) {
        $stmt = $this->db->prepare("SELECT * FROM tags WHERE slug = :slug");
        $stmt->bindParam(':slug', $slug, PDO::PARAM_STR);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function countAll() {
        $stmt = $this->db->query("SELECT COUNT(*) FROM tags");
        return $stmt->fetchColumn();
    }
}
