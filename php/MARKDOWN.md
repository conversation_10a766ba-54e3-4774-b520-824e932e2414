# Dokumentacja Simple CMS - Aplikacja PHP

## Spis treści
1. [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>](#wprowad<PERSON>ie)
2. [Wymagania systemowe](#wymagania-systemowe)
3. [Instalacja](#instalacja)
4. [Struktura aplikacji](#struktura-aplikacji)
5. [Funkcjonalności](#funkcjonalności)
6. [Upload plików w artykułach](#upload-plików-w-artykułach)
7. [API i endpointy](#api-i-endpointy)
8. [<PERSON><PERSON> danych](#baza-danych)
9. [Konfiguracja](#konfiguracja)
10. [Rozwiązywanie problemów](#rozwiązywanie-problemów)

## Wprowadzenie

Simple CMS to system zarządzania treścią napisany w PHP z wykorzystaniem SQLite jako bazy danych. Aplikacja umożliwia zarządzanie artykułami, kate<PERSON><PERSON><PERSON>, tagami, stronami statycznymi, galeriami, menu, bannerami i użytkownikami.

## Wymagania systemowe

- PHP 7.4 lub nowszy
- Rozszerzenia PHP:
  - PDO
  - SQLite
  - GD (dla przetwarzania obrazów)
  - fileinfo (dla wykrywania typów plików)
- Serwer web (Apache/Nginx) lub wbudowany serwer PHP
- Minimum 512MB RAM
- 100MB wolnego miejsca na dysku

## Instalacja

1. **Sklonuj lub pobierz kod aplikacji**
   ```bash
   git clone [repository-url]
   cd nowy-cms/php
   ```

2. **Ustaw uprawnienia do katalogów**
   ```bash
   chmod -R 755 public/uploads/
   chmod 644 database.sqlite
   ```

3. **Zainicjalizuj bazę danych**
   ```bash
   php init_db.php
   ```

4. **Uruchom serwer**
   ```bash
   cd public
   php -S localhost:8000
   ```

5. **Zaloguj się do panelu administracyjnego**
   - URL: `http://localhost:8000/login`
   - Login: `admin`
   - Hasło: `password` (domyślne hasło z skryptu inicjalizacji)

## Struktura aplikacji

```
php/
├── app/                    # Klasy podstawowe
│   ├── Controller.php      # Klasa bazowa kontrolerów
│   ├── Database.php        # Połączenie z bazą danych
│   ├── Router.php          # System routingu
│   ├── View.php           # System widoków
│   └── config.php         # Konfiguracja aplikacji
├── controllers/           # Kontrolery aplikacji
│   ├── ArticleController.php
│   ├── CategoryController.php
│   ├── TagController.php
│   └── ...
├── models/               # Modele danych
│   ├── Article.php
│   ├── Category.php
│   ├── Tag.php
│   └── ...
├── views/                # Widoki (templates)
│   ├── articles/
│   ├── categories/
│   ├── LayoutView.php    # Layout główny
│   └── ...
├── public/               # Pliki publiczne
│   ├── uploads/          # Katalog na przesłane pliki
│   ├── index.php         # Punkt wejścia aplikacji
│   └── .htaccess         # Konfiguracja Apache
├── lang/                 # Pliki językowe
├── database.sqlite       # Baza danych SQLite
├── init_db.php          # Skrypt inicjalizacji bazy
└── MARKDOWN.md          # Ta dokumentacja
```

## Funkcjonalności

### Zarządzanie artykułami
- Tworzenie, edycja i usuwanie artykułów
- **Upload wielu plików graficznych i załączników**
- Przypisywanie kategorii i tagów
- Harmonogramowanie publikacji
- Edytor Markdown z podglądem
- Wyszukiwanie artykułów

### Zarządzanie kategoriami
- Tworzenie kategorii z automatycznym generowaniem slug
- Edycja i usuwanie kategorii
- Licznik artykułów w kategorii

### Zarządzanie tagami
- Dodawanie tagów do artykułów
- Dynamiczne tworzenie nowych tagów
- Zarządzanie istniejącymi tagami

### Strony statyczne
- Tworzenie stron niezależnych od artykułów
- Edytor Markdown
- Podgląd stron przed publikacją

### Galeria
- Upload wielu obrazów jednocześnie
- Organizacja w kategorie
- Zarządzanie pozycją obrazów

### System menu
- Tworzenie wielu menu
- Hierarchiczne elementy menu
- Sortowanie pozycji

### Bannery
- Zarządzanie bannerami promocyjnymi
- Aktywacja/deaktywacja
- Pozycjonowanie

### Użytkownicy
- System ról (admin, editor)
- Zarządzanie kontami użytkowników
- Bezpieczne hasła (bcrypt)

## Upload plików w artykułach

### Nowa funkcjonalność uploadu

Aplikacja została rozszerzona o zaawansowany system uploadu plików dla artykułów, który obsługuje:

#### Typy plików
- **Obrazy**: JPG, PNG, GIF, WebP
- **Dokumenty**: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX
- **Archiwa**: ZIP, RAR, 7Z
- **Tekstowe**: TXT, CSV

#### Funkcje uploadu

1. **Upload wielu plików jednocześnie**
   - Możliwość wyboru wielu plików naraz
   - Automatyczne rozpoznawanie typu pliku
   - Podgląd przed zapisaniem

2. **Zarządzanie istniejącymi plikami**
   - Wyświetlanie wszystkich plików przypisanych do artykułu
   - Podgląd miniatur dla obrazów
   - Linki do pobrania dla dokumentów
   - Usuwanie pojedynczych plików

3. **Organizacja plików**
   - Automatyczne sortowanie według typu
   - Wyświetlanie rozmiaru plików
   - Ikony odpowiednie dla typu pliku

#### Struktura bazy danych dla plików

Nowa tabela `article_files`:
```sql
CREATE TABLE article_files (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    article_id INTEGER NOT NULL,
    filename TEXT NOT NULL,              -- Nazwa pliku na serwerze
    original_filename TEXT NOT NULL,     -- Oryginalna nazwa pliku
    filepath TEXT NOT NULL,              -- Ścieżka do pliku
    file_type TEXT NOT NULL,             -- 'image' lub 'attachment'
    mime_type TEXT,                      -- Typ MIME pliku
    file_size INTEGER,                   -- Rozmiar w bajtach
    position INTEGER DEFAULT 0,          -- Pozycja sortowania
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (article_id) REFERENCES articles(id) ON DELETE CASCADE
);
```

#### Metody modelu Article

Nowe metody w klasie `Article`:

- `uploadMultipleFiles($files, $articleId)` - Upload wielu plików
- `getArticleFiles($articleId, $fileType = null)` - Pobieranie plików artykułu
- `deleteArticleFile($fileId)` - Usuwanie pojedynczego pliku
- `saveFileToDatabase()` - Zapisywanie informacji o pliku w bazie

#### Bezpieczeństwo

- Walidacja typów plików po stronie serwera
- Unikalne nazwy plików (uniqid + oryginalna nazwa)
- Sprawdzanie rozmiaru plików
- Ochrona przed przesłaniem szkodliwych plików

## API i endpointy

### Artykuły
- `GET /articles` - Lista artykułów
- `GET /articles/new` - Formularz nowego artykułu
- `POST /articles/store` - Zapisanie nowego artykułu
- `GET /articles/{id}` - Edycja artykułu
- `POST /articles/update/{id}` - Aktualizacja artykułu
- `GET /articles/delete/{id}` - Usunięcie artykułu
- `POST /articles/files/delete/{fileId}` - **Usunięcie pliku artykułu (AJAX)**

### Kategorie
- `GET /categories` - Lista kategorii
- `GET /categories/new` - Formularz nowej kategorii
- `POST /categories/store` - Zapisanie kategorii
- `GET /categories/edit/{id}` - Edycja kategorii
- `POST /categories/update/{id}` - Aktualizacja kategorii
- `GET /categories/delete/{id}` - Usunięcie kategorii

### Pozostałe endpointy
- Tagi: `/tags/*`
- Strony statyczne: `/pages/*`
- Galeria: `/gallery/*`
- Menu: `/menus/*`
- Bannery: `/banners/*`
- Użytkownicy: `/users/*`

## Baza danych

### Główne tabele

#### articles
Przechowuje artykuły z podstawowymi informacjami.

#### article_files ⭐ NOWA
Przechowuje informacje o plikach przypisanych do artykułów.

#### categories
Kategorie artykułów.

#### tags
Tagi do oznaczania artykułów.

#### article_tags
Tabela łącząca artykuły z tagami (many-to-many).

#### static_pages
Strony statyczne niezależne od artykułów.

#### gallery_images
Obrazy w galerii.

#### menus i menu_items
System menu z hierarchią.

#### banners
Bannery promocyjne.

#### users
Użytkownicy systemu.

### Relacje

- `articles` ↔ `categories` (many-to-one)
- `articles` ↔ `tags` (many-to-many przez `article_tags`)
- `articles` ↔ `article_files` (one-to-many) ⭐ NOWA
- `menus` ↔ `menu_items` (one-to-many)
- `gallery_categories` ↔ `gallery_images` (one-to-many)

## Konfiguracja

### Ustawienia bazy danych
Plik: `app/Database.php`
```php
private $dbFile = BASE_PATH . '/database.sqlite';
```

### Ustawienia uploadu
Plik: `models/Article.php`
```php
private $uploadDir = BASE_PATH . '/public/uploads/articles/';
```

### Limity plików
Sprawdź ustawienia PHP:
- `upload_max_filesize` - maksymalny rozmiar pojedynczego pliku
- `post_max_size` - maksymalny rozmiar całego żądania POST
- `max_file_uploads` - maksymalna liczba plików w jednym żądaniu

## Rozwiązywanie problemów

### Naprawione błędy w wersji bieżącej ✅

#### 1. Błędy SQL w modelach
- **Gallery.php**: Zmieniono `gallery_category_id` na `category_id`, `uploaded_at` na `created_at`
- **Menu.php**: Zmieniono `location` na `slug`, `name` na `title` w menu_items, `order_index` na `position`
- **Banner.php**: Dopasowano do struktury tabeli (title, content, image, position, active)

#### 2. Błędy widoków (Undefined array key warnings)
- **MenusView.php**: Naprawiono odwołania do `$menu['location']` → `$menu['slug']`
- **BannersView.php**: Naprawiono odwołania do nieprawidłowych nazw kolumn
- Dodano obsługę null values w `htmlspecialchars()`

#### 3. Błędy kontrolerów
- Wszystkie kontrolery używają flash messages zamiast `echo`
- Prawidłowe przekierowania po błędach i sukcesach
- Ulepszona walidacja danych wejściowych

#### 4. Problemy z formularzami
- **MenusFormView.php**: Zmieniono `location` na `slug`
- **BannerFormView.php**: Dopasowano nazwy pól do struktury bazy danych

### Jak sprawdzić poprawność działania
Uruchom test: `php test_fixes.php`

### Problemy z uploadem plików

1. **Błąd "File too large"**
   - Zwiększ `upload_max_filesize` w php.ini
   - Zwiększ `post_max_size` w php.ini

2. **Pliki nie są zapisywane**
   - Sprawdź uprawnienia do katalogu `public/uploads/`
   - Upewnij się, że katalog istnieje

3. **Błąd podczas usuwania plików**
   - Sprawdź czy żądanie jest wysyłane jako AJAX
   - Sprawdź logi błędów PHP

### Problemy z bazą danych

1. **Błąd "Database locked"**
   - Sprawdź uprawnienia do pliku database.sqlite
   - Upewnij się, że nie ma otwartych połączeń

2. **Tabela nie istnieje**
   - Uruchom ponownie `php init_db.php`

### Problemy z routingiem

1. **404 Not Found**
   - Sprawdź konfigurację .htaccess
   - Upewnij się, że mod_rewrite jest włączony

### Debugowanie

Włącz wyświetlanie błędów w `public/index.php`:
```php
ini_set('display_errors', 1);
error_reporting(E_ALL);
```

Sprawdź logi błędów:
```bash
tail -f /var/log/apache2/error.log
# lub
tail -f /var/log/nginx/error.log
```

## Aktualizacje i rozwój

### Planowane funkcjonalności
- Optymalizacja obrazów podczas uploadu
- Galeria plików w edytorze Markdown
- Wersjonowanie artykułów
- System komentarzy
- API REST
- Wielojęzyczność

### Wkład w rozwój
1. Fork repozytorium
2. Utwórz branch dla nowej funkcjonalności
3. Dodaj testy
4. Wyślij Pull Request

---

**Wersja dokumentacji**: 2.0  
**Data ostatniej aktualizacji**: Grudzień 2024  
**Autor**: System CMS Team 