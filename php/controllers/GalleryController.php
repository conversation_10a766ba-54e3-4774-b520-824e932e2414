<?php

require_once BASE_PATH . '/app/Controller.php';
require_once BASE_PATH . '/models/Gallery.php';

class GalleryController extends Controller {
    private $galleryModel;

    public function __construct($dbConnection) {
        parent::__construct($dbConnection);
        $this->galleryModel = new Gallery($dbConnection);

        // Check if user is logged in for all actions in this controller
        // session_start(); // Session is already started in index.php for protected routes
        if (!isset($_SESSION['user_id'])) {
            // Redirect to login page if not authenticated
            header('Location: /login');
            exit();
        }
    }

    public function index() {
        $categories = $this->galleryModel->getAllCategories();
        $imageCounts = $this->galleryModel->getImageCountByCategoryId();
        
        // Dodaj liczbę zdjęć do każdej kategorii
        foreach ($categories as &$category) {
            $category['image_count'] = $imageCounts[$category['id']] ?? 0;
        }
        
        $flash = null;
        if (isset($_SESSION['flash'])) {
            $flash = $_SESSION['flash'];
            unset($_SESSION['flash']);
        }

        $this->renderView('gallery/GalleryIndexView', [
            'categories' => $categories,
            'flash' => $flash
        ]);
    }

    public function category($categoryId) {
        $category = $this->galleryModel->getCategoryById($categoryId);
        if (!$category) {
            $_SESSION['flash'] = ['type' => 'danger', 'message' => 'Nie znaleziono kategorii.'];
            header('Location: /gallery');
            exit();
        }
        
        $searchTerm = $_GET['search'] ?? '';
        
        if (!empty($searchTerm)) {
            $images = $this->galleryModel->searchImages($searchTerm, $categoryId);
        } else {
            $images = $this->galleryModel->getImagesByCategoryId($categoryId);
        }
        
        $flash = null;
        if (isset($_SESSION['flash'])) {
            $flash = $_SESSION['flash'];
            unset($_SESSION['flash']);
        }

        $this->renderView('gallery/GalleryCategoryView', [
            'category' => $category,
            'images' => $images,
            'searchTerm' => $searchTerm,
            'flash' => $flash
        ]);
    }

    public function createCategory() {
        $name = filter_input(INPUT_POST, 'name', FILTER_SANITIZE_STRING);
        // Basic slug generation server-side if not provided or empty
        $slug = filter_input(INPUT_POST, 'slug', FILTER_SANITIZE_STRING) ?? '';
        if (empty($slug) && !empty($name)) {
            $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $name), '-'));
        }

        // Basic validation
        if (empty($name) || empty($slug)) {
            $_SESSION['flash'] = ['type' => 'danger', 'message' => 'Nazwa kategorii i slug są wymagane.'];
            header('Location: /gallery');
            exit();
        }

        if ($this->galleryModel->createCategory($name, $slug)) {
            $_SESSION['flash'] = ['type' => 'success', 'message' => 'Kategoria została utworzona pomyślnie.'];
            header('Location: /gallery');
            exit();
        } else {
            $_SESSION['flash'] = ['type' => 'danger', 'message' => 'Błąd podczas tworzenia kategorii. Slug może nie być unikalny.'];
            header('Location: /gallery');
            exit();
        }
    }

    public function uploadImages($categoryId) {
        // Sprawdź czy kategoria istnieje
        $category = $this->galleryModel->getCategoryById($categoryId);
        if (!$category) {
            $_SESSION['flash'] = ['type' => 'danger', 'message' => 'Nie znaleziono kategorii.'];
            header('Location: /gallery');
            exit();
        }
        
        // Sprawdź czy pliki zostały przesłane
        if (!isset($_FILES['images'])) {
            $_SESSION['flash'] = ['type' => 'danger', 'message' => 'Nie wybrano żadnych plików do przesłania.'];
            header('Location: /gallery/category/' . $categoryId);
            exit();
        }

        $uploadedFiles = $_FILES['images'];
        $successfulUploads = 0;

        // Obsługa wielu plików (multiple attribute)
        if (is_array($uploadedFiles['name'])) {
            $fileCount = count($uploadedFiles['name']);
            for ($i = 0; $i < $fileCount; $i++) {
                // Pomiń puste pliki
                if (empty($uploadedFiles['name'][$i])) {
                    continue;
                }

                $file = [
                    'name' => $uploadedFiles['name'][$i],
                    'type' => $uploadedFiles['type'][$i],
                    'tmp_name' => $uploadedFiles['tmp_name'][$i],
                    'error' => $uploadedFiles['error'][$i],
                    'size' => $uploadedFiles['size'][$i],
                ];

                $uploadResult = $this->galleryModel->uploadImage($file);

                if ($uploadResult) {
                    $title = pathinfo($file['name'], PATHINFO_FILENAME);
                    $title = str_replace(['_', '-'], ' ', $title); // Zamień podkreślenia i myślniki na spacje

                    $this->galleryModel->createImage($title, $uploadResult['filename'], $uploadResult['filepath'], $categoryId);
                    $successfulUploads++;
                }
            }
        } else {
            // Obsługa pojedynczego pliku
            if (!empty($uploadedFiles['name'])) {
                $uploadResult = $this->galleryModel->uploadImage($uploadedFiles);

                if ($uploadResult) {
                    $title = pathinfo($uploadedFiles['name'], PATHINFO_FILENAME);
                    $title = str_replace(['_', '-'], ' ', $title);

                    $this->galleryModel->createImage($title, $uploadResult['filename'], $uploadResult['filepath'], $categoryId);
                    $successfulUploads++;
                }
            }
        }

        // Przekieruj z informacją o sukcesie
        if ($successfulUploads > 0) {
            $_SESSION['flash'] = ['type' => 'success', 'message' => "Pomyślnie przesłano {$successfulUploads} zdjęcie/zdjęć."];
            header('Location: /gallery/category/' . $categoryId);
            exit();
        } else {
            $_SESSION['flash'] = ['type' => 'danger', 'message' => 'Wystąpił błąd podczas przesyłania plików.'];
            header('Location: /gallery/category/' . $categoryId);
            exit();
        }
    }

    public function deleteImage($id) {
        $image = $this->galleryModel->getImageById($id);
        $categoryId = $image['category_id'] ?? null;
        
        if ($this->galleryModel->deleteImage($id)) {
            $_SESSION['flash'] = ['type' => 'success', 'message' => 'Zdjęcie zostało usunięte.'];
        } else {
            $_SESSION['flash'] = ['type' => 'danger', 'message' => 'Błąd podczas usuwania zdjęcia.'];
        }
        
        if ($categoryId) {
            header('Location: /gallery/category/' . $categoryId);
        } else {
            header('Location: /gallery');
        }
        exit();
    }

    public function deleteMultipleImages() {
        $ids = $_GET['ids'] ?? ''; // Assuming IDs are passed as a comma-separated string
        $categoryId = $_GET['category_id'] ?? null;
        $idArray = array_filter(explode(',', $ids), 'is_numeric'); // Filter out empty values and non-numeric IDs

        if (empty($idArray)) {
            $_SESSION['flash'] = ['type' => 'danger', 'message' => 'Nie wybrano żadnych zdjęć do usunięcia.'];
            if ($categoryId) {
                header('Location: /gallery/category/' . $categoryId);
            } else {
                header('Location: /gallery');
            }
            exit();
        }

        $deletedCount = 0;
        foreach ($idArray as $id) {
            if ($this->galleryModel->deleteImage($id)) {
                $deletedCount++;
            }
        }

        $_SESSION['flash'] = ['type' => 'success', 'message' => "Usunięto {$deletedCount} zdjęcie/zdjęć."];
        if ($categoryId) {
            header('Location: /gallery/category/' . $categoryId);
        } else {
            header('Location: /gallery');
        }
        exit();
    }

    public function editCategory($id) {
        $category = $this->galleryModel->getCategoryById($id);
        if (!$category) {
            $_SESSION['flash'] = ['type' => 'danger', 'message' => 'Nie znaleziono kategorii.'];
            header('Location: /gallery');
            exit();
        }
        
        $flash = null;
        if (isset($_SESSION['flash'])) {
            $flash = $_SESSION['flash'];
            unset($_SESSION['flash']);
        }

        $this->renderView('gallery/GalleryCategoryEditView', [
            'category' => $category,
            'flash' => $flash
        ]);
    }
    
    public function updateCategory($id) {
        $category = $this->galleryModel->getCategoryById($id);
        if (!$category) {
            $_SESSION['flash'] = ['type' => 'danger', 'message' => 'Nie znaleziono kategorii.'];
            header('Location: /gallery');
            exit();
        }
        
        $name = filter_input(INPUT_POST, 'name', FILTER_SANITIZE_STRING);
        $slug = filter_input(INPUT_POST, 'slug', FILTER_SANITIZE_STRING);

        // Basic validation
        if (empty($name) || empty($slug)) {
            $_SESSION['flash'] = ['type' => 'danger', 'message' => 'Nazwa kategorii i slug są wymagane.'];
            header('Location: /gallery/categories/edit/' . $id);
            exit();
        }

        if ($this->galleryModel->updateCategory($id, $name, $slug)) {
            $_SESSION['flash'] = ['type' => 'success', 'message' => 'Kategoria została zaktualizowana pomyślnie.'];
            header('Location: /gallery');
            exit();
        } else {
            $_SESSION['flash'] = ['type' => 'danger', 'message' => 'Błąd podczas aktualizacji kategorii. Slug może nie być unikalny.'];
            header('Location: /gallery/categories/edit/' . $id);
            exit();
        }
    }
    
    public function deleteCategory($id) {
        $category = $this->galleryModel->getCategoryById($id);
        if (!$category) {
            $_SESSION['flash'] = ['type' => 'danger', 'message' => 'Nie znaleziono kategorii.'];
            header('Location: /gallery');
            exit();
        }
        
        // Usuń wszystkie zdjęcia w kategorii
        $images = $this->galleryModel->getImagesByCategoryId($id);
        foreach ($images as $image) {
            $this->galleryModel->deleteImage($image['id']);
        }
        
        if ($this->galleryModel->deleteCategory($id)) {
            $_SESSION['flash'] = ['type' => 'success', 'message' => 'Kategoria i wszystkie jej zdjęcia zostały usunięte.'];
        } else {
            $_SESSION['flash'] = ['type' => 'danger', 'message' => 'Błąd podczas usuwania kategorii.'];
        }
        
        header('Location: /gallery');
        exit();
    }

    // TODO: Add methods for editing image title/category
}

