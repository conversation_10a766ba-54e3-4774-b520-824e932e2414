<?php

require_once BASE_PATH . '/app/Controller.php';
require_once BASE_PATH . '/models/User.php';

class AuthController extends Controller {
    private $userModel;

    public function __construct($dbConnection) {
        parent::__construct($dbConnection);
        $this->userModel = new User($dbConnection);
    }

    public function loginForm() {
        // Check if user is already logged in
        if (isset($_SESSION['user_id'])) {
            header('Location: /'); // Redirect to dashboard if logged in
            exit();
        }

        // TODO: Pass potential error messages to the view
        $this->renderView('auth/AuthView');
    }

    public function login() {
        $username = filter_input(INPUT_POST, 'username', FILTER_SANITIZE_STRING);
        $password = $_POST['password'] ?? ''; // Password should not be sanitized like a string

        // Basic validation
        if (empty($username) || empty($password)) {
            // TODO: Redirect back to login form with error message
            echo "Username and password are required.";
            return;
        }

        $user = $this->userModel->getUserByUsername($username);

        if ($user && $this->userModel->checkPassword($password, $user['password'])) {
            // Authentication successful
            if (session_status() === PHP_SESSION_NONE) {
                session_start();
            }
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['user_role'] = $user['role']; // Store user role in session

            header('Location: /'); // Redirect to dashboard
            exit();
        } else {
            // Authentication failed
            // TODO: Redirect back to login form with error message
            echo "Invalid username or password.";
            // Redirect back to login with error (basic)
            header('Location: /login?error=invalid_credentials');
            exit();
        }
    }

    public function logout() {
        session_start(); // Ensure session is started
        session_unset(); // Unset all session variables
        session_destroy(); // Destroy the session

        header('Location: /login'); // Redirect to login page
        exit();
    }
}
