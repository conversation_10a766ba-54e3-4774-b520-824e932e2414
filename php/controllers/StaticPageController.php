<?php

require_once BASE_PATH . '/app/Controller.php';
require_once BASE_PATH . '/models/StaticPage.php';

class StaticPageController extends Controller {
    private $staticPageModel;

    public function __construct($dbConnection) {
        parent::__construct($dbConnection);
        $this->staticPageModel = new StaticPage($dbConnection);

        // Check if user is logged in for all actions except 'preview'
        // session_start(); // Session is already started in index.php for protected routes

        // Get the requested action from the URL (simple approach)
        $requestUri = trim($_SERVER['REQUEST_URI'], '/');
        $uriSegments = explode('/', $requestUri);
        $action = $uriSegments[1] ?? 'index'; // Default action is 'index'

        if ($action !== 'preview' && !isset($_SESSION['user_id'])) {
            // Redirect to login page if not authenticated
            header('Location: /login');
            exit();
        }
    }

    public function index() {
        $staticPages = $this->staticPageModel->getAllStaticPages();

        $this->renderView('StaticPagesView', [
            'staticPages' => $staticPages
        ]);
    }

    public function form($id = null) {
        $staticPage = null;
        $existingImages = [];
        $existingFiles = [];
        
        if ($id) {
            $staticPage = $this->staticPageModel->getStaticPageById($id);
            if (!$staticPage) {
                http_response_code(404);
                echo "Static Page not found.";
                return;
            }
            
            // Get existing files
            $existingImages = $this->staticPageModel->getStaticPageFiles($id, 'image');
            $existingFiles = $this->staticPageModel->getStaticPageFiles($id, 'attachment');
        }

        $this->renderView('StaticPagesFormView', [
            'staticPage' => $staticPage,
            'existingImages' => $existingImages,
            'existingFiles' => $existingFiles,
            'current_page' => 'pages',
            'title' => isset($staticPage) ? 'Edytuj Stronę Statyczną' : 'Dodaj Nową Stronę Statyczną'
        ]);
    }

    public function store() {
        // Basic validation and sanitization (using same logic as ArticleController)
        $title = trim(filter_input(INPUT_POST, 'title', FILTER_SANITIZE_FULL_SPECIAL_CHARS) ?? '');
        $slug = trim(filter_input(INPUT_POST, 'slug', FILTER_SANITIZE_FULL_SPECIAL_CHARS) ?? '');
        $content = filter_input(INPUT_POST, 'content', FILTER_UNSAFE_RAW);
        $published = isset($_POST['published']) ? 1 : 0;

        // Debug: log the sanitized values
        error_log("=== StaticPageController store method ===");
        error_log("Raw POST data: " . print_r($_POST, true));
        error_log("Sanitized title: '$title'");
        error_log("Sanitized slug: '$slug'");
        error_log("Content length: " . strlen($content ?? ''));

        // Basic validation
        if (empty($title) || empty($slug)) {
            error_log("Validation failed - title or slug is empty");
            $this->setFlashMessage('Tytuł i slug są wymagane', 'error');
            header('Location: /pages/new');
            return;
        }

        // Check if slug is unique
        if ($this->staticPageModel->getStaticPageBySlug($slug)) {
            $this->setFlashMessage('Slug musi być unikalny', 'error');
            header('Location: /pages/new');
            return;
        }

        if ($this->staticPageModel->createStaticPage($title, $slug, $content, $published)) {
            $staticPageId = $this->db->lastInsertId();

            // Handle multiple file uploads (images and attachments)
            if (!empty($_FILES['files']['tmp_name'][0]) || !empty($_FILES['images']['tmp_name'][0])) {
                // Upload regular files
                if (!empty($_FILES['files']['tmp_name'][0])) {
                    $this->staticPageModel->uploadMultipleFiles($_FILES['files'], $staticPageId);
                }
                
                // Upload images
                if (!empty($_FILES['images']['tmp_name'][0])) {
                    $this->staticPageModel->uploadMultipleFiles($_FILES['images'], $staticPageId);
                }
            }

            $this->setFlashMessage('Strona statyczna utworzona pomyślnie!', 'success');
            header('Location: /pages');
        } else {
            $this->setFlashMessage('Wystąpił błąd podczas tworzenia strony statycznej', 'error');
            header('Location: /pages/new');
        }
    }

    public function update($id) {
        error_log("=== StaticPageController update method called for ID: $id ===");
        error_log("POST data: " . print_r($_POST, true));
        error_log("FILES data: " . print_r($_FILES, true));
        
        // Basic validation and sanitization (using same logic as ArticleController)
        $title = trim(filter_input(INPUT_POST, 'title', FILTER_SANITIZE_FULL_SPECIAL_CHARS) ?? '');
        if (empty($title)) {
            error_log("Title validation failed: empty title");
            $this->setFlashMessage('Tytuł strony jest wymagany', 'error');
            header('Location: /pages/' . $id);
            exit();
        }

        $slug = trim(filter_input(INPUT_POST, 'slug', FILTER_SANITIZE_FULL_SPECIAL_CHARS) ?? '');
        if (empty($slug)) {
            error_log("Slug validation failed: empty slug");
            $this->setFlashMessage('Slug strony jest wymagany', 'error');
            header('Location: /pages/' . $id);
            exit();
        }
        
        error_log("Validation passed: title='$title', slug='$slug'");

        $content = filter_input(INPUT_POST, 'content', FILTER_UNSAFE_RAW);
        $published = isset($_POST['published']) ? 1 : 0;

        // Check if slug is unique, excluding the current page
        $existingPage = $this->staticPageModel->getStaticPageBySlug($slug);
        if ($existingPage && $existingPage['id'] != $id) {
            $this->setFlashMessage('Slug musi być unikalny', 'error');
            header('Location: /pages/' . $id);
            return;
        }

        // Handle multiple file uploads (images and attachments)
        error_log("StaticPageController update: Processing files for static page ID: $id");
        error_log("FILES array: " . print_r($_FILES, true));
        
        if (!empty($_FILES['files']['tmp_name'][0]) || !empty($_FILES['images']['tmp_name'][0])) {
            error_log("Files detected for upload");
            
            // Upload regular files
            if (!empty($_FILES['files']['tmp_name'][0])) {
                error_log("Uploading regular files");
                $this->staticPageModel->uploadMultipleFiles($_FILES['files'], $id);
            }
            
            // Upload images
            if (!empty($_FILES['images']['tmp_name'][0])) {
                error_log("Uploading images");
                $this->staticPageModel->uploadMultipleFiles($_FILES['images'], $id);
            }
        } else {
            error_log("No files detected for upload");
        }

        if ($this->staticPageModel->updateStaticPage($id, $title, $slug, $content, $published)) {
            $this->setFlashMessage('Strona statyczna zaktualizowana pomyślnie!', 'success');
            header('Location: /pages/' . $id);
        } else {
            $this->setFlashMessage('Wystąpił błąd podczas aktualizacji strony statycznej', 'error');
            header('Location: /pages/' . $id);
        }
    }

    public function delete($id) {
        if ($this->staticPageModel->deleteStaticPage($id)) {
            $this->setFlashMessage('Strona statyczna usunięta pomyślnie!', 'success');
            header('Location: /pages');
        } else {
            $this->setFlashMessage('Wystąpił błąd podczas usuwania strony statycznej', 'error');
            header('Location: /pages');
        }
    }

    public function togglePublished($id) {
        $staticPage = $this->staticPageModel->getStaticPageById($id);
        if (!$staticPage) {
            http_response_code(404);
            echo "Static Page not found.";
            return;
        }

        $newStatus = $staticPage['published'] ? 0 : 1;

        if ($this->staticPageModel->updatePublishedStatus($id, $newStatus)) {
            header('Location: /pages');
        } else {
            // Handle error
            echo "Error updating published status.";
        }
    }

    public function preview($slug) {
        $staticPage = $this->staticPageModel->getStaticPageBySlug($slug);
        if (!$staticPage || !$staticPage['published']) {
            http_response_code(404);
            echo "Static Page not found or not published.";
            return;
        }

        // In a real application, you might use a different layout/view for frontend preview
        $this->renderView('StaticPagesPreview', [
            'staticPage' => $staticPage
        ]);
    }

    public function deleteFile($fileId) {
        header('Content-Type: application/json');
        
        if ($this->staticPageModel->deleteStaticPageFile($fileId)) {
            echo json_encode(['success' => true]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Nie udało się usunąć pliku']);
        }
        exit;
    }

    public function updateFilePositions() {
        header('Content-Type: application/json');
        
        // Get JSON input
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!isset($input['positions']) || !is_array($input['positions'])) {
            echo json_encode(['success' => false, 'message' => 'Nieprawidłowe dane pozycji']);
            exit;
        }
        
        if ($this->staticPageModel->updateFilePositions($input['positions'])) {
            echo json_encode(['success' => true]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Nie udało się zaktualizować pozycji plików']);
        }
        exit;
    }
}
