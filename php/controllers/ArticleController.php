<?php

require_once BASE_PATH . '/app/Controller.php';
require_once BASE_PATH . '/models/Article.php';
require_once BASE_PATH . '/models/Category.php'; // Need Category model for filter/display
require_once BASE_PATH . '/models/Tag.php'; // Need Tag model for filter/display

class ArticleController extends Controller {
    private $articleModel;
    private $categoryModel;
    private $tagModel;

    public function __construct($dbConnection) {
        parent::__construct($dbConnection);
        $this->articleModel = new Article($dbConnection);
        $this->categoryModel = new Category($dbConnection);
        $this->tagModel = new Tag($dbConnection);

        // Check if user is logged in for all actions in this controller
        // session_start(); // Session is already started in index.php for protected routes
        if (!isset($_SESSION['user_id'])) {
            // Redirect to login page if not authenticated
            header('Location: /login');
            exit();
        }
    }

    public function index() {
        $searchTerm = $_GET['search'] ?? ''; // Get search term from query parameter

        if (!empty($searchTerm)) {
            $articles = $this->articleModel->searchArticles($searchTerm);
        } else {
            $articles = $this->articleModel->getAllArticles();
        }

        // For each article, fetch associated tags
        foreach ($articles as &$article) {
            $article['tags'] = $this->articleModel->getTagsForArticle($article['id']);
        }
        unset($article); // Unset reference

        // We might also need categories for filtering in the future, fetch them here
        $categories = $this->categoryModel->getAllCategories(); // Need getAllCategories method in Category model

        $this->renderView('articles/ArticlesView', [
            'articles' => $articles,
            'searchTerm' => $searchTerm,
            'categories' => $categories // Pass categories to the view
        ]);
    }

    public function create() {
        $categories = $this->categoryModel->getAllCategories();
        $tags = $this->tagModel->getAllTags();

        $this->renderView('articles/ArticlesFormView', [
            'article' => null, // No article data for creation
            'categories' => $categories,
            'tags' => $tags,
            'articleTagIds' => [],
            'existingImages' => [],
            'existingFiles' => []
        ]);
    }

    public function store() {
        // Basic validation and sanitization
        $title = filter_input(INPUT_POST, 'title', FILTER_SANITIZE_STRING);
        $slug = filter_input(INPUT_POST, 'slug', FILTER_SANITIZE_STRING);
        $excerpt = filter_input(INPUT_POST, 'excerpt', FILTER_UNSAFE_RAW); // Rich text excerpt
        $content = filter_input(INPUT_POST, 'content', FILTER_UNSAFE_RAW); // Rich text content, be cautious
        $categoryId = filter_input(INPUT_POST, 'category_id', FILTER_VALIDATE_INT);
        $published = isset($_POST['published']) ? 1 : 0;
        $publishedStart = !empty($_POST['published_start']) ? $_POST['published_start'] : null;
        $publishedEnd = !empty($_POST['published_end']) ? $_POST['published_end'] : null;
        $selectedTags = $_POST['tags'] ?? []; // Array of tag IDs

        if ($this->articleModel->createArticle($title, $slug, $excerpt, $content, $categoryId, $published, $publishedStart, $publishedEnd)) {
            $articleId = $this->db->lastInsertId();

            // Handle multiple file uploads (images and attachments)
            if (!empty($_FILES['files']['tmp_name'][0]) || !empty($_FILES['images']['tmp_name'][0])) {
                // Upload regular files
                if (!empty($_FILES['files']['tmp_name'][0])) {
                    $this->articleModel->uploadMultipleFiles($_FILES['files'], $articleId);
                }
                
                // Upload images
                if (!empty($_FILES['images']['tmp_name'][0])) {
                    $this->articleModel->uploadMultipleFiles($_FILES['images'], $articleId);
                }
            }

            // Link tags to the article
            $this->articleModel->syncTags($articleId, $selectedTags);

            $this->setFlashMessage('Artykuł utworzony pomyślnie!', 'success');
            header('Location: /articles');
        } else {
            // Handle error
            $this->setFlashMessage('Wystąpił błąd podczas tworzenia artykułu', 'error');
            header('Location: /articles/new');
        }
    }

    public function edit($id) {
        $article = $this->articleModel->getArticleById($id);
        if (!$article) {
            http_response_code(404);
            echo "Article not found.";
            return;
        }

        $categories = $this->categoryModel->getAllCategories();
        $tags = $this->tagModel->getAllTags();
        $articleTags = $this->articleModel->getTagsForArticle($id);

        // Convert tag names array to IDs for checking in the form
        $articleTagNames = $this->articleModel->getTagsForArticle($id);
        $articleTagIds = [];
        $allTags = $this->tagModel->getAllTags();
        foreach ($articleTagNames as $tagName) {
            foreach ($allTags as $tag) {
                if ($tag['name'] === $tagName) {
                    $articleTagIds[] = $tag['id'];
                    break;
                }
            }
        }

        // Get existing files for the article
        $existingImages = $this->articleModel->getArticleFiles($id, 'image');
        $existingFiles = $this->articleModel->getArticleFiles($id, 'attachment');

        $this->renderView('articles/ArticlesFormView', [
            'article' => $article,
            'categories' => $categories,
            'tags' => $tags,
            'articleTagIds' => $articleTagIds,
            'existingImages' => $existingImages,
            'existingFiles' => $existingFiles
        ]);
    }

    public function update($id) {
        error_log("=== ArticleController update method called for ID: $id ===");
        error_log("POST data: " . print_r($_POST, true));
        error_log("FILES data: " . print_r($_FILES, true));
        
        // Basic validation and sanitization
        $title = trim(filter_input(INPUT_POST, 'title', FILTER_SANITIZE_FULL_SPECIAL_CHARS) ?? '');
        if (empty($title)) {
            error_log("Title validation failed: empty title");
            $this->setFlashMessage('Tytuł artykułu jest wymagany', 'error');
            header('Location: /articles/' . $id);
            exit();
        }

        $slug = trim(filter_input(INPUT_POST, 'slug', FILTER_SANITIZE_FULL_SPECIAL_CHARS) ?? '');
        if (empty($slug)) {
            error_log("Slug validation failed: empty slug");
            $this->setFlashMessage('Slug artykułu jest wymagany', 'error');
            header('Location: /articles/' . $id);
            exit();
        }
        
        error_log("Validation passed: title='$title', slug='$slug'");

        $excerpt = filter_input(INPUT_POST, 'excerpt', FILTER_UNSAFE_RAW);
        $content = filter_input(INPUT_POST, 'content', FILTER_UNSAFE_RAW);
        $categoryId = filter_input(INPUT_POST, 'category_id', FILTER_VALIDATE_INT);
        $published = isset($_POST['published']) ? 1 : 0;
        $publishedStart = !empty($_POST['published_start']) ? $_POST['published_start'] : null;
        $publishedEnd = !empty($_POST['published_end']) ? $_POST['published_end'] : null;
        $selectedTags = $_POST['tags'] ?? [];

        // Handle multiple file uploads (images and attachments)
        error_log("ArticleController update: Processing files for article ID: $id");
        error_log("FILES array: " . print_r($_FILES, true));
        
        if (!empty($_FILES['files']['tmp_name'][0]) || !empty($_FILES['images']['tmp_name'][0])) {
            error_log("Files detected for upload");
            
            // Upload regular files
            if (!empty($_FILES['files']['tmp_name'][0])) {
                error_log("Uploading regular files");
                $this->articleModel->uploadMultipleFiles($_FILES['files'], $id);
            }
            
            // Upload images
            if (!empty($_FILES['images']['tmp_name'][0])) {
                error_log("Uploading images");
                $this->articleModel->uploadMultipleFiles($_FILES['images'], $id);
            }
        } else {
            error_log("No files detected for upload");
        }

        if ($this->articleModel->updateArticle($id, $title, $slug, $excerpt, $content, $categoryId, $published, $publishedStart, $publishedEnd)) {
            // Sync tags for the article
            $this->articleModel->syncTags($id, $selectedTags);

            $this->setFlashMessage('Artykuł zaktualizowany pomyślnie!', 'success');
            header('Location: /articles/' . $id);
        } else {
            // Handle error
            $this->setFlashMessage('Wystąpił błąd podczas aktualizacji artykułu', 'error');
            header('Location: /articles/' . $id);
        }
    }

    public function delete($id) {
        if ($this->articleModel->deleteArticle($id)) {
            $this->setFlashMessage('Artykuł usunięty pomyślnie!', 'success');
            header('Location: /articles');
        } else {
            // Handle error
            // TODO: Set error flash message and redirect back
            echo "Error deleting article.";
        }
    }

    public function deleteFile($fileId) {
        header('Content-Type: application/json');
        
        if ($this->articleModel->deleteArticleFile($fileId)) {
            echo json_encode(['success' => true]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Nie udało się usunąć pliku']);
        }
        exit;
    }

    public function updateFilePositions() {
        header('Content-Type: application/json');
        
        // Get JSON input
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!isset($input['positions']) || !is_array($input['positions'])) {
            echo json_encode(['success' => false, 'message' => 'Nieprawidłowe dane pozycji']);
            exit;
        }
        
        if ($this->articleModel->updateFilePositions($input['positions'])) {
            echo json_encode(['success' => true]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Nie udało się zaktualizować pozycji plików']);
        }
        exit;
    }
}
