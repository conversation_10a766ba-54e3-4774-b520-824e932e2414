# Dokumentacja Aplikacji PHP Simple CMS

Ten dokument opisuje stan aplikacji PHP Simple CMS po migracji z React/Tailwind.

## Architektura

Aplikacja jest zbudowana w oparciu o wzorzec MVC (Model-View-Controller).

-   **public/**: Katalog dostępny publicznie, zawiera plik wejściowy `index.php`.
-   **app/**: Zawiera główne klasy aplikacji i konfigurację (Router, Controller, View, Database, config).
-   **controllers/**: Zawiera klasy kontrolerów obsługujących logikę żądań.
-   **models/**: Zawiera klasy modeli do interakcji z bazą danych.
-   **views/**: Zawiera pliki widoków (szablony HTML z osadzonym PHP) do prezentacji danych.
-   **database.sqlite**: Plik bazy danych SQLite.

## Baza Danych

Aplikacja korzysta z bazy danych SQLite. Struktura tabel została utworzona na podstawie analizy aplikacji React i obejmuje (wstępnie):

-   `articles`: Przechowuje dane artykułów.
-   `categories`: Przechowuje dane kategorii.
-   `tags`: Przechowuje dane tagów.
-   `article_tags`: Tabela łącząca artykuły z tagami (wiele do wielu).
-   `static_pages`: Przechowuje dane stron statycznych.
-   `menus`: Przechowuje dane menu nawigacyjnych.
-   `menu_items`: Przechowuje dane elementów menu.
-   `gallery_categories`: Przechowuje dane kategorii galerii zdjęć.
-   `gallery_images`: Przechowuje dane zdjęć w galerii.
-   `users`: Przechowuje dane użytkowników.
-   `banners`: Przechowuje dane banerów.

Skrypt do utworzenia tabel znajduje się w `app/database_setup.php` i został już wykonany.

## Router

Router (`app/Router.php`) odpowiada za mapowanie przychodzących żądań URL na odpowiednie metody kontrolerów. Obsługuje metody HTTP GET i proste ścieżki z opcjonalnymi parametrami w nawiasach klamrowych `{}`.

## Kontroler Bazowy

Kontroler bazowy (`app/Controller.php`) dostarcza podstawowe funkcjonalności dla wszystkich kontrolerów, w tym dostęp do obiektu bazy danych (`$this->db`) oraz metodę do renderowania widoków (`renderView`).

Metoda `renderView()` wyszukuje pliki widoków w następującej kolejności:
1. Najpierw w podkatalogu odpowiadającym nazwie kontrolera (np. `views/gallery/` dla `GalleryController`)
2. Jeśli nie znajdzie, szuka w głównym katalogu `views/`

Przykład dla GalleryController:
- Wywołanie `$this->renderView('GalleryView', $data)` najpierw spróbuje załadować `views/gallery/GalleryView.php`
- Jeśli plik nie istnieje, spróbuje załadować `views/GalleryView.php`

## Widok Bazowy

Klasa Widoku (`app/View.php`) jest obecna w strukturze, ale jej główna rola w obecnej implementacji polega na inkludowaniu plików widoków przez kontroler bazowy.

## Standardy Widoków

Wszystkie widoki list (np. ArticlesView, CategoriesView) powinny:
- Używać spójnego układu z nagłówkiem, przyciskiem dodawania i tabelą danych
- Implementować system powiadomień/flash messages
- Zawierać funkcjonalność wyszukiwania
- Używać tłumaczeń z plików językowych
- Stosować spójne style Bootstrap 5 zgodnie z systemem grid:
  - Główne kontenery: `container` lub `container-fluid`
  - Układ wierszy i kolumn: `row` z `col-*` (np. `col-md-6`)
  - Tabele z klasami `table table-striped table-hover`
  - Przyciski z klasami `btn btn-primary`, `btn btn-secondary` itp.
  - Formularze z klasami `mb-3` dla odstępów i `row g-2` dla układu
  - Nawigacja z klasami `nav nav-pills flex-column` dla sidebarów

Widoki formularzy (np. ArticlesFormView, CategoriesFormView) powinny:
- Zawierać walidację po stronie klienta
- Używać spójnego układu formularza
- Implementować system powiadomień błędów
- Stosować standardowe style Bootstrap 5 dla formularzy

## Migracja Modułu: Artykuły

Pierwszym migrowanym modułem są Artykuły.

-   **Model (`models/Article.php`):** Zawiera metody do operacji CRUD (tworzenie, pobieranie, aktualizacja, usuwanie) na tabeli `articles`, a także metody do zarządzania powiązaniami artykułów z tagami (`syncTags`), pobierania tagów dla danego artykułu (`getTagsForArticle`) i podstawową metodę wyszukiwania (`searchArticles`).
-   **Kontroler (`controllers/ArticleController.php`):** Obsługuje żądania dotyczące artykułów:
    -   `/articles` (GET): Wyświetla listę artykułów z wyszukiwaniem i filtrowaniem.
    -   `/articles/new` (GET): Wyświetla formularz dodawania nowego artykułu. Pobiera listy kategorii i tagów z modeli.
    -   `/articles/store` (POST): Przetwarza dane z formularza dodawania i tworzy nowy artykuł wraz z powiązaniami tagów.
    -   `/articles/{id}` (GET): Wyświetla formularz edycji artykułu o podanym ID. Pobiera dane artykułu, kategorii, tagów i zaznaczonych tagów dla tego artykułu.
    -   `/articles/update/{id}` (POST): Przetwarza dane z formularza edycji i aktualizuje istniejący artykuł wraz z powiązaniami tagów.
    -   `/articles/delete/{id}` (GET): Usuwa artykuł o podanym ID. (Uwaga: Użycie metody GET dla usuwania nie jest najlepszą praktyką; w pełnej aplikacji lepiej użyć POST lub DELETE).
    -   Wykorzystuje Modele `Category` i `Tag` do pobierania danych dla formularzy i widoku listy.
-   **Widok listy (`views/articles/index.php`):** Wyświetla listę artykułów w tabeli, korzystając ze stylów Bootstrap 5. Zawiera pole wyszukiwania, link do dodawania nowego artykułu oraz linki do edycji i usuwania dla każdego artykułu.
-   **Widok formularza (`views/articles/form.php`):** Używany zarówno do dodawania, jak i edycji artykułów. Zawiera pola na tytuł, slug, treść (z edytorem SimpleMDE), wybór kategorii (dropdown), wybór tagów (checkboxy) oraz status publikacji (checkbox). Wykorzystuje Bootstrap 5 do stylizacji.

## Migracja Modułu: Kategorie

-   **Model (`models/Category.php`):** Zawiera metody do operacji CRUD (tworzenie, pobieranie, aktualizacja, usuwanie) na tabeli `categories`, a także metodę do pobierania kategorii po slugu (`getCategoryBySlug`).
-   **Kontroler (`controllers/CategoryController.php`):** Obsługuje żądania dotyczące kategorii:
    -   `/categories` (GET): Wyświetla listę kategorii. (TODO: Dodać liczbę artykułów dla każdej kategorii).
    -   `/categories/new` (GET): Wyświetla formularz dodawania nowej kategorii.
    -   `/categories/store` (POST): Przetwarza dane z formularza dodawania i tworzy nową kategorię. Zawiera podstawową walidację nazw i slugów (unikalność sluga).
    -   `/categories/edit/{id}` (GET): Wyświetla formularz edycji kategorii o podanym ID.
    -   `/categories/update/{id}` (POST): Przetwarza dane z formularza edycji i aktualizuje istniejącą kategorię. Zawiera podstawową walidację nazw i slugów (unikalność sluga, z wyłączeniem edytowanej kategorii).
    -   `/categories/delete/{id}` (GET): Usuwa kategorię o podanym ID. (Uwaga: Użycie metody GET dla usuwania nie jest najlepszą praktyką; w pełnej aplikacji lepiej użyć POST lub DELETE. TODO: Rozważyć obsługę artykułów powiązanych z usuwaną kategorią).
-   **Widok listy (`views/categories/index.php`):** Wyświetla kategorie w tabeli Bootstrap 5, z linkami do edycji i usuwania. Brakuje jeszcze wyświetlania liczby artykułów.
-   **Widok formularza (`views/categories/form.php`):** Używany do dodawania i edycji kategorii. Posiada pola na nazwę i slug, z auto-generowaniem sluga na podstawie nazwy (client-side JS).

## Migracja Modułu: Pulpit

-   **Kontroler (`controllers/HomeController.php`):** Obsługuje żądanie wyświetlenia strony głównej (`/`).
-   **Widok (`views/dashboard.php`):** Prosty widok strony głównej z powitaniem i linkiem do listy artykułów, stylizowany Bootstrapem 5.

## Migracja Modułu: Tagi

-   **Model (`models/Tag.php`):** Zawiera metody do operacji CRUD (tworzenie, pobieranie, aktualizacja, usuwanie) na tabeli `tags`, a także metodę do pobierania tagu po slugu (`getTagBySlug`).
-   **Kontroler (`controllers/TagController.php`):** Obsługuje żądania dotyczące tagów:
    -   `/tags` (GET): Wyświetla listę tagów. (TODO: Dodać liczbę artykułów dla każdego tagu).
    -   `/tags/new` (GET): Wyświetla formularz dodawania nowego tagu.
    -   `/tags/store` (POST): Przetwarza dane z formularza dodawania i tworzy nowy tag. Zawiera podstawową walidację nazw i slugów (unikalność sluga).
    -   `/tags/edit/{id}` (GET): Wyświetla formularz edycji tagu o podanym ID.
    -   `/tags/update/{id}` (POST): Przetwarza dane z formularza edycji i aktualizuje istniejący tag. Zawiera podstawową walidację nazw i slugów (unikalność sluga, z wyłączeniem edytowanego tagu).
    -   `/tags/delete/{id}` (GET): Usuwa tag o podanym ID. (Uwaga: Użycie metody GET dla usuwania nie jest najlepszą praktyką; w pełnej aplikacji lepiej użyć POST lub DELETE. TODO: Rozważyć obsługę artykułów powiązanych z usuwanym tagiem).
-   **Widok listy (`views/tags/index.php`):** Wyświetla tagi w tabeli Bootstrap 5, z linkami do edycji i usuwania. Brakuje jeszcze wyświetlania liczby artykułów.
-   **Widok formularza (`views/tags/form.php`):** Używany do dodawania i edycji tagów. Posiada pola na nazwę i slug, z auto-generowaniem sluga na podstawie nazwy (client-side JS).

## Migracja Modułu: Strony Statyczne

-   **Model (`models/StaticPage.php`):** Zawiera metody do operacji CRUD (tworzenie, pobieranie, aktualizacja, usuwanie) na tabeli `static_pages`, a także metody do pobierania strony po slugu (`getStaticPageBySlug`) i aktualizacji statusu publikacji (`updatePublishedStatus`).
-   **Kontroler (`controllers/StaticPageController.php`):** Obsługuje żądania dotyczące stron statycznych:
    -   `/pages` (GET): Wyświetla listę stron statycznych.
    -   `/pages/new` (GET): Wyświetla formularz dodawania nowej strony.
    -   `/pages/store` (POST): Przetwarza dane z formularza dodawania i tworzy nową stronę statyczną. Zawiera podstawową walidację nazw i slugów (unikalność sluga).
    -   `/pages/edit/{id}` (GET): Wyświetla formularz edycji strony statycznej o podanym ID.
    -   `/pages/update/{id}` (POST): Przetwarza dane z formularza edycji i aktualizuje istniejącą stronę. Zawiera podstawową walidację nazw i slugów (unikalność sluga, z wyłączeniem edytowanej strony).
    -   `/pages/delete/{id}` (GET): Usuwa stronę statyczną o podanym ID. (Uwaga: Użycie metody GET dla usuwania nie jest najlepszą praktyką; w pełnej aplikacji lepiej użyć POST lub DELETE).
    -   `/pages/toggle-published/{id}` (GET): Przełącza status publikacji strony o podanym ID. (Użycie GET dla akcji modyfikacji jest uproszczeniem, w pełnej aplikacji lepiej użyć POST).
    -   `/preview/{slug}` (GET): Wyświetla podgląd opublikowanej strony statycznej o podanym slugu.
-   **Widok listy (`views/static_pages/index.php`):** Wyświetla strony statyczne w tabeli Bootstrap 5, z przyciskami do zmiany statusu publikacji, podglądu, edycji i usuwania.
-   **Widok formularza (`views/static_pages/form.php`):** Używany do dodawania i edycji stron statycznych. Posiada pola na tytuł, slug i treść (z edytorem SimpleMDE) oraz checkbox statusu publikacji. Zawiera auto-generowanie sluga (client-side JS).
-   **Widok podglądu (`views/static_pages/preview.php`):** Wyświetla tytuł, datę aktualizacji i treść strony statycznej. (TODO: Dodać renderowanie Markdown do HTML).

## Migracja Modułu: Galeria

-   **Model (`models/Gallery.php`):** Zawiera metody do zarządzania kategoriami galerii (CRUD z walidacją sluga) i zdjęciami (pobieranie wszystkich, wg kategorii, po ID, tworzenie, usuwanie pojedynczego, podstawowe wgrywanie plików), a także pobieranie liczby zdjęć per kategoria.
-   **Kontroler (`controllers/GalleryController.php`):** Obsługuje żądania dotyczące galerii:
    -   `/gallery` (GET): Wyświetla galerię zdjęć z wyszukiwaniem i filtrowaniem według kategorii.
    -   `/gallery/categories/store` (POST): Przetwarza dane z modalu i tworzy nową kategorię galerii. Zawiera podstawową walidację.
    -   `/gallery/upload` (POST): Obsługuje przesyłanie jednego lub wielu plików graficznych, zapisuje je na serwerze i tworzy rekordy w bazie danych (uproszczona implementacja).
    -   `/gallery/delete/{id}` (GET): Usuwa pojedyncze zdjęcie o podanym ID (razem z plikiem na serwerze). (Użycie GET dla usuwania jest uproszczeniem).
    -   `/gallery/delete-multiple` (GET): Usuwa wiele zdjęć na podstawie listy ID przekazanych w query string. (Użycie GET dla usuwania jest uproszczeniem).
    -   Wykorzystuje Model `Gallery` do wszystkich operacji.
-   **Widok (`views/gallery/index.php`):** Wyświetla zdjęcia w widoku siatki (z możliwością rozbudowy o widok kategorii). Zawiera pole wyszukiwania, filtr kategorii, modal do dodawania kategorii galerii oraz funkcjonalność zaznaczania wielu zdjęć z przyciskiem do ich usuwania (client-side JS z przekierowaniem GET). Zawiera podstawowe style Bootstrap 5.

## Migracja Modułu: Menu

-   **Model (`models/Menu.php`):** Zawiera metody do zarządzania menu (CRUD z walidacją nazwy) i elementami menu (pobieranie wg ID menu, po ID elementu, tworzenie, aktualizacja, usuwanie, aktualizacja kolejności `updateMenuItemOrder`), a także pobieranie liczby elementów per menu.
-   **Kontroler (`controllers/MenuController.php`):** Obsługuje żądania dotyczące menu i elementów menu:
    -   `/menus` (GET): Wyświetla listę menu. Opcjonalnie, jeśli podano `menu_id`, wyświetla listę elementów danego menu. Zawiera podstawowe wyszukiwanie menu.
    -   `/menus/new` (GET): Wyświetla formularz dodawania nowego menu.
    -   `/menus/store` (POST): Przetwarza dane z formularza dodawania i tworzy nowe menu. Zawiera podstawową walidację.
    -   `/menus/edit/{id}` (GET): Wyświetla formularz edycji menu o podanym ID.
    -   `/menus/update/{id}` (POST): Przetwarza dane z formularza edycji i aktualizuje istniejące menu. Zawiera podstawową walidację.
    -   `/menus/delete/{id}` (GET): Usuwa menu o podanym ID (kaskadowo usuwa elementy). (Użycie GET jest uproszczeniem).
    -   `/menus/{menuId}/items/new` (GET): Wyświetla formularz dodawania nowego elementu menu dla danego menu.
    -   `/menus/{menuId}/items/store` (POST): Przetwarza dane z formularza dodawania i tworzy nowy element menu.
    -   `/menus/{menuId}/items/edit/{itemId}` (GET): Wyświetla formularz edycji elementu menu o podanym ID w danym menu.
    -   `/menus/{menuId}/items/update/{itemId}` (POST): Przetwarza dane z formularza edycji i aktualizuje istniejący element menu.
    -   `/menus/{menuId}/items/delete/{itemId}` (GET): Usuwa element menu o podanym ID w danym menu. (Użycie GET jest uproszczeniem). (TODO: Po usunięciu elementu, warto by zaktualizować kolejność pozostałych elementów).
    -   `/menus/{menuId}/items/order` (POST): Obsługuje aktualizację kolejności elementów menu.
    -   Wykorzystuje Model `Menu` do wszystkich operacji.
-   **Widok główny (`views/menus/index.php`):** Wyświetla listę menu w tabeli Bootstrap 5 z przyciskami akcji. Po kliknięciu "Elementy", wyświetla listę elementów wybranego menu w drugiej tabeli. Zaimplementowano podstawową funkcję przeciągnij i upuść dla zmiany kolejności elementów menu za pomocą SortableJS, z przyciskiem "Zapisz kolejność" do wysłania zmienionej kolejności na serwer.
-   **Widok formularza menu (`views/menus/form.php`):** Używany do dodawania i edycji menu. Posiada pola na nazwę i lokalizację, stylizowany Bootstrapem 5.
-   **Widok formularza elementu menu (`views/menus/item_form.php`):** Używany do dodawania i edycji elementów menu. Posiada pola na nazwę i URL, stylizowany Bootstrapem 5.

## Migracja Modułu: Banery

-   **Model (`models/Banner.php`):** Zawiera metody do operacji CRUD (tworzenie, pobieranie, aktualizacja, usuwanie) na tabeli `banners`, a także metodę do aktualizacji statusu aktywności (`updateActiveStatus`) i obsługę przesyłania plików graficznych (`uploadImage`, z usuwaniem starego pliku przy aktualizacji i usuwaniu baneru).
-   **Kontroler (`controllers/BannerController.php`):** Obsługuje żądania dotyczące banerów:
    -   `/banners` (GET): Wyświetla listę banerów.
    -   `/banners/new` (GET): Wyświetla formularz dodawania nowego banera.
    -   `/banners/store` (POST): Przetwarza dane z formularza dodawania i tworzy nowy baner wraz z wgraniem pliku graficznego. Zawiera podstawową walidację.
    -   `/banners/edit/{id}` (GET): Wyświetla formularz edycji banera o podanym ID.
    -   `/banners/update/{id}` (POST): Przetwarza dane z formularza edycji i aktualizuje istniejący baner. Obsługuje wgranie nowego pliku graficznego i usunięcie starego. Zawiera podstawową walidację.
    -   `/banners/delete/{id}` (GET): Usuwa baner o podanym ID (wraz z plikiem graficznym). (Użycie GET jest uproszczeniem).
    -   `/banners/toggle-active/{id}` (GET): Przełącza status aktywności banera o podanym ID. (Użycie GET dla akcji modyfikacji jest uproszczeniem).
    -   Wykorzystuje Model `Banner` do wszystkich operacji.
-   **Widok listy (`views/banners/index.php`):** Wyświetla banery w tabeli Bootstrap 5, z podglądem zdjęcia, linkiem URL, statusem aktywności oraz przyciskami do zmiany statusu, edycji i usuwania.
-   **Widok formularza (`views/banners/form.php`):** Używany do dodawania i edycji banerów. Posiada pola na nazwę, plik graficzny (z podglądem przy edycji), link URL i status aktywności. Umożliwia wgrywanie plików.

## Migracja Modułu: Użytkownicy

-   **Model (`models/User.php`):** Zawiera metody do operacji CRUD (tworzenie, pobieranie listy z wykluczeniem hasła, pobieranie po ID z wykluczeniem hasła, pobieranie po nazwie użytkownika z hasłem, aktualizacja danych użytkownika, aktualizacja hasła, usuwanie), a także metodę do weryfikacji hasła (`checkPassword`). Hasła są haszowane przed zapisem do bazy.
-   **Kontroler (`controllers/UserController.php`):** Obsługuje żądania dotyczące użytkowników:
    -   `/users` (GET): Wyświetla listę użytkowników.
    -   `/users/new` (GET): Wyświetla formularz dodawania nowego użytkownika.
    -   `/users/store` (POST): Przetwarza dane z formularza dodawania i tworzy nowego użytkownika z haszowanym hasłem. Zawiera podstawową walidację. (TODO: Dodać sprawdzanie unikalności nazwy użytkownika i emaila, lepszą walidację hasła/emaila).
    -   `/users/edit/{id}` (GET): Wyświetla formularz edycji użytkownika o podanym ID.
    -   `/users/update/{id}` (POST): Przetwarza dane z formularza edycji i aktualizuje dane użytkownika. Umożliwia opcjonalną zmianę hasła. Zawiera podstawową walidację. (TODO: Dodać sprawdzanie unikalności nazwy użytkownika i emaila, z wyłączeniem edytowanego użytkownika).
    -   `/users/delete/{id}` (GET): Usuwa użytkownika o podanym ID. (Użycie GET jest uproszczeniem). (TODO: Dodać potwierdzenie, uniemożliwić usunięcie aktywnego użytkownika).
    -   Wykorzystuje Model `User` do wszystkich operacji.
-   **Widok listy (`views/users/index.php`):** Wyświetla użytkowników w tabeli Bootstrap 5, z nazwą użytkownika, emailem, rolą, datą utworzenia oraz przyciskami do edycji i usuwania.
-   **Widok formularza (`views/users/form.php`):** Używany do dodawania i edycji użytkowników. Posiada pola na nazwę użytkownika (zablokowane przy edycji), email, hasło (opcjonalne przy edycji) i wybór roli (dropdown).

## Kolejne Kroki

Migracja podstawowych modułów zarządzania treścią i danymi (Artykuły, Kategorie, Tagi, Strony Statyczne, Galeria, Menu, Banery, Użytkownicy) została zakończona na poziomie podstawowych operacji CRUD i odtwarzania funkcjonalności z widoków React.

Następne etapy będą polegały na:
-   Migracja z Tailwind do Bootstrap 5 została zakończona we wszystkich widokach
-   Wspólny layout/szablon dla wszystkich widoków został zaimplementowany z użyciem systemu grid Bootstrap
-   Implementacji systemu powiadomień/komunikatów dla użytkownika (np. "Artykuł dodany pomyślnie").
-   Implementacji podstawowego systemu uwierzytelniania (login/logout) i autoryzacji (sprawdzanie ról użytkowników przed dostępem do zasobów/akcji).
-   Uzupełnieniu brakujących TODOs zaimplementowanych w kodzie, np. pełna walidacja formularzy, obsługa błędów, wyświetlanie liczby artykułów w kategoriach/tagach, renderowanie treści Markdown.
-   Dopracowaniu stylistyki i responsywności z użyciem Bootstrapa.
-   Refaktoryzacji kodu i poprawie potencjalnych problemów bezpieczeństwa (np. użycie POST dla akcji usuwania).