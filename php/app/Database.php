<?php

class Database {
    private $db;
    private $databaseFile;

    public function __construct() {
        $this->databaseFile = __DIR__ . '/../database.sqlite';
        $this->connect();
    }

    private function connect() {
        try {
            $this->db = new PDO('sqlite:' . $this->databaseFile);
            $this->db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->db->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC); // Fetch results as associative arrays
        } catch (PDOException $e) {
            // In a real application, you would log this error
            die("Database connection error: " . $e->getMessage());
        }
    }

    public function getConnection() {
        return $this->db;
    }
}
