<?php

class Router {
    private $routes = [];
    private $dbConnection;

    public function __construct($dbConnection) {
        $this->dbConnection = $dbConnection;
    }

    public function addRoute($method, $path, $action) {
        $this->routes[] = [
            'method' => $method,
            'path' => $path,
            'action' => $action
        ];
    }

    public function handleRequest($method, $uri) {
        error_log("Handling request: $method $uri");

        // Remove query string
        $uri = strtok($uri, '?');
        error_log("URI after query removal: $uri");

        foreach ($this->routes as $route) {
            error_log("Checking route: {$route['method']} {$route['path']}");

            if ($route['method'] !== $method) {
                continue;
            }

            // Simple path matching
            if ($route['path'] === $uri) {
                $this->callAction($route['action'], []);
                return;
            }

            // Path with parameters
            $routeParts = explode('/', trim($route['path'], '/'));
            $uriParts = explode('/', trim($uri, '/'));

            if (count($routeParts) !== count($uriParts)) {
                continue;
            }

            $params = [];
            $match = true;

            for ($i = 0; $i < count($routeParts); $i++) {
                if (strpos($routeParts[$i], '{') === 0 && strpos($routeParts[$i], '}') === strlen($routeParts[$i]) - 1) {
                    $paramName = trim($routeParts[$i], '{}');
                    $params[$paramName] = $uriParts[$i];
                } elseif ($routeParts[$i] !== $uriParts[$i]) {
                    $match = false;
                    break;
                }
            }

            if ($match) {
                error_log("Route matched with params: " . print_r($params, true));
                $this->callAction($route['action'], $params);
                return;
            }
        }

        http_response_code(404);
        echo "404 Not Found";
        error_log("No route found for $method $uri");
    }

    private function callAction($action, $params) {
        error_log("Calling action: $action with params: " . print_r($params, true));

        list($controllerName, $methodName) = explode('@', $action);
        $controllerFile = BASE_PATH . '/controllers/' . $controllerName . '.php';

        if (!file_exists($controllerFile)) {
            error_log("Controller file not found: $controllerFile");
            http_response_code(500);
            echo "Error: Controller file not found";
            return;
        }

        require_once $controllerFile;

        if (!class_exists($controllerName)) {
            error_log("Controller class not found: $controllerName");
            http_response_code(500);
            echo "Error: Controller class not found";
            return;
        }

        $controller = new $controllerName($this->dbConnection);

        if (!method_exists($controller, $methodName)) {
            error_log("Method not found: $methodName in $controllerName");
            http_response_code(500);
            echo "Error: Method not found";
            return;
        }

        // Convert associative array to indexed array for call_user_func_array
        $paramValues = array_values($params);
        call_user_func_array([$controller, $methodName], $paramValues);
    }
}
