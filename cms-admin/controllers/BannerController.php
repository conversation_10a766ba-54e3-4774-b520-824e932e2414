<?php

require_once CMS_ADMIN_PATH . '/app/Controller.php';
require_once CMS_ADMIN_PATH . '/models/Banner.php';

class BannerController extends Controller {
    private $bannerModel;

    public function __construct($dbConnection) {
        parent::__construct($dbConnection);
        $this->bannerModel = new Banner($dbConnection);

        // Check if user is logged in for all actions in this controller
        // session_start(); // Session is already started in index.php for protected routes
        if (!isset($_SESSION['user_id'])) {
            // TODO: Show a proper unauthorized view or message
            http_response_code(403); // Forbidden
            echo "Unauthorized access. Please log in.";
            exit(); // Stop further execution
        }
    }

    public function index() {
        $banners = $this->bannerModel->getAllBanners();

        $flash = null;
        if (isset($_SESSION['flash'])) {
            $flash = $_SESSION['flash'];
            unset($_SESSION['flash']);
        }

        $this->renderView('banners/BannersView', [
            'banners' => $banners,
            'flash' => $flash
        ]);
    }

    // Handles both display of form (GET) and processing (POST for update - store handled separately)
    public function form($id = null) {
        $banner = null;
        if ($id) {
            $banner = $this->bannerModel->getBannerById($id);
            if (!$banner) {
                $_SESSION['flash'] = ['type' => 'danger', 'message' => 'Nie znaleziono bannera.'];
                header('Location: /banners');
                exit();
            }
        }

        $flash = null;
        if (isset($_SESSION['flash'])) {
            $flash = $_SESSION['flash'];
            unset($_SESSION['flash']);
        }

        $this->renderView('banners/BannerFormView', [
            'banner' => $banner,
            'flash' => $flash
        ]);
    }

    public function store() {
        $title = filter_input(INPUT_POST, 'title', FILTER_SANITIZE_STRING);
        $content = filter_input(INPUT_POST, 'content', FILTER_UNSAFE_RAW);
        $position = filter_input(INPUT_POST, 'position', FILTER_VALIDATE_INT) ?: 0;
        $active = isset($_POST['active']) ? 1 : 0;

        // Handle file upload
        $imageFilename = null;

        if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            $uploadResult = $this->bannerModel->uploadImage($_FILES['image']);
            if ($uploadResult) {
                $imageFilename = $uploadResult['filename'];
            } else {
                $_SESSION['flash'] = ['type' => 'danger', 'message' => 'Błąd podczas przesyłania obrazu'];
                header('Location: /banners/new');
                exit();
            }
        }

        // Basic validation
        if (empty($title)) {
            $_SESSION['flash'] = ['type' => 'danger', 'message' => 'Tytuł jest wymagany'];
            header('Location: /banners/new');
            exit();
        }

        if ($this->bannerModel->createBanner($title, $content, $imageFilename, $position, $active)) {
            $_SESSION['flash'] = ['type' => 'success', 'message' => 'Banner utworzony pomyślnie!'];
            header('Location: /banners');
            exit();
        } else {
            $_SESSION['flash'] = ['type' => 'danger', 'message' => 'Błąd podczas tworzenia bannera'];
            header('Location: /banners/new');
            exit();
        }
    }

    public function update($id) {
        $banner = $this->bannerModel->getBannerById($id);
        if (!$banner) {
            $_SESSION['flash'] = ['type' => 'danger', 'message' => 'Banner nie został znaleziony'];
            header('Location: /banners');
            exit();
        }

        $title = filter_input(INPUT_POST, 'title', FILTER_SANITIZE_STRING);
        $content = filter_input(INPUT_POST, 'content', FILTER_UNSAFE_RAW);
        $position = filter_input(INPUT_POST, 'position', FILTER_VALIDATE_INT) ?: 0;
        $active = isset($_POST['active']) ? 1 : 0;

        // Handle file upload (if a new image is uploaded)
        $imageFilename = $banner['image']; // Keep old if no new upload

        if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            $uploadResult = $this->bannerModel->uploadImage($_FILES['image']);
            if ($uploadResult) {
                // Delete old image file if exists
                if (!empty($banner['image'])) {
                    $oldFullFilepath = $this->bannerModel->getUploadDir() . $banner['image'];
                    if (file_exists($oldFullFilepath)) {
                        unlink($oldFullFilepath);
                    }
                }
                $imageFilename = $uploadResult['filename'];
            } else {
                $_SESSION['flash'] = ['type' => 'danger', 'message' => 'Błąd podczas przesyłania nowego obrazu'];
                header('Location: /banners/edit/' . $id);
                exit();
            }
        }

        // Basic validation
        if (empty($title)) {
            $_SESSION['flash'] = ['type' => 'danger', 'message' => 'Tytuł jest wymagany'];
            header('Location: /banners/edit/' . $id);
            exit();
        }

        if ($this->bannerModel->updateBanner($id, $title, $content, $imageFilename, $position, $active)) {
            $_SESSION['flash'] = ['type' => 'success', 'message' => 'Banner zaktualizowany pomyślnie!'];
            header('Location: /banners');
            exit();
        } else {
            $_SESSION['flash'] = ['type' => 'danger', 'message' => 'Błąd podczas aktualizacji bannera'];
            header('Location: /banners/edit/' . $id);
            exit();
        }
    }

    public function delete($id) {
        if ($this->bannerModel->deleteBanner($id)) {
            $_SESSION['flash'] = ['type' => 'success', 'message' => 'Banner usunięty pomyślnie!'];
            header('Location: /banners');
            exit();
        } else {
            $_SESSION['flash'] = ['type' => 'danger', 'message' => 'Błąd podczas usuwania bannera'];
            header('Location: /banners');
            exit();
        }
    }

    public function toggleActive($id) {
        $banner = $this->bannerModel->getBannerById($id);
        if (!$banner) {
            $_SESSION['flash'] = ['type' => 'danger', 'message' => 'Banner nie został znaleziony'];
            header('Location: /banners');
            exit();
        }

        $newStatus = (isset($banner['active']) && $banner['active']) ? 0 : 1;

        if ($this->bannerModel->updateActiveStatus($id, $newStatus)) {
            $_SESSION['flash'] = ['type' => 'success', 'message' => 'Status bannera zaktualizowany pomyślnie!'];
            header('Location: /banners');
            exit();
        } else {
            $_SESSION['flash'] = ['type' => 'danger', 'message' => 'Błąd podczas aktualizacji statusu bannera'];
            header('Location: /banners');
            exit();
        }
    }
}
