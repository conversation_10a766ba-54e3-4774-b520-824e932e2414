<?php

require_once CMS_ADMIN_PATH . '/app/Controller.php';
require_once CMS_ADMIN_PATH . '/models/Tag.php';

class TagController extends Controller {
    private $tagModel;

    public function __construct($dbConnection) {
        parent::__construct($dbConnection);
        $this->tagModel = new Tag($dbConnection);

        // Check if user is logged in for all actions in this controller
        // session_start(); // Session is already started in index.php for protected routes
        if (!isset($_SESSION['user_id'])) {
            // Redirect to login page if not authenticated
            header('Location: /login');
            exit();
        }
    }

    public function index() {
        $tags = $this->tagModel->getAllTags();
        // TODO: Add article count for each tag

        $this->renderView('tags/TagsView', [
            'tags' => $tags
        ]);
    }

    // Handles both display of form (GET) and processing (POST)
    public function form($id = null) {
        $tag = null;
        if ($id) {
            $tag = $this->tagModel->getTagById($id);
            if (!$tag) {
                http_response_code(404);
                echo "Tag not found.";
                return;
            }
        }

        $this->renderView('tags/TagsFormView', [
            'tag' => $tag
        ]);
    }

    public function store() {
        $name = filter_input(INPUT_POST, 'name', FILTER_SANITIZE_STRING);
        $slug = filter_input(INPUT_POST, 'slug', FILTER_SANITIZE_STRING); // Assuming slug is provided or auto-generated

        // Basic validation
        if (empty($name) || empty($slug)) {
            // TODO: Handle validation error more gracefully
            echo "Name and slug are required.";
            return;
        }

        // Check if slug is unique
        if ($this->tagModel->getTagBySlug($slug)) {
            // TODO: Handle validation error more gracefully
            echo "Slug must be unique.";
            return;
        }


        if ($this->tagModel->createTag($name, $slug)) {
            $this->setFlashMessage('Tag utworzony pomyślnie!', 'success');
            header('Location: /tags');
        } else {
            // Handle error
            // TODO: Set error flash message and redirect back to form with old input
            echo "Error creating tag.";
        }
    }

    public function update($id) {
        $name = filter_input(INPUT_POST, 'name', FILTER_SANITIZE_STRING);
        $slug = filter_input(INPUT_POST, 'slug', FILTER_SANITIZE_STRING); // Assuming slug is provided or auto-generated

        // Basic validation
        if (empty($name) || empty($slug)) {
            // TODO: Handle validation error more gracefully
            echo "Name and slug are required.";
            return;
        }

        // Check if slug is unique, excluding the current tag
        $existingTag = $this->tagModel->getTagBySlug($slug);
        if ($existingTag && $existingTag['id'] != $id) {
            // TODO: Handle validation error more gracefully
            echo "Slug must be unique.";
            return;
        }


        if ($this->tagModel->updateTag($id, $name, $slug)) {
            $this->setFlashMessage('Tag zaktualizowany pomyślnie!', 'success');
            header('Location: /tags');
        } else {
            // Handle error
            // TODO: Set error flash message and redirect back to form with old input
            echo "Error updating tag.";
        }
    }


    public function delete($id) {
        // TODO: Add confirmation or handle articles associated with this tag

        if ($this->tagModel->deleteTag($id)) {
            $this->setFlashMessage('Tag usunięty pomyślnie!', 'success');
            header('Location: /tags');
        } else {
            // Handle error
            // TODO: Set error flash message and redirect back
            echo "Error deleting tag.";
        }
    }
}
