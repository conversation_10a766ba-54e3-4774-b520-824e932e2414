<?php

require_once CMS_ADMIN_PATH . '/app/Controller.php';
require_once CMS_ADMIN_PATH . '/models/Category.php';

class CategoryController extends Controller {
    private $categoryModel;

    public function __construct($dbConnection) {
        parent::__construct($dbConnection);
        $this->categoryModel = new Category($dbConnection);

        // Check if user is logged in for all actions in this controller
        // session_start(); // Session is already started in index.php for protected routes
        if (!isset($_SESSION['user_id'])) {
            // Redirect to login page if not authenticated
            header('Location: ' . cms_admin_url('login'));
            exit();
        }
    }

    public function index() {
        $categories = $this->categoryModel->getAllCategories();
        // TODO: Add article count for each category

        $this->renderView('categories/CategoriesView', [
            'categories' => $categories
        ]);
    }

    // Handles both display of form (GET) and processing (POST)
    public function form($id = null) {
        $category = null;
        if ($id) {
            $category = $this->categoryModel->getCategoryById($id);
            if (!$category) {
                http_response_code(404);
                echo "Category not found.";
                return;
            }
        }

        $this->renderView('categories/CategoriesFormView', [
            'category' => $category
        ]);
    }

    public function store() {
        $name = filter_input(INPUT_POST, 'name', FILTER_SANITIZE_STRING);
        $slug = filter_input(INPUT_POST, 'slug', FILTER_SANITIZE_STRING); // Assuming slug is provided or auto-generated

        // Basic validation
        if (empty($name) || empty($slug)) {
            // TODO: Handle validation error more gracefully
            echo "Name and slug are required.";
            return;
        }

        // Check if slug is unique
        if ($this->categoryModel->getCategoryBySlug($slug)) {
            // TODO: Handle validation error more gracefully
            echo "Slug must be unique.";
            return;
        }


        if ($this->categoryModel->createCategory($name, $slug)) {
            $this->setFlashMessage('Kategoria utworzona pomyślnie!', 'success');
            header('Location: ' . cms_admin_url('categories'));
        } else {
            // Handle error
            // TODO: Set error flash message and redirect back to form with old input
            echo "Error creating category.";
        }
    }

    public function update($id) {
        $name = filter_input(INPUT_POST, 'name', FILTER_SANITIZE_STRING);
        $slug = filter_input(INPUT_POST, 'slug', FILTER_SANITIZE_STRING); // Assuming slug is provided or auto-generated

        // Basic validation
        if (empty($name) || empty($slug)) {
            // TODO: Handle validation error more gracefully
            echo "Name and slug are required.";
            return;
        }

        // Check if slug is unique, excluding the current category
        $existingCategory = $this->categoryModel->getCategoryBySlug($slug);
        if ($existingCategory && $existingCategory['id'] != $id) {
            // TODO: Handle validation error more gracefully
            echo "Slug must be unique.";
            return;
        }


        if ($this->categoryModel->updateCategory($id, $name, $slug)) {
            $this->setFlashMessage('Kategoria zaktualizowana pomyślnie!', 'success');
            header('Location: ' . cms_admin_url('categories'));
        } else {
            // Handle error
            // TODO: Set error flash message and redirect back to form with old input
            echo "Error updating category.";
        }
    }


    public function delete($id) {
        // TODO: Add confirmation or handle articles in this category

        if ($this->categoryModel->deleteCategory($id)) {
            $this->setFlashMessage('Kategoria usunięta pomyślnie!', 'success');
            header('Location: ' . cms_admin_url('categories'));
        } else {
            // Handle error
            // TODO: Set error flash message and redirect back
            echo "Error deleting category.";
        }
    }
}
