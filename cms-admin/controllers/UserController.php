<?php

require_once CMS_ADMIN_PATH . '/app/Controller.php';
require_once CMS_ADMIN_PATH . '/models/User.php';

class UserController extends Controller {
    private $userModel;

    public function __construct($dbConnection) {
        parent::__construct($dbConnection);
        $this->userModel = new User($dbConnection);

        // Check if the user is logged in and has admin role for all actions in this controller
        if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
            // TODO: Show a proper unauthorized view or message
            http_response_code(403); // Forbidden
            echo "Unauthorized access. Admin role required.";
            exit(); // Stop further execution
        }
    }

    public function index() {
        $users = $this->userModel->getAllUsers();

        $flash = null;
        if (isset($_SESSION['flash'])) {
            $flash = $_SESSION['flash'];
            unset($_SESSION['flash']);
        }

        $this->renderView('users/UsersView', [
            'users' => $users,
            'flash' => $flash
        ]);
    }

    // Handles both display of form (GET) and processing (POST for update - store handled separately)
    public function form($id = null) {
        $user = null;
        if ($id) {
            $user = $this->userModel->getUserById($id);
            if (!$user) {
                $_SESSION['flash'] = ['type' => 'danger', 'message' => 'Nie znaleziono użytkownika.'];
                header('Location: /users');
                exit();
            }
        }

        $flash = null;
        if (isset($_SESSION['flash'])) {
            $flash = $_SESSION['flash'];
            unset($_SESSION['flash']);
        }

        $this->renderView('users/UsersFormView', [
            'user' => $user,
            'flash' => $flash
        ]);
    }

    public function store() {
        $username = filter_input(INPUT_POST, 'username', FILTER_SANITIZE_STRING);
        $password = $_POST['password'] ?? ''; // Password should not be sanitized like a string
        $email = filter_input(INPUT_POST, 'email', FILTER_SANITIZE_EMAIL);
        $role = filter_input(INPUT_POST, 'role', FILTER_SANITIZE_STRING) ?? 'editor'; // Default role

        // Basic validation
        if (empty($username) || empty($password) || empty($email)) {
            $_SESSION['flash'] = ['type' => 'danger', 'message' => 'Nazwa użytkownika, hasło i email są wymagane.'];
            header('Location: /users/new');
            exit();
        }

        // TODO: Add more robust validation (e.g., email format, password strength)
        // TODO: Check for unique username and email

        if ($this->userModel->createUser($username, $password, $email, $role)) {
            $_SESSION['flash'] = ['type' => 'success', 'message' => 'Użytkownik został utworzony pomyślnie.'];
            header('Location: /users');
            exit();
        } else {
            $_SESSION['flash'] = ['type' => 'danger', 'message' => 'Błąd podczas tworzenia użytkownika.'];
            header('Location: /users/new');
            exit();
        }
    }

    public function update($id) {
        $user = $this->userModel->getUserById($id);
        if (!$user) {
            $_SESSION['flash'] = ['type' => 'danger', 'message' => 'Nie znaleziono użytkownika.'];
            header('Location: /users');
            exit();
        }

        $username = filter_input(INPUT_POST, 'username', FILTER_SANITIZE_STRING);
        $email = filter_input(INPUT_POST, 'email', FILTER_SANITIZE_EMAIL);
        $role = filter_input(INPUT_POST, 'role', FILTER_SANITIZE_STRING) ?? 'editor';

        // Basic validation
        if (empty($username) || empty($email)) {
            $_SESSION['flash'] = ['type' => 'danger', 'message' => 'Nazwa użytkownika i email są wymagane.'];
            header('Location: /users/edit/' . $id);
            exit();
        }

        // TODO: Add more robust validation
        // TODO: Check for unique username and email, excluding the current user

        // Handle password update separately if provided
        $password = $_POST['password'] ?? '';
        if (!empty($password)) {
            // TODO: Validate password strength
            $this->userModel->updatePassword($id, $password);
        }

        if ($this->userModel->updateUser($id, $username, $email, $role)) {
            $_SESSION['flash'] = ['type' => 'success', 'message' => 'Użytkownik został zaktualizowany pomyślnie.'];
            header('Location: /users');
            exit();
        } else {
            $_SESSION['flash'] = ['type' => 'danger', 'message' => 'Błąd podczas aktualizacji użytkownika.'];
            header('Location: /users/edit/' . $id);
            exit();
        }
    }


    public function delete($id) {
        // TODO: Add confirmation
        // TODO: Prevent deleting the currently logged-in user (if it's the only admin, or other rules)

        if ($this->userModel->deleteUser($id)) {
            $_SESSION['flash'] = ['type' => 'success', 'message' => 'Użytkownik został usunięty pomyślnie.'];
            header('Location: /users');
            exit();
        } else {
            $_SESSION['flash'] = ['type' => 'danger', 'message' => 'Błąd podczas usuwania użytkownika.'];
            header('Location: /users');
            exit();
        }
    }
}
