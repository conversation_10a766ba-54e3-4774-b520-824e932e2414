<?php
if (!isset($lang)) {
    $lang = include __DIR__ . '/../../lang/pl.php';
}
$title = 'Logowanie';
$current_page = 'login';
ob_start();
?>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8 login-container">
            <h2 class="text-center mb-4">Panel Logowania CMS</h2>

            <?php if (isset($_GET['error'])): ?>
                <div class="alert alert-danger" role="alert">
                    Błąd logowania. Sprawdź nazwę użytkownika i hasło.
                </div>
            <?php endif; ?>

            <form action="<?php echo cms_admin_url('login'); ?>" method="POST">
                <div class="mb-3">
                    <label for="username" class="form-label">Nazwa użytkownika</label>
                    <input type="text" class="form-control" id="username" name="username" required autofocus>
                </div>
                <div class="mb-3">
                    <label for="password" class="form-label">Hasło</label>
                    <input type="password" class="form-control" id="password" name="password" required>
                </div>
                <button type="submit" class="btn btn-primary w-100">Zaloguj się</button>
            </form>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../LayoutView.php';
?>