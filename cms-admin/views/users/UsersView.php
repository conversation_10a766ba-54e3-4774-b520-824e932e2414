<?php
$translations = include __DIR__ . '/../../lang/pl.php';
$title = $translations['users.title'] ?? 'Użytkownicy';
$current_page = 'users';
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="page-title"><?php echo $translations['users.title']; ?></h1>
    <a href="/users/new" class="btn btn-primary">
        <i class="bi bi-person-plus me-1"></i> <?php echo $translations['users.new_button']; ?>
    </a>
</div>

<?php if (isset($flash)): ?>
    <div class="alert alert-<?php echo $flash['type']; ?> alert-dismissible fade show shadow-sm" role="alert">
        <i class="bi bi-info-circle me-2"></i> <?php echo htmlspecialchars($flash['message']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<div class="card mb-4 shadow-sm">
    <div class="card-body">
        <form method="GET" action="<?php echo cms_admin_url('users'); ?>" class="row g-3 align-items-center mb-0">
            <div class="col-md-8">
                <div class="input-group">
                    <span class="input-group-text bg-light border-end-0">
                        <i class="bi bi-search text-muted"></i>
                    </span>
                    <input class="form-control border-start-0 ps-0" type="search"
                        placeholder="<?php echo $translations['users.search_placeholder']; ?>"
                        aria-label="Search" name="search"
                        value="<?php echo htmlspecialchars($searchTerm ?? ''); ?>">
                </div>
            </div>
            <div class="col-md-4">
                <button class="btn btn-primary" type="submit">
                    <?php echo $translations['common.search']; ?>
                </button>
            </div>
        </form>
    </div>
</div>

<div class="card shadow-sm">
    <div class="table-responsive">
        <table class="table table-striped table-hover mb-0">
            <thead>
                <tr>
                    <th scope="col"><?php echo $translations['users.table.username']; ?></th>
                    <th scope="col"><?php echo $translations['users.table.email']; ?></th>
                    <th scope="col"><?php echo $translations['users.table.role']; ?></th>
                    <th scope="col"><?php echo $translations['users.table.created_at']; ?></th>
                    <th scope="col" class="text-end"><?php echo $translations['users.table.actions']; ?></th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($users)): ?>
                    <tr>
                        <td colspan="5" class="text-center py-4">
                            <div class="text-muted">
                                <i class="bi bi-search fs-3 d-block mb-2"></i>
                                <?php echo $translations['users.table.no_results']; ?>
                            </div>
                        </td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($users as $user): ?>
                        <tr>
                            <td class="align-middle fw-medium"><?php echo htmlspecialchars($user['username']); ?></td>
                            <td class="align-middle"><?php echo htmlspecialchars($user['email']); ?></td>
                            <td class="align-middle">
                                <?php if ($user['role'] === 'admin'): ?>
                                    <span class="badge bg-danger text-white">
                                        <i class="bi bi-shield-lock me-1"></i> <?php echo htmlspecialchars($user['role']); ?>
                                    </span>
                                <?php elseif ($user['role'] === 'editor'): ?>
                                    <span class="badge bg-info text-white">
                                        <i class="bi bi-pencil me-1"></i> <?php echo htmlspecialchars($user['role']); ?>
                                    </span>
                                <?php else: ?>
                                    <span class="badge bg-secondary text-white">
                                        <i class="bi bi-person me-1"></i> <?php echo htmlspecialchars($user['role']); ?>
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td class="align-middle">
                                <span class="text-muted">
                                    <i class="bi bi-calendar3 me-1"></i> <?php echo htmlspecialchars($user['created_at']); ?>
                                </span>
                            </td>
                            <td class="align-middle text-end">
                                <div class="btn-group">
                                    <a href="/users/edit/<?php echo $user['id']; ?>" class="btn btn-sm btn-primary">
                                        <i class="bi bi-pencil-square"></i>
                                    </a>
                                    <a href="/users/delete/<?php echo $user['id']; ?>"
                                        class="btn btn-sm btn-danger"
                                        onclick="return confirm('<?php echo $translations['users.delete_confirm']; ?>');">
                                        <i class="bi bi-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../views/LayoutView.php';
?>