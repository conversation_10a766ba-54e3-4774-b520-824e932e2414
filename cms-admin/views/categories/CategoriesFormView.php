<?php
$translations = include __DIR__ . '/../../lang/pl.php';
$title = isset($category) ? $translations['categories.edit_title'] : $translations['categories.new_title'];
$current_page = 'categories';

ob_start();
?>

<div class="d-flex align-items-center mb-4">
    <a href="/categories" class="btn btn-sm btn-outline-secondary me-2">
        <i class="bi bi-arrow-left"></i>
    </a>
    <h1 class="page-title mb-0"><?php echo $title; ?></h1>
</div>

<?php if (isset($flash)): ?>
    <div class="alert alert-<?php echo $flash['type']; ?> alert-dismissible fade show shadow-sm" role="alert">
        <i class="bi bi-info-circle me-2"></i> <?php echo htmlspecialchars($flash['message']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<div class="card shadow-sm">
    <div class="card-body p-4">
        <form action="<?php echo isset($category) ? '/categories/update/' . $category['id'] : '/categories/store'; ?>" method="POST">
            <div class="row g-4 mb-4">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="name" class="form-label fw-medium mb-2">
                            <?php echo $translations['categories.form.name']; ?>
                        </label>
                        <div class="input-group">
                            <span class="input-group-text bg-light">
                                <i class="bi bi-folder text-primary"></i>
                            </span>
                            <input type="text" class="form-control" id="name" name="name"
                                value="<?php echo htmlspecialchars($category['name'] ?? ''); ?>"
                                oninput="generateSlug(this.value)" required>
                        </div>
                        <small class="form-text text-muted mt-1">
                            <i class="bi bi-info-circle-fill me-1"></i> Wprowadź nazwę kategorii widoczną dla użytkowników
                        </small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="slug" class="form-label fw-medium mb-2">
                            <?php echo $translations['categories.form.slug']; ?>
                        </label>
                        <div class="input-group">
                            <span class="input-group-text bg-light">
                                <i class="bi bi-link-45deg text-secondary"></i>
                            </span>
                            <input type="text" class="form-control font-monospace" id="slug" name="slug"
                                value="<?php echo htmlspecialchars($category['slug'] ?? ''); ?>" required>
                        </div>
                        <small class="form-text text-muted mt-1">
                            <i class="bi bi-info-circle-fill me-1"></i> Wygenerowany automatycznie unikalny identyfikator używany w adresach URL
                        </small>
                    </div>
                </div>
            </div>

            <div class="d-flex justify-content-end gap-2 mt-4">
                <a href="/categories" class="btn btn-outline-secondary">
                    <i class="bi bi-x-circle me-2"></i><?php echo $translations['common.cancel']; ?>
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-check-circle me-2"></i><?php echo $translations['common.save']; ?>
                </button>
            </div>
        </form>
    </div>
</div>

<?php
$content = ob_get_clean();

$additional_scripts = <<<JS
<script>
    function generateSlug(name) {
        const slugInput = document.getElementById('slug');
        if (slugInput.value === '' || !slugInput.dataset.edited) {
            const slug = name
                .toLowerCase()
                .replace(/[^a-z0-9]+/g, '-')
                .replace(/(^-|-$)/g, '');
            slugInput.value = slug;
        }
    }

    document.getElementById('slug').addEventListener('input', function() {
        this.dataset.edited = 'true';
    });
</script>
JS;

require_once __DIR__ . '/../LayoutView.php';
