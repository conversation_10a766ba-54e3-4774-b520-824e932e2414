<?php
$translations = include __DIR__ . '/../../lang/pl.php';
$title = $translations['categories.title'] ?? 'Kategorie';
$current_page = 'categories';
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="page-title"><?php echo $translations['categories.title']; ?></h1>
    <a href="/categories/new" class="btn btn-primary">
        <i class="bi bi-folder-plus me-1"></i> <?php echo $translations['categories.new_button']; ?>
    </a>
</div>

<?php if (isset($flash)): ?>
    <div class="alert alert-<?php echo $flash['type']; ?> alert-dismissible fade show shadow-sm" role="alert">
        <i class="bi bi-info-circle me-2"></i> <?php echo htmlspecialchars($flash['message']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<div class="card mb-4 shadow-sm">
    <div class="card-body">
        <form method="GET" action="/categories" class="row g-3 align-items-center mb-0">
            <div class="col-md-8">
                <div class="input-group">
                    <span class="input-group-text bg-light border-end-0">
                        <i class="bi bi-search text-muted"></i>
                    </span>
                    <input class="form-control border-start-0 ps-0" type="search"
                        placeholder="<?php echo $translations['categories.search_placeholder']; ?>"
                        aria-label="Search" name="search"
                        value="<?php echo htmlspecialchars($searchTerm ?? ''); ?>">
                </div>
            </div>
            <div class="col-md-4">
                <button class="btn btn-primary" type="submit">
                    <?php echo $translations['common.search']; ?>
                </button>
            </div>
        </form>
    </div>
</div>

<div class="card shadow-sm">
    <div class="table-responsive">
        <table class="table table-striped table-hover mb-0">
            <thead>
                <tr>
                    <th scope="col"><?php echo $translations['categories.table.name']; ?></th>
                    <th scope="col"><?php echo $translations['categories.table.slug']; ?></th>
                    <th scope="col"><?php echo $translations['categories.table.articles_count']; ?></th>
                    <th scope="col" class="text-end"><?php echo $translations['categories.table.actions']; ?></th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($categories)): ?>
                    <tr>
                        <td colspan="4" class="text-center py-4">
                            <div class="text-muted">
                                <i class="bi bi-folder-x fs-3 d-block mb-2"></i>
                                <?php echo $translations['categories.table.no_results']; ?>
                            </div>
                        </td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($categories as $category): ?>
                        <tr>
                            <td class="align-middle fw-medium">
                                <i class="bi bi-folder me-2 text-primary"></i>
                                <?php echo htmlspecialchars($category['name']); ?>
                            </td>
                            <td class="align-middle">
                                <code><?php echo htmlspecialchars($category['slug']); ?></code>
                            </td>
                            <td class="align-middle">
                                <span class="badge bg-secondary rounded-pill">
                                    <?php echo $category['articles_count'] ?? 0; ?>
                                    <i class="bi bi-file-earmark-text ms-1"></i>
                                </span>
                            </td>
                            <td class="align-middle text-end">
                                <div class="btn-group">
                                    <a href="/categories/edit/<?php echo $category['id']; ?>" class="btn btn-sm btn-primary">
                                        <i class="bi bi-pencil-square"></i>
                                    </a>
                                    <a href="/categories/delete/<?php echo $category['id']; ?>"
                                        class="btn btn-sm btn-danger"
                                        onclick="return confirm('<?php echo $translations['categories.delete_confirm']; ?>');">
                                        <i class="bi bi-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../views/LayoutView.php';
?>