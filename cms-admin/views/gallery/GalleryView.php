<?php
$title = 'Galeria';
$current_page = 'gallery';

// Define additional CSS
$additional_css = <<<CSS
<style>
.gallery-grid {
    margin-top: 2rem;
}

.image-card {
    position: relative;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    cursor: pointer;
    transition: all 0.3s ease;
    height: 100%;
}

.image-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.image-card.selected {
    border: 3px solid var(--bs-primary);
}

.gallery-image {
    aspect-ratio: 4/3;
    object-fit: cover;
    width: 100%;
    height: auto;
}

.image-card-body {
    padding: 1rem;
    background-color: #fff;
}

.image-card-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
    font-size: 0.95rem;
    line-height: 1.4;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.image-card-date {
    font-size: 0.8rem;
    color: #718096;
    margin-bottom: 0.5rem;
}

.category-btn {
    transition: all 0.2s ease;
}

.category-btn:hover {
    background-color: var(--bs-primary);
    color: white;
}
</style>
CSS;

ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="page-title">Galeria</h1>
    <div class="d-flex gap-2">
        <button id="deleteSelectedBtn" class="btn btn-danger d-none">
            <i class="bi bi-trash me-1"></i> Usuń wybrane (<span id="selectedCount">0</span>)
        </button>
        <div class="btn-group">
            <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                <i class="bi bi-folder-plus me-1"></i> Dodaj kategorię
            </button>
            <form id="uploadForm" action="/gallery/upload" method="POST" enctype="multipart/form-data" class="d-inline">
                <label for="upload-images" class="btn btn-primary mb-0">
                    <i class="bi bi-cloud-arrow-up me-1"></i> Wgraj zdjęcia
                </label>
                <input id="upload-images" type="file" name="images[]" accept="image/*" multiple class="d-none" onchange="document.getElementById('uploadForm').submit();" />
            </form>
        </div>
    </div>
</div>

<?php if (isset($flash)): ?>
    <div class="alert alert-<?php echo $flash['type']; ?> alert-dismissible fade show shadow-sm" role="alert">
        <i class="bi bi-info-circle me-2"></i> <?php echo htmlspecialchars($flash['message']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<?php
// Obsługa komunikatów z URL
if (isset($_GET['uploaded'])) {
    $uploadedCount = (int)$_GET['uploaded'];
    echo '<div class="alert alert-success alert-dismissible fade show shadow-sm" role="alert">';
    echo '<i class="bi bi-check-circle-fill me-2"></i>Pomyślnie przesłano ' . $uploadedCount . ' zdjęcie/zdjęć.';
    echo '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>';
    echo '</div>';
} elseif (isset($_GET['error'])) {
    $error = $_GET['error'];
    $errorMessage = '';
    switch ($error) {
        case 'no_files':
            $errorMessage = 'Nie wybrano żadnych plików do przesłania.';
            break;
        case 'upload_failed':
            $errorMessage = 'Wystąpił błąd podczas przesyłania plików.';
            break;
        default:
            $errorMessage = 'Wystąpił nieznany błąd.';
    }
    echo '<div class="alert alert-danger alert-dismissible fade show shadow-sm" role="alert">';
    echo '<i class="bi bi-exclamation-triangle-fill me-2"></i>' . htmlspecialchars($errorMessage);
    echo '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>';
    echo '</div>';
}
?>

<div class="card shadow-sm mb-4">
    <div class="card-body">
        <form method="GET" action="/gallery" class="row g-3 align-items-end">
            <div class="col-md-6">
                <div class="input-group">
                    <span class="input-group-text bg-light border-end-0">
                        <i class="bi bi-search text-muted"></i>
                    </span>
                    <input class="form-control border-start-0 ps-0" type="search"
                        placeholder="Szukaj zdjęć..."
                        aria-label="Search" name="search"
                        value="<?php echo htmlspecialchars($searchTerm ?? ''); ?>">
                </div>
            </div>
            <div class="col-md-4">
                <label for="categoryFilter" class="form-label text-muted small mb-1">
                    <i class="bi bi-funnel-fill me-1"></i> Filtruj według kategorii
                </label>
                <select class="form-select" id="categoryFilter" name="category_id">
                    <?php foreach ($filterCategories as $category): ?>
                        <option value="<?php echo $category['id']; ?>" <?php echo ($selectedCategory ?? 'all') == $category['id'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($category['name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-2">
                <button class="btn btn-primary w-100" type="submit">
                    <i class="bi bi-search me-1"></i> Szukaj
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Gallery Images Grid -->
<div class="gallery-grid">
    <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-4 g-4" id="galleryImagesGrid">
        <?php if (empty($images)): ?>
            <div class="col-12">
                <div class="card shadow-sm border-0 py-5">
                    <div class="card-body text-center text-muted">
                        <i class="bi bi-images fs-1 mb-3 d-block"></i>
                        <h5>Nie znaleziono zdjęć</h5>
                        <p class="mb-0">Wgraj swoje pierwsze zdjęcie lub zmień kryteria wyszukiwania.</p>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <?php foreach ($images as $image): ?>
                <div class="col">
                    <div class="image-card" data-image-id="<?php echo $image['id']; ?>">
                        <div class="position-relative">
                            <?php
                            $imagePath = $image['filepath'] ?? (!empty($image['filename']) ? '/cms-content/uploads/gallery/' . $image['filename'] : '');
                            $imageTitle = $image['title'] ?? 'Obraz galerii';
                            ?>
                            <img src="<?php echo htmlspecialchars($imagePath); ?>"
                                class="gallery-image"
                                alt="<?php echo htmlspecialchars($imageTitle); ?>"
                                onerror="this.src='data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'200\' height=\'150\' viewBox=\'0 0 200 150\'%3E%3Crect fill=\'%23f5f5f5\' width=\'200\' height=\'150\'/%3E%3Ctext x=\'50%25\' y=\'50%25\' dominant-baseline=\'middle\' text-anchor=\'middle\' fill=\'%23999\' font-family=\'Arial\' font-size=\'14\'%3EBrak obrazu%3C/text%3E%3C/svg%3E'">
                            <div class="position-absolute top-0 end-0 m-2">
                                <span class="badge bg-dark bg-opacity-75 rounded-pill">
                                    <i class="bi bi-image me-1"></i>
                                    <?php
                                    if (!empty($imagePath)) {
                                        echo strtoupper(pathinfo($imagePath, PATHINFO_EXTENSION));
                                    } else {
                                        echo 'IMG';
                                    }
                                    ?>
                                </span>
                            </div>
                        </div>
                        <div class="image-card-body">
                            <div class="image-card-title"><?php echo htmlspecialchars($imageTitle); ?></div>
                            <div class="image-card-date">
                                <i class="bi bi-calendar3 me-1"></i>
                                <?php
                                $uploadedAt = $image['uploaded_at'] ?? $image['created_at'] ?? '';
                                if (!empty($uploadedAt)) {
                                    // Formatuj datę jeśli dostępna
                                    $date = new DateTime($uploadedAt);
                                    echo htmlspecialchars($date->format('d.m.Y H:i'));
                                } else {
                                    echo 'Brak daty';
                                }
                                ?>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mt-2">
                                <span class="badge bg-light text-dark border">
                                    <i class="bi bi-folder me-1"></i>
                                    <?php echo htmlspecialchars($image['category_name'] ?? 'Bez kategorii'); ?>
                                </span>
                                <div class="btn-group btn-group-sm">
                                    <?php if (!empty($imagePath)): ?>
                                        <a href="<?php echo htmlspecialchars($imagePath); ?>"
                                            class="btn btn-sm btn-outline-secondary"
                                            target="_blank"
                                            title="Otwórz w nowym oknie">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                    <?php else: ?>
                                        <button class="btn btn-sm btn-outline-secondary"
                                            disabled
                                            title="Brak obrazu">
                                            <i class="bi bi-eye-slash"></i>
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
</div>

<!-- Add Category Modal -->
<div class="modal fade" id="addCategoryModal" tabindex="-1" aria-labelledby="addCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="/gallery/categories/store" method="POST">
                <div class="modal-header">
                    <h5 class="modal-title" id="addCategoryModalLabel">
                        <i class="bi bi-folder-plus me-2"></i>Nowa kategoria
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="categoryName" class="form-label">Nazwa kategorii</label>
                        <div class="input-group">
                            <span class="input-group-text bg-light">
                                <i class="bi bi-folder text-primary"></i>
                            </span>
                            <input type="text" class="form-control" id="categoryName" name="name" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="categorySlug" class="form-label">Slug</label>
                        <div class="input-group">
                            <span class="input-group-text bg-light">
                                <i class="bi bi-link-45deg text-secondary"></i>
                            </span>
                            <input type="text" class="form-control font-monospace" id="categorySlug" name="slug" required>
                        </div>
                        <small class="form-text text-muted mt-1">
                            <i class="bi bi-info-circle-fill me-1"></i>
                            Wygenerowany automatycznie unikalny identyfikator używany w adresach URL
                        </small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x me-1"></i>Anuluj
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check2 me-1"></i>Dodaj kategorię
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        let selectedImages = [];

        // Initialize category filter
        const categoryFilter = document.getElementById('categoryFilter');
        if (categoryFilter) {
            categoryFilter.addEventListener('change', function() {
                this.form.submit();
            });
        }

        // Initialize image selection functionality
        document.querySelectorAll('.image-card').forEach(card => {
            card.addEventListener('click', function(event) {
                // Don't select if clicking on a button or link
                if (event.target.tagName === 'A' || event.target.tagName === 'BUTTON' ||
                    event.target.closest('a') || event.target.closest('button')) {
                    return;
                }

                const imageId = this.dataset.imageId;
                const index = selectedImages.indexOf(imageId);

                if (index > -1) {
                    // Deselect
                    selectedImages.splice(index, 1);
                    this.classList.remove('selected');
                } else {
                    // Select
                    selectedImages.push(imageId);
                    this.classList.add('selected');
                }

                updateSelectedCount();
            });
        });

        function updateSelectedCount() {
            const count = selectedImages.length;
            document.getElementById('selectedCount').innerText = count;
            const deleteButton = document.getElementById('deleteSelectedBtn');

            if (count > 0) {
                deleteButton.classList.remove('d-none');
            } else {
                deleteButton.classList.add('d-none');
            }
        }

        // Initialize delete button
        const deleteSelectedBtn = document.getElementById('deleteSelectedBtn');
        if (deleteSelectedBtn) {
            deleteSelectedBtn.addEventListener('click', function() {
                if (selectedImages.length > 0 && confirm('Czy na pewno chcesz usunąć wybrane zdjęcia?')) {
                    window.location.href = '/gallery/delete-multiple?ids=' + selectedImages.join(',');
                }
            });
        }

        // Handle delete confirmation
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.has('deleted')) {
            const toastEl = document.createElement('div');
            toastEl.innerHTML = `
            <div class="toast-container position-fixed bottom-0 end-0 p-3">
                <div class="toast align-items-center text-white bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="d-flex">
                        <div class="toast-body">
                            <i class="bi bi-check-circle-fill me-2"></i>
                            ${urlParams.get('deleted')} zdjęć zostało usuniętych.
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                </div>
            </div>
        `;
            document.body.appendChild(toastEl);

            const toast = new bootstrap.Toast(document.querySelector('.toast'));
            toast.show();

            // Clean up URL
            window.history.replaceState({}, document.title, window.location.pathname + window.location.search.replace(/&?deleted=\d+/, ''));
        }

        // Initialize slug generation
        const categoryNameInput = document.getElementById('categoryName');
        const categorySlugInput = document.getElementById('categorySlug');

        if (categoryNameInput && categorySlugInput) {
            categoryNameInput.addEventListener('input', function() {
                const nameInput = this.value;
                if (categorySlugInput.value === '' || !categorySlugInput.dataset.edited) {
                    const slug = nameInput
                        .toLowerCase()
                        .replace(/[^a-z0-9]+/g, '-')
                        .replace(/(^-|-$)/g, '');
                    categorySlugInput.value = slug;
                }
            });

            categorySlugInput.addEventListener('input', function() {
                this.dataset.edited = 'true';
            });
        }
    });
</script>

<?php
$content = ob_get_clean();
require_once __DIR__ . '/../LayoutView.php';
?>