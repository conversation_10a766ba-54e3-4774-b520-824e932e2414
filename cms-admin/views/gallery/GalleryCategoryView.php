<?php
$title = 'Galeria - ' . htmlspecialchars($category['name']);
$current_page = 'gallery';
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div class="d-flex align-items-center">
        <a href="/gallery" class="btn btn-sm btn-outline-secondary me-3">
            <i class="bi bi-arrow-left"></i>
        </a>
        <div>
            <h1 class="page-title mb-0">
                <i class="bi bi-folder-fill text-primary me-2"></i>
                <?php echo htmlspecialchars($category['name']); ?>
            </h1>
            <small class="text-muted">Kategoria galerii</small>
        </div>
    </div>
    <div class="d-flex gap-2">
        <button id="deleteSelectedBtn" class="btn btn-danger d-none">
            <i class="bi bi-trash me-1"></i> Usuń wybrane (<span id="selectedCount">0</span>)
        </button>
        <form id="uploadForm" action="/gallery/category/<?php echo $category['id']; ?>/upload" method="POST" enctype="multipart/form-data" class="d-inline">
            <label for="upload-images" class="btn btn-primary mb-0">
                <i class="bi bi-cloud-arrow-up me-1"></i> Dodaj zdjęcia
            </label>
            <input id="upload-images" type="file" name="images[]" accept="image/*" multiple class="d-none" onchange="document.getElementById('uploadForm').submit();" />
        </form>
    </div>
</div>

<?php if (isset($flash)): ?>
    <div class="alert alert-<?php echo $flash['type']; ?> alert-dismissible fade show shadow-sm" role="alert">
        <i class="bi bi-info-circle me-2"></i> <?php echo htmlspecialchars($flash['message']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<div class="card shadow-sm mb-4">
    <div class="card-body">
        <form method="GET" action="/gallery/category/<?php echo $category['id']; ?>" class="row g-3 align-items-end">
            <div class="col-md-10">
                <div class="input-group">
                    <span class="input-group-text bg-light border-end-0">
                        <i class="bi bi-search text-muted"></i>
                    </span>
                    <input class="form-control border-start-0 ps-0" type="search"
                        placeholder="Szukaj zdjęć w tej kategorii..."
                        aria-label="Search" name="search"
                        value="<?php echo htmlspecialchars($searchTerm ?? ''); ?>">
                </div>
            </div>
            <div class="col-md-2">
                <button class="btn btn-primary w-100" type="submit">
                    <i class="bi bi-search me-1"></i> Szukaj
                </button>
            </div>
        </form>
    </div>
</div>

<?php if (empty($images)): ?>
    <div class="text-center py-5">
        <div class="mb-4">
            <i class="bi bi-images text-muted" style="font-size: 4rem;"></i>
        </div>
        <h4 class="text-muted mb-3">Brak zdjęć w tej kategorii</h4>
        <p class="text-muted mb-4">Dodaj pierwsze zdjęcia do kategorii "<?php echo htmlspecialchars($category['name']); ?>".</p>
        <form id="uploadFormEmpty" action="/gallery/category/<?php echo $category['id']; ?>/upload" method="POST" enctype="multipart/form-data" class="d-inline">
            <label for="upload-images-empty" class="btn btn-primary btn-lg">
                <i class="bi bi-cloud-arrow-up me-2"></i> Dodaj pierwsze zdjęcia
            </label>
            <input id="upload-images-empty" type="file" name="images[]" accept="image/*" multiple class="d-none" onchange="document.getElementById('uploadFormEmpty').submit();" />
        </form>
    </div>
<?php else: ?>
    <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-4 g-4" id="galleryImagesGrid">
        <?php foreach ($images as $image): ?>
            <div class="col">
                <div class="image-card" data-image-id="<?php echo $image['id']; ?>">
                    <div class="position-relative">
                        <?php 
                        $imagePath = $image['filepath'] ?? (!empty($image['filename']) ? '/uploads/gallery/' . $image['filename'] : '');
                        $imageTitle = $image['title'] ?? 'Obraz galerii';
                        ?>
                        <img src="<?php echo htmlspecialchars($imagePath); ?>"
                            class="gallery-image"
                            alt="<?php echo htmlspecialchars($imageTitle); ?>"
                            onerror="this.src='data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'200\' height=\'150\' viewBox=\'0 0 200 150\'%3E%3Crect fill=\'%23f5f5f5\' width=\'200\' height=\'150\'/%3E%3Ctext x=\'50%25\' y=\'50%25\' dominant-baseline=\'middle\' text-anchor=\'middle\' fill=\'%23999\' font-family=\'Arial\' font-size=\'14\'%3EBrak obrazu%3C/text%3E%3C/svg%3E'">
                        <div class="position-absolute top-0 end-0 m-2">
                            <span class="badge bg-dark bg-opacity-75 rounded-pill">
                                <i class="bi bi-image me-1"></i>
                                <?php 
                                if (!empty($imagePath)) {
                                    echo strtoupper(pathinfo($imagePath, PATHINFO_EXTENSION));
                                } else {
                                    echo 'IMG';
                                }
                                ?>
                            </span>
                        </div>
                        <div class="position-absolute bottom-0 start-0 end-0 image-overlay p-2">
                            <div class="d-flex justify-content-between align-items-end">
                                <div class="text-white">
                                    <div class="fw-bold small mb-1"><?php echo htmlspecialchars($imageTitle); ?></div>
                                    <div class="small opacity-75">
                                        <i class="bi bi-calendar3 me-1"></i>
                                        <?php echo date('d.m.Y', strtotime($image['created_at'] ?? $image['uploaded_at'] ?? 'now')); ?>
                                    </div>
                                </div>
                                <div class="btn-group btn-group-sm">
                                    <?php if (!empty($imagePath)): ?>
                                        <a href="<?php echo htmlspecialchars($imagePath); ?>"
                                            class="btn btn-sm btn-light"
                                            target="_blank"
                                            title="Otwórz w nowym oknie">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                    <?php endif; ?>
                                    <a href="/gallery/delete/<?php echo $image['id']; ?>"
                                        class="btn btn-sm btn-danger"
                                        onclick="return confirm('Czy na pewno chcesz usunąć to zdjęcie?')"
                                        title="Usuń zdjęcie">
                                        <i class="bi bi-trash"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
<?php endif; ?>

<style>
.image-card {
    position: relative;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    cursor: pointer;
    transition: all 0.3s ease;
    height: 250px;
}

.image-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.image-card.selected {
    border: 3px solid var(--bs-primary);
}

.gallery-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.image-overlay {
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.image-card:hover .image-overlay {
    opacity: 1;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let selectedImages = [];

    // Initialize image selection functionality
    document.querySelectorAll('.image-card').forEach(card => {
        card.addEventListener('click', function(event) {
            // Don't select if clicking on a button or link
            if (event.target.tagName === 'A' || event.target.tagName === 'BUTTON' ||
                event.target.closest('a') || event.target.closest('button')) {
                return;
            }

            const imageId = this.dataset.imageId;
            const index = selectedImages.indexOf(imageId);

            if (index > -1) {
                // Deselect
                selectedImages.splice(index, 1);
                this.classList.remove('selected');
            } else {
                // Select
                selectedImages.push(imageId);
                this.classList.add('selected');
            }

            updateSelectedCount();
        });
    });

    function updateSelectedCount() {
        const count = selectedImages.length;
        document.getElementById('selectedCount').textContent = count;
        
        const deleteBtn = document.getElementById('deleteSelectedBtn');
        if (count > 0) {
            deleteBtn.classList.remove('d-none');
        } else {
            deleteBtn.classList.add('d-none');
        }
    }

    // Handle delete selected images
    document.getElementById('deleteSelectedBtn').addEventListener('click', function() {
        if (selectedImages.length === 0) return;
        
        const count = selectedImages.length;
        if (confirm(`Czy na pewno chcesz usunąć ${count} wybranych zdjęć?`)) {
            const categoryId = <?php echo $category['id']; ?>;
            window.location.href = `/gallery/delete-multiple?ids=${selectedImages.join(',')}&category_id=${categoryId}`;
        }
    });
});
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../LayoutView.php';
?> 