<?php
$title = isset($staticPage) ? 'Ed<PERSON><PERSON>j Stronę Statyczną' : 'Dodaj Nową Stronę Statyczną';
$current_page = 'pages';

$additional_css = '
    <style>
        .CodeMirror {
            height: 300px;
            border-radius: 0.375rem;
            border: 1px solid #dee2e6;
        }
        .editor-toolbar {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem 0.375rem 0 0;
            border-bottom: none;
        }
    </style>
';

$additional_head = '
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/simplemde/latest/simplemde.min.css">
';

ob_start();
?>

<div class="d-flex align-items-center mb-4">
    <a href="/pages" class="btn btn-sm btn-outline-secondary me-2">
        <i class="bi bi-arrow-left"></i>
    </a>
    <h1 class="page-title mb-0"><?php echo $title; ?></h1>
</div>

<?php if (isset($flash)): ?>
    <div class="alert alert-<?php echo $flash['type']; ?> alert-dismissible fade show shadow-sm" role="alert">
        <i class="bi bi-info-circle me-2"></i> <?php echo htmlspecialchars($flash['message']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<form action="<?php echo isset($staticPage) ? cms_admin_url('pages/update/' . $staticPage['id']) : cms_admin_url('pages/store'); ?>" method="POST">
    <div class="row g-4">
        <!-- Main Content Column -->
        <div class="col-lg-8">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white py-3">
                    <h5 class="card-title mb-0 fw-bold">
                        <i class="bi bi-pencil-square me-2"></i>Podstawowe informacje
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3 mb-4">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="title" class="form-label fw-medium mb-2">Tytuł</label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light">
                                        <i class="bi bi-type-h1 text-primary"></i>
                                    </span>
                                    <input type="text" class="form-control" id="title" name="title"
                                        value="<?php echo htmlspecialchars($staticPage['title'] ?? ''); ?>"
                                        oninput="generateSlug(this.value)" required>
                                </div>
                                <small class="text-muted">Wprowadź tytuł strony, który będzie widoczny dla użytkowników</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="slug" class="form-label fw-medium mb-2">Slug</label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light">
                                        <i class="bi bi-link-45deg text-secondary"></i>
                                    </span>
                                    <input type="text" class="form-control font-monospace" id="slug" name="slug"
                                        value="<?php echo htmlspecialchars($staticPage['slug'] ?? ''); ?>" required>
                                </div>
                                <small class="text-muted">Unikalny identyfikator używany w adresach URL</small>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="content" class="form-label fw-medium mb-2">Treść strony</label>
                        <textarea class="form-control" id="content" name="content" rows="10"><?php echo htmlspecialchars($staticPage['content'] ?? ''); ?></textarea>
                        <small class="text-muted mt-2">Użyj Markdown do formatowania tekstu lub edytora wizualnego powyżej</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar Options Column -->
        <div class="col-lg-4">
            <!-- Publish Card -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white py-3">
                    <h5 class="card-title mb-0 fw-bold">
                        <i class="bi bi-send me-2"></i>Publikacja
                    </h5>
                </div>
                <div class="card-body">
                    <div class="form-check form-switch mb-3">
                        <input type="checkbox" class="form-check-input" id="published" name="published" value="1"
                            <?php echo (isset($staticPage['published']) && $staticPage['published']) ? 'checked' : ''; ?>>
                        <label class="form-check-label" for="published">Opublikowana</label>
                    </div>
                    <div class="alert alert-info" role="alert">
                        <div class="d-flex">
                            <i class="bi bi-info-circle me-2 fs-5"></i>
                            <div>
                                <strong>Status:</strong>
                                <?php if (isset($staticPage['published']) && $staticPage['published']): ?>
                                    <span class="text-success">Opublikowana</span>
                                <?php else: ?>
                                    <span class="text-secondary">Szkic</span>
                                <?php endif; ?>
                                <?php if (isset($staticPage['created_at'])): ?>
                                    <div class="mt-2 small">Utworzona: <?php echo htmlspecialchars($staticPage['created_at']); ?></div>
                                <?php endif; ?>
                                <?php if (isset($staticPage['updated_at'])): ?>
                                    <div class="small">Zaktualizowana: <?php echo htmlspecialchars($staticPage['updated_at']); ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Save Button -->
            <div class="d-grid gap-2">
                <button type="submit" class="btn btn-primary py-2">
                    <i class="bi bi-save me-2"></i>Zapisz stronę
                </button>
                <a href="/pages" class="btn btn-outline-secondary">
                    <i class="bi bi-x-circle me-2"></i>Anuluj
                </a>
            </div>
        </div>
    </div>
</form>

<?php
$content = ob_get_clean();

$additional_scripts = <<<JS
    <script src="https://cdn.jsdelivr.net/simplemde/latest/simplemde.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            try {
                console.log("Initializing WYSIWYG editor for static page...");
                
                // Get the page ID from the form action URL
                let pageId = 'new';
                const formAction = document.querySelector('form').action;
                if (formAction) {
                    const parts = formAction.split('/');
                    if (parts.length > 0) {
                        const lastPart = parts[parts.length - 1];
                        if (lastPart && !isNaN(parseInt(lastPart))) {
                            pageId = lastPart;
                        }
                    }
                }
                
                // Initialize Markdown Editor
                var simplemde = new SimpleMDE({
                    element: document.getElementById("content"),
                    spellChecker: false,
                    autosave: {
                        enabled: true,
                        uniqueId: "page-" + pageId,
                        delay: 1000,
                    },
                    toolbar: [
                        "bold", "italic", "heading", "|",
                        "quote", "unordered-list", "ordered-list", "|",
                        "link", "image", "table", "|",
                        "preview", "side-by-side", "fullscreen", "|",
                        "guide"
                    ]
                });
                
                console.log("WYSIWYG editor initialized successfully");
            } catch (error) {
                console.error("Error initializing editor:", error);
            }

            // Generate slug from title
            function generateSlug(title) {
                const slugField = document.getElementById('slug');
                
                // Only generate if the slug field hasn't been manually edited
                if (!slugField.dataset.userEdited) {
                    const slug = title
                        .trim()
                        .toLowerCase()
                        .replace(/[^a-z0-9\s-]/g, '')  // Remove special chars except spaces and hyphens
                        .replace(/\s+/g, '-')          // Replace spaces with hyphens
                        .replace(/-+/g, '-')           // Replace multiple hyphens with a single one
                        .replace(/^-+|-+$/g, '');      // Remove hyphens from start and end
                    
                    slugField.value = slug;
                }
            }

            // Mark slug as manually edited
            document.getElementById('slug').addEventListener('input', function() {
                this.dataset.userEdited = 'true';
            });

            // Make generateSlug function globally available
            window.generateSlug = generateSlug;
        });
    </script>
JS;

require_once __DIR__ . '/../LayoutView.php';
?>