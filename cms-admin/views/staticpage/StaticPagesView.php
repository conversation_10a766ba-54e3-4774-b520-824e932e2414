<?php
$translations = include __DIR__ . '/../../lang/pl.php';

$title = $translations['pages.title'] ?? 'Strony Statyczne';
$current_page = 'pages';

ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="page-title">Strony Statyczne</h1>
    <a href="/pages/new" class="btn btn-primary">
        <i class="bi bi-file-earmark-plus me-1"></i> Nowa strona
    </a>
</div>

<?php if (isset($flash)): ?>
    <div class="alert alert-<?php echo $flash['type']; ?> alert-dismissible fade show shadow-sm" role="alert">
        <i class="bi bi-info-circle me-2"></i> <?php echo htmlspecialchars($flash['message']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<div class="card shadow-sm">
    <div class="table-responsive">
        <table class="table table-striped table-hover mb-0">
            <thead>
                <tr>
                    <th scope="col">Tytuł</th>
                    <th scope="col">Slug</th>
                    <th scope="col">Status</th>
                    <th scope="col">Ostatnia aktualizacja</th>
                    <th scope="col" class="text-end">Akcje</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($staticPages)): ?>
                    <tr>
                        <td colspan="5" class="text-center py-4">
                            <div class="text-muted">
                                <i class="bi bi-file-earmark-x fs-3 d-block mb-2"></i>
                                Nie znaleziono żadnych stron statycznych
                            </div>
                        </td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($staticPages as $page): ?>
                        <tr>
                            <td class="align-middle fw-medium">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-file-earmark-text me-2 text-primary"></i>
                                    <?php echo htmlspecialchars($page['title']); ?>
                                </div>
                            </td>
                            <td class="align-middle">
                                <code><?php echo htmlspecialchars($page['slug']); ?></code>
                            </td>
                            <td class="align-middle">
                                <a href="/pages/toggle-published/<?php echo $page['id']; ?>"
                                    class="badge text-decoration-none <?php echo $page['published'] ? 'bg-success' : 'bg-warning text-dark'; ?>">
                                    <?php if ($page['published']): ?>
                                        <i class="bi bi-check-circle me-1"></i> Opublikowany
                                    <?php else: ?>
                                        <i class="bi bi-pencil-square me-1"></i> Szkic
                                    <?php endif; ?>
                                </a>
                            </td>
                            <td class="align-middle">
                                <span class="text-muted small">
                                    <i class="bi bi-calendar3 me-1"></i>
                                    <?php echo htmlspecialchars($page['updated_at']); ?>
                                </span>
                            </td>
                            <td class="align-middle text-end">
                                <div class="btn-group">
                                    <a href="/preview/<?php echo $page['slug']; ?>"
                                        class="btn btn-sm btn-secondary"
                                        target="_blank">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="/pages/edit/<?php echo $page['id']; ?>"
                                        class="btn btn-sm btn-primary">
                                        <i class="bi bi-pencil-square"></i>
                                    </a>
                                    <a href="/pages/delete/<?php echo $page['id']; ?>"
                                        class="btn btn-sm btn-danger"
                                        onclick="return confirm('Czy na pewno chcesz usunąć tę stronę?');">
                                        <i class="bi bi-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../views/LayoutView.php';
?>