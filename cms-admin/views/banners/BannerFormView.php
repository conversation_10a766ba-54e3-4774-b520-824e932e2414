<?php
$translations = include __DIR__ . '/../../lang/pl.php';
$title = isset($banner) ? 'Edytuj Banner' : 'Dodaj Nowy Banner';
$current_page = 'banners';
ob_start();
?>

<div class="d-flex align-items-center mb-4">
    <a href="/banners" class="btn btn-sm btn-outline-secondary me-2">
        <i class="bi bi-arrow-left"></i>
    </a>
    <h1 class="page-title mb-0"><?php echo $title; ?></h1>
</div>

<?php if (isset($flash)): ?>
    <div class="alert alert-<?php echo $flash['type']; ?> alert-dismissible fade show shadow-sm" role="alert">
        <i class="bi bi-info-circle me-2"></i> <?php echo htmlspecialchars($flash['message']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<div class="card shadow-sm">
    <div class="card-body">
        <form action="<?php echo isset($banner) ? '/banners/update/' . $banner['id'] : '/banners/store'; ?>" method="POST" enctype="multipart/form-data">
            <div class="mb-3">
                <label for="title" class="form-label">Tytuł</label>
                <input type="text" class="form-control" id="title" name="title"
                    value="<?php echo htmlspecialchars($banner['title'] ?? ''); ?>" required>
            </div>

            <div class="mb-3">
                <label for="content" class="form-label">Treść</label>
                <textarea class="form-control" id="content" name="content" rows="3"><?php echo htmlspecialchars($banner['content'] ?? ''); ?></textarea>
            </div>

            <div class="mb-3">
                <label for="position" class="form-label">Pozycja</label>
                <input type="number" class="form-control" id="position" name="position"
                    value="<?php echo htmlspecialchars($banner['position'] ?? '0'); ?>" min="0">
            </div>

            <div class="mb-3">
                <label for="image" class="form-label">Obraz</label>
                <input type="file" class="form-control" id="image" name="image" accept="image/*"
                    <?php echo !isset($banner) ? 'required' : ''; ?>>
                <?php if (isset($banner) && !empty($banner['image'])): ?>
                    <div class="mt-2">
                        <img src="/cms-content/uploads/banners/<?php echo htmlspecialchars($banner['image']); ?>"
                            alt="Current banner image" class="img-thumbnail" style="max-height: 100px;">
                        <p class="text-muted mt-1">Aktualny obraz. Wybierz nowy, jeśli chcesz go zmienić.</p>
                    </div>
                <?php endif; ?>
            </div>

            <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="active" name="active" value="1"
                    <?php echo (isset($banner['active']) && $banner['active']) ? 'checked' : ''; ?>>
                <label class="form-check-label" for="active">Aktywny</label>
            </div>

            <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-check-lg me-1"></i>
                    <?php echo isset($banner) ? 'Zaktualizuj banner' : 'Utwórz banner'; ?>
                </button>
                <a href="/banners" class="btn btn-secondary">
                    <i class="bi bi-x-lg me-1"></i>
                    Anuluj
                </a>
            </div>
        </form>
    </div>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../LayoutView.php';
?>