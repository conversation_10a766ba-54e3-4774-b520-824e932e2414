# Funkcjonalność uploadu plików w stronach statycznych

## Przegląd zmian

Strony statyczne zostały rozszerzone o taką samą funkcjonalność uploadu plików jak artykuły. Użytkownicy mogą teraz dodawać zdjęcia i pliki do stron statycznych.

## Nowe funkcjonalności

### Upload zdjęć
- Obsługa formatów: JPG, PNG, GIF, WebP
- Upload wielu plików jednocześnie
- Podgląd miniatur
- Modal do powiększania zdjęć
- Możliwość usuwania istniejących zdjęć

### Upload plików
- Obsługa dokumentów: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX
- Obsługa archiwów: ZIP, RAR, 7Z
- Obsługa plików tekstowych: TXT, CSV
- Upload wielu plików jednocześnie
- Linki do pobrania plików
- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> usuwania istniejących plików

### Ograniczenia bezpieczeństwa
- Maksymalny rozmiar pliku: 10MB
- Walidacja typu MIME po stronie serwera
- Sprawdzanie rozszerzenia pliku
- Ochrona przed przesłaniem szkodliwych plików

## Zmiany w bazie danych

### Nowa tabela: `static_page_files`
```sql
CREATE TABLE static_page_files (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    static_page_id INTEGER NOT NULL,
    filename TEXT NOT NULL,
    original_filename TEXT NOT NULL,
    filepath TEXT NOT NULL,
    file_type TEXT NOT NULL, -- 'image' or 'attachment'
    mime_type TEXT,
    file_size INTEGER,
    position INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (static_page_id) REFERENCES static_pages(id) ON DELETE CASCADE
);
```

## Zmiany w kodzie

### Model StaticPage.php
- Dodano konstruktor z `$uploadDir`
- Dodano `uploadMultipleFiles()` - upload wielu plików
- Dodano `saveFileToDatabase()` - zapis informacji o pliku do bazy
- Dodano `getStaticPageFiles()` - pobieranie plików strony
- Dodano `deleteStaticPageFile()` - usuwanie pliku
- Dodano `updateFilePositions()` - aktualizacja pozycji plików

### Kontroler StaticPageController.php
- Rozszerzono `form()` o pobieranie istniejących plików
- Rozszerzono `store()` o obsługę uploadu plików
- Rozszerzono `update()` o obsługę uploadu plików
- Dodano `deleteFile()` - AJAX endpoint do usuwania plików
- Dodano `updateFilePositions()` - AJAX endpoint do zmiany kolejności

### Widok StaticPagesFormView.php
- Dodano `enctype="multipart/form-data"` do formularza
- Dodano sekcję "Media" z uploadem zdjęć i plików
- Dodano modal do podglądu zdjęć
- Dodano JavaScript do obsługi uploadu i preview
- Dodano funkcje do usuwania plików przez AJAX

### Routing (index.php)
- Dodano `POST /pages/files/delete/{fileId}` - usuwanie pliku
- Dodano `POST /pages/files/reorder` - zmiana kolejności plików
- Dodano `GET /pages/{id}` - alternatywny routing do edycji

## Struktura plików

Przesłane pliki są zapisywane w katalogu:
```
public/uploads/static_pages/
├── 64a1b2c3d4e5f_dokument.pdf
├── 64a1b2c3d4e5g_zdjecie.jpg
└── 64a1b2c3d4e5h_arkusz.xlsx
```

## Testowanie

1. Uruchom serwer: `cd php/public && php -S localhost:8000`
2. Zaloguj się do panelu administracyjnego
3. Przejdź do "Strony statyczne"
4. Edytuj istniejącą stronę lub utwórz nową
5. W sekcji "Media" przetestuj upload zdjęć i plików
6. Sprawdź funkcje podglądu, usuwania i pobierania

## Zgodność

Funkcjonalność jest w pełni zgodna z istniejącym systemem uploadu artykułów i używa tych samych standardów bezpieczeństwa i interfejsu użytkownika. 