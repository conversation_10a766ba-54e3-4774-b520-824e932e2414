<?php

class Gallery {
    private $db;
    private $uploadDir;

    public function __construct($dbConnection) {
        $this->db = $dbConnection;
        // Użyj nowej funkcji pomocniczej do uzyskania ścieżki uploads
        $this->uploadDir = cms_get_upload_path('gallery');
    }

    // --- Category Methods ---

    public function getAllCategories() {
        $stmt = $this->db->query("SELECT * FROM gallery_categories");
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getCategoryById($id) {
        $stmt = $this->db->prepare("SELECT * FROM gallery_categories WHERE id = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function createCategory($name, $slug) {
        // Basic check for slug uniqueness
        if ($this->getCategoryBySlug($slug)) {
            return false; // Slug already exists
        }

        $stmt = $this->db->prepare("INSERT INTO gallery_categories (name, slug) VALUES (:name, :slug)");
        $stmt->bindParam(':name', $name, PDO::PARAM_STR);
        $stmt->bindParam(':slug', $slug, PDO::PARAM_STR);
        return $stmt->execute();
    }

    public function updateCategory($id, $name, $slug) {
        // Basic check for slug uniqueness, excluding current category
        $existingCategory = $this->getCategoryBySlug($slug);
        if ($existingCategory && $existingCategory['id'] != $id) {
            return false; // Slug already exists for a different category
        }

        $stmt = $this->db->prepare("UPDATE gallery_categories SET name = :name, slug = :slug WHERE id = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->bindParam(':name', $name, PDO::PARAM_STR);
        $stmt->bindParam(':slug', $slug, PDO::PARAM_STR);
        return $stmt->execute();
    }

    public function deleteCategory($id) {
        // TODO: Consider handling images associated with this category (e.g., set category_id to NULL or delete images)
        $stmt = $this->db->prepare("DELETE FROM gallery_categories WHERE id = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        return $stmt->execute();
    }

    public function getCategoryBySlug($slug) {
        $stmt = $this->db->prepare("SELECT * FROM gallery_categories WHERE slug = :slug");
        $stmt->bindParam(':slug', $slug, PDO::PARAM_STR);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function getImageCountByCategoryId() {
        $stmt = $this->db->query("SELECT category_id, COUNT(*) as count FROM gallery_images GROUP BY category_id");
        $counts = $stmt->fetchAll(PDO::FETCH_KEY_PAIR); // {category_id: count}
        return $counts;
    }


    // --- Image Methods ---

    public function getAllImages() {
        $stmt = $this->db->query("SELECT gi.*, gc.name as category_name FROM gallery_images gi LEFT JOIN gallery_categories gc ON gi.category_id = gc.id ORDER BY gi.created_at DESC");
        $images = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Dodaj filepath jeśli nie istnieje w bazie danych
        foreach ($images as &$image) {
            if (empty($image['filepath']) && !empty($image['filename'])) {
                $image['filepath'] = '/cms-content/uploads/gallery/' . $image['filename'];
            }
            // Dodaj uploaded_at z created_at jeśli nie istnieje
            if (empty($image['uploaded_at']) && !empty($image['created_at'])) {
                $image['uploaded_at'] = $image['created_at'];
            }
        }

        return $images;
    }

    public function getImagesByCategoryId($categoryId) {
        $stmt = $this->db->prepare("SELECT gi.*, gc.name as category_name FROM gallery_images gi LEFT JOIN gallery_categories gc ON gi.category_id = gc.id WHERE gi.category_id = :category_id ORDER BY gi.created_at DESC");
        $stmt->bindParam(':category_id', $categoryId, PDO::PARAM_INT);
        $stmt->execute();
        $images = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Dodaj filepath jeśli nie istnieje w bazie danych
        foreach ($images as &$image) {
            if (empty($image['filepath']) && !empty($image['filename'])) {
                $image['filepath'] = '/cms-content/uploads/gallery/' . $image['filename'];
            }
            // Dodaj uploaded_at z created_at jeśli nie istnieje
            if (empty($image['uploaded_at']) && !empty($image['created_at'])) {
                $image['uploaded_at'] = $image['created_at'];
            }
        }

        return $images;
    }

    public function getImageById($id) {
        $stmt = $this->db->prepare("SELECT * FROM gallery_images WHERE id = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }


    public function createImage($title, $filename, $filepath, $categoryId = null) {
        // Sprawdź czy tabela ma kolumnę filepath
        try {
            $stmt = $this->db->prepare("INSERT INTO gallery_images (title, filename, filepath, category_id) VALUES (:title, :filename, :filepath, :category_id)");
            $stmt->bindParam(':title', $title, PDO::PARAM_STR);
            $stmt->bindParam(':filename', $filename, PDO::PARAM_STR);
            $stmt->bindParam(':filepath', $filepath, PDO::PARAM_STR);
            $stmt->bindParam(':category_id', $categoryId, $categoryId ? PDO::PARAM_INT : PDO::PARAM_NULL);
            return $stmt->execute();
        } catch (PDOException $e) {
            // Fallback dla starej struktury tabeli (bez kolumny filepath)
            $stmt = $this->db->prepare("INSERT INTO gallery_images (title, filename, category_id) VALUES (:title, :filename, :category_id)");
            $stmt->bindParam(':title', $title, PDO::PARAM_STR);
            $stmt->bindParam(':filename', $filename, PDO::PARAM_STR);
            $stmt->bindParam(':category_id', $categoryId, $categoryId ? PDO::PARAM_INT : PDO::PARAM_NULL);
            return $stmt->execute();
        }
    }

    public function uploadImage($file) {
        if ($file['error'] !== UPLOAD_ERR_OK) {
            // Handle upload errors
            return false;
        }

        $filename = uniqid() . '_' . basename($file['name']);
        $filepath = $this->uploadDir . $filename;

        if (move_uploaded_file($file['tmp_name'], $filepath)) {
            // Return filename and relative path for database
            return [
                'filename' => $filename,
                'filepath' => '/cms-content/uploads/gallery/' . $filename // Store relative path in DB
            ];
        } else {
            // Handle move failed error
            return false;
        }
    }


    public function deleteImage($id) {
        $image = $this->getImageById($id);
        if (!$image) {
            return false; // Image not found
        }

        // Delete file from server if filename exists
        if (!empty($image['filename'])) {
            $fullFilepath = $this->uploadDir . $image['filename'];
            if (file_exists($fullFilepath)) {
                unlink($fullFilepath);
            }
        }

        // Delete record from database
        $stmt = $this->db->prepare("DELETE FROM gallery_images WHERE id = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        return $stmt->execute();
    }

    public function searchImages($searchTerm, $categoryId = null) {
        $sql = "SELECT gi.*, gc.name as category_name FROM gallery_images gi LEFT JOIN gallery_categories gc ON gi.category_id = gc.id WHERE gi.title LIKE :searchTerm";
        $params = [':searchTerm' => '%' . $searchTerm . '%'];

        if ($categoryId !== null && $categoryId !== 'all') {
            $sql .= " AND gi.category_id = :category_id";
            $params[':category_id'] = $categoryId;
        }

        $sql .= " ORDER BY gi.created_at DESC";

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        $images = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Dodaj filepath jeśli nie istnieje w bazie danych
        foreach ($images as &$image) {
            if (empty($image['filepath']) && !empty($image['filename'])) {
                $image['filepath'] = '/cms-content/uploads/gallery/' . $image['filename'];
            }
            // Dodaj uploaded_at z created_at jeśli nie istnieje
            if (empty($image['uploaded_at']) && !empty($image['created_at'])) {
                $image['uploaded_at'] = $image['created_at'];
            }
        }

        return $images;
    }

    /**
     * Count all gallery images
     * @return int Number of images in gallery
     */
    public function countAll() {
        $stmt = $this->db->query("SELECT COUNT(*) FROM gallery_images");
        return $stmt->fetchColumn();
    }

    // Metody dla frontendu
    public function getPublishedCategories() {
        $stmt = $this->db->query("SELECT * FROM gallery_categories ORDER BY name");
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getPublishedImages($categoryId = null, $limit = null) {
        $sql = "SELECT gi.*, gc.name as category_name, gc.slug as category_slug
                FROM gallery_images gi
                LEFT JOIN gallery_categories gc ON gi.category_id = gc.id";

        $params = [];

        if ($categoryId) {
            $sql .= " WHERE gi.category_id = :category_id";
            $params[':category_id'] = $categoryId;
        }

        $sql .= " ORDER BY gi.position ASC, gi.created_at DESC";

        if ($limit) {
            $sql .= " LIMIT :limit";
            $params[':limit'] = $limit;
        }

        $stmt = $this->db->prepare($sql);

        foreach ($params as $key => $value) {
            if ($key === ':limit') {
                $stmt->bindParam($key, $value, PDO::PARAM_INT);
            } else {
                $stmt->bindValue($key, $value);
            }
        }

        $stmt->execute();
        $images = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Dodaj filepath jeśli nie istnieje w bazie danych
        foreach ($images as &$image) {
            if (empty($image['filepath']) && !empty($image['filename'])) {
                $image['filepath'] = '/cms-content/uploads/gallery/' . $image['filename'];
            }
        }

        return $images;
    }
}
