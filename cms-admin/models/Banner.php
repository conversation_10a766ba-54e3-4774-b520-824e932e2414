<?php

class Banner {
    private $db;
    private $uploadDir;

    public function __construct($dbConnection) {
        $this->db = $dbConnection;
        // Użyj nowej funkcji pomocniczej do uzyskania ścieżki uploads
        $this->uploadDir = cms_get_upload_path('banners');
    }

    public function getUploadDir() {
        return $this->uploadDir;
    }

    public function getAllBanners() {
        $stmt = $this->db->query("SELECT * FROM banners ORDER BY created_at DESC");
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getBannerById($id) {
        $stmt = $this->db->prepare("SELECT * FROM banners WHERE id = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function createBanner($title, $content, $image, $position, $active) {
        $stmt = $this->db->prepare("INSERT INTO banners (title, content, image, position, active) VALUES (:title, :content, :image, :position, :active)");
        $stmt->bindParam(':title', $title, PDO::PARAM_STR);
        $stmt->bindParam(':content', $content, PDO::PARAM_STR);
        $stmt->bindParam(':image', $image, PDO::PARAM_STR);
        $stmt->bindParam(':position', $position, PDO::PARAM_INT);
        $stmt->bindParam(':active', $active, PDO::PARAM_INT);
        return $stmt->execute();
    }

    public function updateBanner($id, $title, $content, $image, $position, $active) {
        $stmt = $this->db->prepare("UPDATE banners SET title = :title, content = :content, image = :image, position = :position, active = :active WHERE id = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->bindParam(':title', $title, PDO::PARAM_STR);
        $stmt->bindParam(':content', $content, PDO::PARAM_STR);
        $stmt->bindParam(':image', $image, PDO::PARAM_STR);
        $stmt->bindParam(':position', $position, PDO::PARAM_INT);
        $stmt->bindParam(':active', $active, PDO::PARAM_INT);
        return $stmt->execute();
    }

    public function deleteBanner($id) {
        $banner = $this->getBannerById($id);
        if (!$banner) {
            return false; // Banner not found
        }

        // Delete file from server if exists
        if (!empty($banner['image'])) {
            $fullFilepath = $this->uploadDir . $banner['image'];
            if (file_exists($fullFilepath)) {
                unlink($fullFilepath);
            }
        }

        // Delete record from database
        $stmt = $this->db->prepare("DELETE FROM banners WHERE id = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        return $stmt->execute();
    }

    public function uploadImage($file) {
        if ($file['error'] !== UPLOAD_ERR_OK) {
            // Handle upload errors
            return false;
        }

        $filename = uniqid() . '_' . basename($file['name']);
        $filepath = $this->uploadDir . $filename;

        if (move_uploaded_file($file['tmp_name'], $filepath)) {
            // Return filename and relative path for database
            return [
                'filename' => $filename,
                'filepath' => '/cms-content/uploads/banners/' . $filename // Store relative path in DB
            ];
        } else {
            // Handle move failed error
            return false;
        }
    }

    public function updateActiveStatus($id, $active) {
        $stmt = $this->db->prepare("UPDATE banners SET active = :active WHERE id = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->bindParam(':active', $active, PDO::PARAM_INT);
        return $stmt->execute();
    }

    /**
     * Count all banners
     * @return int Number of banners
     */
    public function countAll() {
        $stmt = $this->db->query("SELECT COUNT(*) FROM banners");
        return $stmt->fetchColumn();
    }

    // Metody dla frontendu
    public function getActiveBanners() {
        $stmt = $this->db->query("SELECT * FROM banners WHERE active = 1 ORDER BY position ASC, created_at DESC");
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
