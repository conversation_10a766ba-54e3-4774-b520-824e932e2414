<?php

class Controller {
    protected $db;

    public function __construct($dbConnection) {
        $this->db = $dbConnection;
    }

    protected function renderView($viewName, $data = []) {
        // Extract data variables so they can be used directly in the view file
        extract($data);

        // Pass flash messages to the view and clear them from session
        $flashMessage = $this->getFlashMessage();
        if ($flashMessage) {
            extract(['flash' => $flashMessage]); // Make $flash available in the view
        }

        // Only handle new-style view names (ending with View)
        if (!str_ends_with($viewName, 'View')) {
            throw new Exception("View name must end with 'View'");
        }

        // Get controller name without 'Controller' suffix
        $controllerName = str_replace('Controller', '', get_class($this));
        $controllerName = strtolower($controllerName);

        // First try to find view in controller-specific subdirectory
        $viewFile = CMS_ADMIN_PATH . '/views/' . $controllerName . '/' . $viewName . '.php';

        if (!file_exists($viewFile)) {
            // Fallback to root views directory
            $viewFile = CMS_ADMIN_PATH . '/views/' . $viewName . '.php';
        }

        if (file_exists($viewFile)) {
            require $viewFile;
        } else {
            // View file not found
            http_response_code(500);
            echo "Error: View file not found for " . $viewName;
        }
    }

    // Set a flash message in the session
    protected function setFlashMessage($message, $type = 'success') // type can be 'success', 'error', 'warning', 'info'
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        $_SESSION['flash_message'] = ['message' => $message, 'type' => $type];
    }

    // Get and clear the flash message from the session
    protected function getFlashMessage() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        if (isset($_SESSION['flash_message'])) {
            $message = $_SESSION['flash_message'];
            unset($_SESSION['flash_message']); // Clear the message after getting it
            return $message;
        }
        return null;
    }
}
