<?php

class Database {
    private $connection;
    private $dbFile;

    public function __construct() {
        $this->dbFile = DB_FILE;
    }

    public function getConnection() {
        if ($this->connection === null) {
            try {
                $this->connection = new PDO('sqlite:' . $this->dbFile);
                $this->connection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                $this->connection->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
            } catch (PDOException $e) {
                die('Database connection failed: ' . $e->getMessage());
            }
        }
        return $this->connection;
    }
}