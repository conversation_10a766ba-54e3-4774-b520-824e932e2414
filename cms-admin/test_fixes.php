<?php
/**
 * Test script do sprawdzenia czy naprawione błędy działają poprawnie
 */

define('BASE_PATH', __DIR__);
require_once __DIR__ . '/app/Database.php';
require_once __DIR__ . '/models/Gallery.php';
require_once __DIR__ . '/models/Menu.php';
require_once __DIR__ . '/models/Banner.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "=== Test naprawionych błędów ===\n\n";
    
    // Test 1: Gallery Model
    echo "1. Test Gallery Model:\n";
    $gallery = new Gallery($db);
    $images = $gallery->getAllImages();
    echo "   ✓ getAllImages() działa - pobrano " . count($images) . " obrazów\n";
    
    $categories = $gallery->getAllCategories();
    echo "   ✓ getAllCategories() działa - pobrano " . count($categories) . " kategorii\n";
    
    // Test 2: Menu Model
    echo "\n2. Test Menu Model:\n";
    $menu = new Menu($db);
    $menus = $menu->getAllMenus();
    echo "   ✓ getAllMenus() działa - pobrano " . count($menus) . " menu\n";
    
    if (!empty($menus)) {
        $firstMenu = $menus[0];
        $items = $menu->getMenuItemsByMenuId($firstMenu['id']);
        echo "   ✓ getMenuItemsByMenuId() działa - pobrano " . count($items) . " elementów dla menu '{$firstMenu['name']}'\n";
    }
    
    // Test 3: Banner Model
    echo "\n3. Test Banner Model:\n";
    $banner = new Banner($db);
    $banners = $banner->getAllBanners();
    echo "   ✓ getAllBanners() działa - pobrano " . count($banners) . " bannerów\n";
    
    echo "\n4. Test struktury danych:\n";
    if (!empty($menus)) {
        $firstMenu = $menus[0];
        echo "   ✓ Menu ma kolumny: " . implode(', ', array_keys($firstMenu)) . "\n";
    }
    
    if (!empty($banners)) {
        $firstBanner = $banners[0];
        echo "   ✓ Banner ma kolumny: " . implode(', ', array_keys($firstBanner)) . "\n";
    }
    
    if (!empty($categories)) {
        $firstCategory = $categories[0];
        echo "   ✓ Gallery Category ma kolumny: " . implode(', ', array_keys($firstCategory)) . "\n";
    }
    
    echo "\n=== Wszystkie testy zakończone pomyślnie! ===\n";
    
} catch (Exception $e) {
    echo "BŁĄD: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
} 