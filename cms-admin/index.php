<?php
/**
 * Router backendu - Panel administracyjny
 */

// Załaduj konfigurację
require_once dirname(__DIR__) . '/cms-config.php';

// <PERSON><PERSON> deweloperski
define('CMS_DEBUG', true);

// Obsługa plików statycznych dla wbudowanego serwera PHP
if (php_sapi_name() === 'cli-server') {
    $uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
    $uri = urldecode($uri);
    $file_path = __DIR__ . $uri;
    
    if (is_file($file_path)) {
        $ext = pathinfo($file_path, PATHINFO_EXTENSION);
        $content_types = [
            'css' => 'text/css',
            'js' => 'application/javascript',
            'png' => 'image/png',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'gif' => 'image/gif'
        ];
        
        if (isset($content_types[$ext])) {
            header('Content-Type: ' . $content_types[$ext]);
        }
        readfile($file_path);
        exit;
    }
}

// Załaduj klasy aplikacji
require_once __DIR__ . '/app/Router.php';
require_once __DIR__ . '/app/Controller.php';
require_once __DIR__ . '/app/View.php';
require_once __DIR__ . '/app/Database.php';

// Rozpocznij sesję
session_start();

// Inicjalizacja bazy danych
$db = (new Database())->getConnection();
$router = new Router($db);

// Trasy publiczne
$router->addRoute('GET', '/login', 'AuthController@loginForm');
$router->addRoute('POST', '/login', 'AuthController@login');

// Middleware autoryzacji
if (!isset($_SESSION['user_id'])) {
    $requestUri = $_SERVER['REQUEST_URI'];
    if ($requestUri !== '/login' && strpos($requestUri, '/preview/') !== 0) {
        header('Location: /cms-admin/login');
        exit();
    }
}

// Trasy chronione
$router->addRoute('GET', '/', 'HomeController@index');

// Artykuły
$router->addRoute('GET', '/articles', 'ArticleController@index');
$router->addRoute('GET', '/articles/new', 'ArticleController@create');
$router->addRoute('POST', '/articles/store', 'ArticleController@store');
$router->addRoute('GET', '/articles/{id}', 'ArticleController@edit');
$router->addRoute('POST', '/articles/update/{id}', 'ArticleController@update');
$router->addRoute('GET', '/articles/delete/{id}', 'ArticleController@delete');
$router->addRoute('POST', '/articles/files/delete/{fileId}', 'ArticleController@deleteFile');
$router->addRoute('POST', '/articles/files/reorder', 'ArticleController@updateFilePositions');

// Kategorie
$router->addRoute('GET', '/categories', 'CategoryController@index');
$router->addRoute('GET', '/categories/new', 'CategoryController@form');
$router->addRoute('POST', '/categories/store', 'CategoryController@store');
$router->addRoute('GET', '/categories/edit/{id}', 'CategoryController@form');
$router->addRoute('POST', '/categories/update/{id}', 'CategoryController@update');
$router->addRoute('GET', '/categories/delete/{id}', 'CategoryController@delete');

// Pozostałe trasy...
// (skopiuj wszystkie trasy z oryginalnego index.php)

// Obsłuż żądanie
$router->handleRequest($_SERVER['REQUEST_METHOD'], $_SERVER['REQUEST_URI']);