<?php

// Serve static files first (for built-in PHP server)
if (php_sapi_name() === 'cli-server') {
    $uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
    // Decode URL to handle spaces and special characters
    $uri = urldecode($uri);
    $file_path = __DIR__ . $uri;

    // If it's a file that exists, serve it directly
    if (is_file($file_path)) {
        // Set proper content type based on file extension
        $ext = pathinfo($file_path, PATHINFO_EXTENSION);
        $content_types = [
            'css' => 'text/css',
            'js' => 'application/javascript',
            'png' => 'image/png',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'gif' => 'image/gif',
            'svg' => 'image/svg+xml',
            'webp' => 'image/webp',
            'pdf' => 'application/pdf',
            'zip' => 'application/zip',
            'doc' => 'application/msword',
            'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'txt' => 'text/plain'
        ];

        if (isset($content_types[$ext])) {
            header('Content-Type: ' . $content_types[$ext]);
        }

        readfile($file_path);
        exit;
    }
}

// Załaduj główną konfigurację CMS
require_once dirname(dirname(__DIR__)) . '/cms-config.php';

// Użyj ścieżek z cms-config.php
require_once CMS_ADMIN_PATH . '/app/config.php';
require_once CMS_ADMIN_PATH . '/app/Router.php';
require_once CMS_ADMIN_PATH . '/app/Controller.php';
require_once CMS_ADMIN_PATH . '/app/View.php';
require_once CMS_ADMIN_PATH . '/app/Database.php';

// Start session
session_start();

// Initialize Database connection
$db = (new Database())->getConnection();

$router = new Router($db);

// Define public routes (no authentication required for these)
$router->addRoute('GET', '/login', 'AuthController@loginForm');
$router->addRoute('POST', '/login', 'AuthController@login');
// $router->addRoute('GET', '/preview/{slug}', 'StaticPageController@preview'); // Preview route is also public

// Middleware: Check if user is logged in for protected routes
if (!isset($_SESSION['user_id'])) {
    // Redirect to login page if not authenticated
    // Allow login and preview routes to be accessed without login
    $requestUri = $_SERVER['REQUEST_URI'];
    if ($requestUri !== '/login' && strpos($requestUri, '/preview/') !== 0) {
        header('Location: /login');
        exit();
    }
}


// Define protected routes (authentication required)
$router->addRoute('GET', '/', 'HomeController@index');

// Article Routes
$router->addRoute('GET', '/articles', 'ArticleController@index');
$router->addRoute('GET', '/articles/new', 'ArticleController@create');
$router->addRoute('POST', '/articles/store', 'ArticleController@store');
$router->addRoute('GET', '/articles/{id}', 'ArticleController@edit');
$router->addRoute('POST', '/articles/update/{id}', 'ArticleController@update');
$router->addRoute('GET', '/articles/delete/{id}', 'ArticleController@delete'); // Using GET for simplicity, POST is better for delete
$router->addRoute('POST', '/articles/files/delete/{fileId}', 'ArticleController@deleteFile'); // Delete article file
$router->addRoute('POST', '/articles/files/reorder', 'ArticleController@updateFilePositions'); // Update file positions via drag & drop

// Category Routes
$router->addRoute('GET', '/categories', 'CategoryController@index');
$router->addRoute('GET', '/categories/new', 'CategoryController@form'); // Use 'form' for both new and edit
$router->addRoute('POST', '/categories/store', 'CategoryController@store');
$router->addRoute('GET', '/categories/edit/{id}', 'CategoryController@form'); // Use 'form' for both new and edit
$router->addRoute('POST', '/categories/update/{id}', 'CategoryController@update');
$router->addRoute('GET', '/categories/delete/{id}', 'CategoryController@delete'); // Using GET for simplicity, POST is better for delete

// Tag Routes
$router->addRoute('GET', '/tags', 'TagController@index');
$router->addRoute('GET', '/tags/new', 'TagController@form'); // Use 'form' for both new and edit
$router->addRoute('POST', '/tags/store', 'TagController@store');
$router->addRoute('GET', '/tags/edit/{id}', 'TagController@form'); // Use 'form' for both new and edit
$router->addRoute('POST', '/tags/update/{id}', 'TagController@update');
$router->addRoute('GET', '/tags/delete/{id}', 'TagController@delete'); // Using GET for simplicity, POST is better for delete

// Static Page Routes
$router->addRoute('GET', '/pages', 'StaticPageController@index');
$router->addRoute('GET', '/pages/new', 'StaticPageController@form'); // Use 'form' for both new and edit
$router->addRoute('POST', '/pages/store', 'StaticPageController@store');
$router->addRoute('GET', '/pages/edit/{id}', 'StaticPageController@form'); // Use 'form' for both new and edit
$router->addRoute('GET', '/pages/{id}', 'StaticPageController@form'); // Alternative route for editing
$router->addRoute('POST', '/pages/update/{id}', 'StaticPageController@update');
$router->addRoute('GET', '/pages/delete/{id}', 'StaticPageController@delete'); // Using GET for simplicity, POST is better for delete
$router->addRoute('GET', '/pages/toggle-published/{id}', 'StaticPageController@togglePublished');
$router->addRoute('POST', '/pages/files/delete/{fileId}', 'StaticPageController@deleteFile'); // Delete static page file
$router->addRoute('POST', '/pages/files/reorder', 'StaticPageController@updateFilePositions'); // Update file positions via drag & drop
$router->addRoute('GET', '/preview/{slug}', 'StaticPageController@preview');

// Gallery Routes
$router->addRoute('GET', '/gallery', 'GalleryController@index');
$router->addRoute('GET', '/gallery/category/{id}', 'GalleryController@category'); // View images in category
$router->addRoute('POST', '/gallery/categories/store', 'GalleryController@createCategory'); // Add category via modal
$router->addRoute('GET', '/gallery/categories/edit/{id}', 'GalleryController@editCategory'); // Edit category
$router->addRoute('POST', '/gallery/categories/update/{id}', 'GalleryController@updateCategory'); // Update category
$router->addRoute('GET', '/gallery/categories/delete/{id}', 'GalleryController@deleteCategory'); // Delete category
$router->addRoute('POST', '/gallery/category/{id}/upload', 'GalleryController@uploadImages'); // Handle image uploads to category
$router->addRoute('GET', '/gallery/delete/{id}', 'GalleryController@deleteImage'); // Delete single image (using GET for simplicity)
$router->addRoute('GET', '/gallery/delete-multiple', 'GalleryController@deleteMultipleImages'); // Delete multiple images (using GET for simplicity)

// Menu Routes
$router->addRoute('GET', '/menus', 'MenuController@index');
$router->addRoute('GET', '/menus/new', 'MenuController@form'); // Use 'form' for new menu
$router->addRoute('POST', '/menus/store', 'MenuController@store'); // Store new menu
$router->addRoute('GET', '/menus/edit/{id}', 'MenuController@form'); // Use 'form' for edit menu
$router->addRoute('POST', '/menus/update/{id}', 'MenuController@update'); // Update menu
$router->addRoute('GET', '/menus/delete/{id}', 'MenuController@delete'); // Delete menu (using GET for simplicity)

// Menu Item Routes
$router->addRoute('GET', '/menus/{menuId}/items/new', 'MenuController@itemForm'); // Use 'itemForm' for new item
$router->addRoute('POST', '/menus/{menuId}/items/store', 'MenuController@storeItem'); // Store new item
$router->addRoute('GET', '/menus/{menuId}/items/edit/{itemId}', 'MenuController@itemForm'); // Use 'itemForm' for edit item
$router->addRoute('POST', '/menus/{menuId}/items/update/{itemId}', 'MenuController@updateItem'); // Update item
$router->addRoute('GET', '/menus/{menuId}/items/delete/{itemId}', 'MenuController@deleteItem'); // Delete item (using GET for simplicity)
$router->addRoute('POST', '/menus/{menuId}/items/order', 'MenuController@updateItemOrder'); // Update item order

// Banner Routes
$router->addRoute('GET', '/banners', 'BannerController@index');
$router->addRoute('GET', '/banners/new', 'BannerController@form'); // Use 'form' for new banner
$router->addRoute('POST', '/banners/store', 'BannerController@store'); // Store new banner
$router->addRoute('GET', '/banners/edit/{id}', 'BannerController@form'); // Use 'form' for edit banner
$router->addRoute('POST', '/banners/update/{id}', 'BannerController@update'); // Update banner
$router->addRoute('GET', '/banners/delete/{id}', 'BannerController@delete'); // Delete banner (using GET for simplicity)
$router->addRoute('GET', '/banners/toggle-active/{id}', 'BannerController@toggleActive'); // Toggle active status

// User Routes
$router->addRoute('GET', '/users', 'UserController@index');
$router->addRoute('GET', '/users/new', 'UserController@form'); // Use 'form' for new user
$router->addRoute('POST', '/users/store', 'UserController@store'); // Store new user
$router->addRoute('GET', '/users/edit/{id}', 'UserController@form'); // Use 'form' for edit user
$router->addRoute('POST', '/users/update/{id}', 'UserController@update'); // Update user
$router->addRoute('GET', '/users/delete/{id}', 'UserController@delete'); // Delete user (using GET for simplicity)

// Handle the request
$router->handleRequest($_SERVER['REQUEST_METHOD'], $_SERVER['REQUEST_URI']);
