<?php
// Skrypt do uruchomienia serwera deweloperskiego dla frontendu

$host = '127.0.0.1';
$port = 8080;
$documentRoot = __DIR__ . '/frontend';

echo "=== Uruchamianie serwera deweloperskiego frontendu ===\n";
echo "Host: $host\n";
echo "Port: $port\n";
echo "Document Root: $documentRoot\n";
echo "URL: http://$host:$port\n";
echo "\nAby zatrzymać serwer, naciśnij Ctrl+C\n";
echo "================================================\n\n";

// Sprawdź czy katalog frontendu istnieje
if (!is_dir($documentRoot)) {
    echo "BŁĄD: Katalog frontendu nie istnieje: $documentRoot\n";
    exit(1);
}

// Sprawdź czy port jest wolny
$socket = @fsockopen($host, $port, $errno, $errstr, 1);
if ($socket) {
    fclose($socket);
    echo "BŁĄD: Port $port jest już zajęty\n";
    exit(1);
}

// Uruchom serwer
$command = "php -S $host:$port -t $documentRoot";
echo "Wykonuję: $command\n\n";

// Zmień katalog roboczy na frontend
chdir($documentRoot);

// Uruchom serwer
passthru($command);
