<?php
$translations = include __DIR__ . '/../../lang/pl.php';
$title = isset($user) ? 'Edytuj Użytkownika' : 'Dodaj Nowego Użytkownika';
$current_page = 'users';
ob_start();
?>

<div class="d-flex align-items-center mb-4">
    <a href="/users" class="btn btn-sm btn-outline-secondary me-2">
        <i class="bi bi-arrow-left"></i>
    </a>
    <h1 class="page-title mb-0"><?php echo $title; ?></h1>
</div>

<?php if (isset($flash)): ?>
    <div class="alert alert-<?php echo $flash['type']; ?> alert-dismissible fade show shadow-sm" role="alert">
        <i class="bi bi-info-circle me-2"></i> <?php echo htmlspecialchars($flash['message']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<div class="card shadow-sm">
    <div class="card-body">
        <form action="<?php echo isset($user) ? '/users/update/' . $user['id'] : '/users/store'; ?>" method="POST">
            <div class="mb-3">
                <label for="username" class="form-label">Nazwa użytkownika</label>
                <input type="text" class="form-control" id="username" name="username" 
                       value="<?php echo htmlspecialchars($user['username'] ?? ''); ?>" 
                       required <?php echo isset($user) ? 'disabled' : ''; ?>>
                <?php if (isset($user)): ?>
                    <input type="hidden" name="username" value="<?php echo htmlspecialchars($user['username']); ?>">
                    <div class="form-text">Nazwa użytkownika nie może być zmieniona po utworzeniu konta.</div>
                <?php endif; ?>
            </div>
            
            <div class="mb-3">
                <label for="email" class="form-label">Email</label>
                <input type="email" class="form-control" id="email" name="email" 
                       value="<?php echo htmlspecialchars($user['email'] ?? ''); ?>" required>
            </div>
            
            <div class="mb-3">
                <label for="password" class="form-label">
                    Hasło <?php echo isset($user) ? '(pozostaw puste, aby nie zmieniać)' : ''; ?>
                </label>
                <input type="password" class="form-control" id="password" name="password" 
                       <?php echo !isset($user) ? 'required' : ''; ?>>
                <?php if (!isset($user)): ?>
                    <div class="form-text">Hasło jest wymagane przy tworzeniu nowego użytkownika.</div>
                <?php endif; ?>
            </div>
            
            <div class="mb-3">
                <label for="role" class="form-label">Rola</label>
                <select class="form-select" id="role" name="role" required>
                    <option value="editor" <?php echo (isset($user['role']) && $user['role'] == 'editor') ? 'selected' : ''; ?>>
                        <?php echo $translations['users.roles.editor']; ?>
                    </option>
                    <option value="admin" <?php echo (isset($user['role']) && $user['role'] == 'admin') ? 'selected' : ''; ?>>
                        <?php echo $translations['users.roles.admin']; ?>
                    </option>
                </select>
            </div>

            <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-check-lg me-1"></i>
                    <?php echo isset($user) ? 'Zaktualizuj użytkownika' : 'Utwórz użytkownika'; ?>
                </button>
                <a href="/users" class="btn btn-secondary">
                    <i class="bi bi-x-lg me-1"></i>
                    Anuluj
                </a>
            </div>
        </form>
    </div>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../LayoutView.php';
?>