<?php
require_once __DIR__ . '/../lang/pl.php';
$lang = include __DIR__ . '/../lang/pl.php';
?>
<!DOCTYPE html>
<html lang="pl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CMS - <?php echo $title ?? $lang['admin_panel']; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --bs-primary: #5a67d8;
            --bs-primary-rgb: 90, 103, 216;
            --bs-secondary: #718096;
            --bs-dark: #1a202c;
            --sidebar-width: 280px;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8f9fa;
            color: #4a5568;
        }

        .btn-primary {
            background-color: var(--bs-primary);
            border-color: var(--bs-primary);
        }

        .btn-primary:hover {
            background-color: #4c51bf;
            border-color: #4c51bf;
        }

        .sidebar {
            width: var(--sidebar-width);
            position: fixed;
            height: 100vh;
            background-color: #fff;
            border-right: 1px solid rgba(0, 0, 0, 0.08);
            z-index: 1000;
            overflow-y: auto;
            transition: all 0.3s ease;
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.08);
        }

        .sidebar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--bs-primary);
        }

        .nav-pills .nav-link {
            padding: 0.75rem 1.5rem;
            border-radius: 0;
            color: #4a5568;
            position: relative;
            transition: all 0.2s ease;
            border-left: 3px solid transparent;
        }

        .nav-pills .nav-link:hover {
            background-color: rgba(90, 103, 216, 0.05);
            color: var(--bs-primary);
        }

        .nav-pills .nav-link.active {
            background-color: rgba(90, 103, 216, 0.1);
            color: var(--bs-primary);
            border-left: 3px solid var(--bs-primary);
        }

        .main-content {
            margin-left: var(--sidebar-width);
            padding: 2rem;
            min-height: 100vh;
            transition: all 0.3s ease;
        }

        .page-title {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 1.5rem;
        }

        .card {
            border: none;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border-radius: 0.5rem;
        }

        .table th {
            font-weight: 600;
            border-top: none;
            color: #4a5568;
            background-color: #f8fafc;
        }

        .table-striped tbody tr:nth-of-type(odd) {
            background-color: rgba(0, 0, 0, 0.02);
        }

        .btn {
            font-weight: 500;
            padding: 0.375rem 1rem;
            border-radius: 0.375rem;
        }

        .btn-sm {
            padding: 0.25rem 0.75rem;
            border-radius: 0.25rem;
        }

        .badge {
            font-weight: 500;
            padding: 0.35em 0.65em;
        }

        /* Mobile responsive styles */
        @media (max-width: 991.98px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .mobile-topbar {
                display: flex;
                background-color: #fff;
                padding: 1rem;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                position: sticky;
                top: 0;
                z-index: 999;
            }
        }

        @media (min-width: 992px) {
            .mobile-topbar {
                display: none;
            }
        }

        /* When sidebar is collapsed on mobile */
        .sidebar-collapsed .main-content {
            margin-left: 0;
        }
    </style>
    <?php if (isset($additional_head)): ?>
        <?php echo $additional_head; ?>
    <?php endif; ?>

    <?php if (isset($additional_css)): ?>
        <?php echo $additional_css; ?>
    <?php endif; ?>
</head>

<body>
    <?php if ($current_page !== 'login'): ?>
        <!-- Mobile Top Bar - Only visible on mobile -->
        <div class="mobile-topbar d-lg-none">
            <button class="btn btn-link" type="button" id="sidebarToggle">
                <i class="bi bi-list fs-4"></i>
            </button>
            <span class="ms-3 fw-bold fs-5">CMS</span>
        </div>

        <!-- Sidebar -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header d-flex justify-content-between align-items-center">
                <a href="/" class="sidebar-brand text-decoration-none">
                    <i class="bi bi-layout-text-sidebar-reverse me-2"></i>CMS
                </a>
                <button class="btn-close d-lg-none" id="closeSidebar"></button>
            </div>

            <div class="p-3">
                <ul class="nav nav-pills flex-column">
                    <li class="nav-item">
                        <a href="/" class="nav-link <?php echo ($current_page === 'dashboard') ? 'active' : ''; ?>">
                            <i class="bi bi-speedometer2 me-2"></i>
                            <?php echo $lang['dashboard']; ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="/articles" class="nav-link <?php echo ($current_page === 'articles') ? 'active' : ''; ?>">
                            <i class="bi bi-file-earmark-text me-2"></i>
                            <?php echo $lang['articles']; ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="/categories" class="nav-link <?php echo ($current_page === 'categories') ? 'active' : ''; ?>">
                            <i class="bi bi-folder me-2"></i>
                            <?php echo $lang['categories']; ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="/tags" class="nav-link <?php echo ($current_page === 'tags') ? 'active' : ''; ?>">
                            <i class="bi bi-tag me-2"></i>
                            <?php echo $lang['tags']; ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="/menus" class="nav-link <?php echo ($current_page === 'menus') ? 'active' : ''; ?>">
                            <i class="bi bi-list-ul me-2"></i>
                            <?php echo $lang['menus']; ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="/gallery" class="nav-link <?php echo ($current_page === 'gallery') ? 'active' : ''; ?>">
                            <i class="bi bi-images me-2"></i>
                            <?php echo $lang['gallery']; ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="/banners" class="nav-link <?php echo ($current_page === 'banners') ? 'active' : ''; ?>">
                            <i class="bi bi-image me-2"></i>
                            <?php echo $lang['banners']; ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="/pages" class="nav-link <?php echo ($current_page === 'pages') ? 'active' : ''; ?>">
                            <i class="bi bi-file-earmark me-2"></i>
                            <?php echo $lang['pages']; ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="/users" class="nav-link <?php echo ($current_page === 'users') ? 'active' : ''; ?>">
                            <i class="bi bi-people me-2"></i>
                            <?php echo $lang['users']; ?>
                        </a>
                    </li>
                </ul>

                <hr class="my-4">

                <div class="d-flex align-items-center">
                    <div class="dropdown">
                        <a href="#" class="d-flex align-items-center text-decoration-none dropdown-toggle" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-person-circle fs-5 me-2"></i>
                            <span class="fw-medium">Admin</span>
                        </a>
                        <ul class="dropdown-menu shadow" aria-labelledby="userDropdown">
                            <li><a class="dropdown-item" href="/logout"><i class="bi bi-box-arrow-right me-2"></i><?php echo $lang['logout']; ?></a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content" id="mainContent">
            <div class="container-fluid p-0">
                <?php echo $content; ?>
            </div>
        </div>
    <?php else: ?>
        <!-- Login Page -->
        <div class="d-flex align-items-center justify-content-center min-vh-100 bg-light">
            <div class="container">
                <?php echo $content; ?>
            </div>
        </div>
    <?php endif; ?>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.14.0/Sortable.min.js"></script>
    <script>
        // Sidebar toggle functionality for mobile
        document.addEventListener('DOMContentLoaded', function() {
            const sidebarToggle = document.getElementById('sidebarToggle');
            const closeSidebar = document.getElementById('closeSidebar');
            const sidebar = document.getElementById('sidebar');

            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('show');
                });
            }

            if (closeSidebar) {
                closeSidebar.addEventListener('click', function() {
                    sidebar.classList.remove('show');
                });
            }

            // Close sidebar when clicking outside on mobile
            document.addEventListener('click', function(event) {
                if (window.innerWidth < 992 &&
                    !sidebar.contains(event.target) &&
                    event.target !== sidebarToggle &&
                    sidebar.classList.contains('show')) {
                    sidebar.classList.remove('show');
                }
            });
        });
    </script>
    <?php if (isset($additional_scripts)): ?>
        <?php echo $additional_scripts; ?>
    <?php endif; ?>
</body>

</html>