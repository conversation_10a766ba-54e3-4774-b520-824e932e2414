<?php
$title = isset($staticPage) ? 'Ed<PERSON><PERSON>j Stronę Statyczną' : 'Do<PERSON>j Nową Stronę Statyczną';
$current_page = 'pages';

$additional_head = '
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/simplemde/latest/simplemde.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
';

$additional_css = '
    <style>
        .CodeMirror {
            height: 300px;
            border-radius: 0.375rem;
            border: 1px solid #dee2e6;
        }
        .CodeMirror.cm-s-paper {
            height: 150px;
        }
        .editor-toolbar {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem 0.375rem 0 0;
            border-bottom: none;
        }
        .tag-badge {
            padding: 0.5rem 0.75rem;
            margin: 0.25rem;
            font-weight: normal;
            display: inline-flex;
            align-items: center;
            transition: all 0.2s ease;
        }
        .tag-badge:hover {
            background-color: rgba(var(--bs-secondary-rgb), 0.9);
        }
        .btn-close-sm {
            font-size: 0.6rem;
            padding: 0.2rem;
        }
        .preview-card {
            transition: all 0.2s ease;
        }
        .preview-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 .5rem 1rem rgba(0,0,0,.15)!important;
        }
        .file-preview {
            border-radius: 0.375rem;
            border: 1px solid rgba(0, 0, 0, 0.1);
            padding: 10px;
            margin-bottom: 10px;
            background-color: #f8f9fa;
            transition: all 0.2s ease;
        }
        .file-preview:hover {
            background-color: #f0f0f0;
        }
        
        /* Drag & Drop Styles */
        .draggable-card {
            position: relative;
            transition: all 0.2s ease;
        }
        
        .drag-handle {
            position: absolute;
            top: 5px;
            right: 5px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 3px;
            padding: 2px 4px;
            cursor: grab;
            z-index: 10;
            font-size: 12px;
        }
        
        .drag-handle:active {
            cursor: grabbing;
        }
        
        .sortable-item.sortable-ghost {
            opacity: 0.4;
        }
        
        .sortable-item.sortable-chosen {
            transform: scale(1.05);
            box-shadow: 0 .5rem 1rem rgba(0,0,0,.25)!important;
        }
        
        .sortable-item.sortable-drag {
            opacity: 0.8;
        }
        
        #existing-images[data-sortable="true"] .sortable-item:hover .drag-handle {
            background: rgba(13, 110, 253, 0.1);
            color: #0d6efd;
        }
        
        /* Image preview hover effects */
        .image-preview-trigger {
            transition: all 0.2s ease;
        }
        
        .image-preview-trigger:hover {
            opacity: 0.8;
            transform: scale(1.02);
        }
        
        .draggable-card:hover .image-preview-trigger {
            filter: brightness(1.1);
        }
        
        /* Modal styles */
        #imagePreviewModal .modal-body {
            background: #f8f9fa;
        }
        
        #imagePreviewModal .modal-footer {
            background: white;
            border-top: 1px solid #dee2e6;
        }
    </style>
';

ob_start();
?>

<div class="d-flex align-items-center mb-4">
    <a href="/pages" class="btn btn-sm btn-outline-secondary me-2">
        <i class="bi bi-arrow-left"></i>
    </a>
    <h1 class="page-title mb-0"><?php echo $title; ?></h1>
</div>

<?php if (isset($flash)): ?>
    <div class="alert alert-<?php echo $flash['type']; ?> alert-dismissible fade show shadow-sm" role="alert">
        <i class="bi bi-info-circle me-2"></i> <?php echo htmlspecialchars($flash['message']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<form action="<?php echo isset($staticPage) ? '/pages/update/' . $staticPage['id'] : '/pages/store'; ?>" method="POST" enctype="multipart/form-data">
    <div class="row g-4">
        <!-- Main Content Column -->
        <div class="col-lg-8">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white py-3">
                    <h5 class="card-title mb-0 fw-bold">
                        <i class="bi bi-pencil-square me-2"></i>Podstawowe informacje
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3 mb-4">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="title" class="form-label fw-medium mb-2">Tytuł</label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light">
                                        <i class="bi bi-type-h1 text-primary"></i>
                                    </span>
                                    <input type="text" class="form-control" id="title" name="title"
                                        value="<?php echo htmlspecialchars($staticPage['title'] ?? ''); ?>"
                                        oninput="generateSlug(this.value)" required>
                                </div>
                                <small class="text-muted">Wprowadź tytuł strony, który będzie widoczny dla użytkowników</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="slug" class="form-label fw-medium mb-2">Slug</label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light">
                                        <i class="bi bi-link-45deg text-secondary"></i>
                                    </span>
                                    <input type="text" class="form-control font-monospace" id="slug" name="slug"
                                        value="<?php echo htmlspecialchars($staticPage['slug'] ?? ''); ?>" required>
                                </div>
                                <small class="text-muted">Unikalny identyfikator używany w adresach URL</small>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="content" class="form-label fw-medium mb-2">Treść strony</label>
                        <textarea class="form-control" id="content" name="content" rows="10"><?php echo htmlspecialchars($staticPage['content'] ?? ''); ?></textarea>
                        <small class="text-muted mt-2">Użyj Markdown do formatowania tekstu</small>
                    </div>
                </div>
            </div>

            <!-- Files and Images -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white py-3">
                    <h5 class="card-title mb-0 fw-bold">
                        <i class="bi bi-file-earmark-image me-2"></i>Media
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Images Upload Section -->
                    <div class="mb-5">
                        <div class="d-flex align-items-center mb-3">
                            <i class="bi bi-image text-primary fs-5 me-2"></i>
                            <h6 class="form-label fw-medium mb-0">Zdjęcia</h6>
                        </div>
                        
                        <!-- Existing Images -->
                        <?php if (isset($existingImages) && !empty($existingImages)): ?>
                            <div class="mb-4">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6 class="text-muted mb-0">Istniejące zdjęcia:</h6>
                                    <div class="d-flex flex-column text-end">
                                        <small class="text-muted">
                                            <i class="bi bi-grip-vertical me-1"></i>Przeciągnij aby zmienić kolejność
                                        </small>
                                        <small class="text-muted">
                                            <i class="bi bi-eye me-1"></i>Kliknij zdjęcie aby powiększyć
                                        </small>
                                    </div>
                                </div>
                                <div class="row g-3" id="existing-images" data-sortable="true">
                                    <?php foreach ($existingImages as $image): ?>
                                        <div class="col-6 col-md-4 col-lg-3 col-xl-2 sortable-item" data-file-id="<?php echo $image['id']; ?>" data-position="<?php echo $image['position']; ?>">
                                            <div class="card preview-card shadow-sm h-100 draggable-card">
                                                <div class="drag-handle">
                                                    <i class="bi bi-grip-vertical text-muted"></i>
                                                </div>
                                                <img src="<?php echo htmlspecialchars($image['filepath']); ?>" 
                                                     class="card-img-top image-preview-trigger" style="height: 120px; object-fit: cover; cursor: pointer;"
                                                     alt="<?php echo htmlspecialchars($image['original_filename']); ?>"
                                                     data-bs-toggle="tooltip" 
                                                     data-bs-placement="top"
                                                     data-bs-title="Kliknij aby powiększyć"
                                                     data-image-src="<?php echo htmlspecialchars($image['filepath']); ?>"
                                                     data-image-title="<?php echo htmlspecialchars($image['original_filename']); ?>"
                                                     data-image-size="<?php echo number_format($image['file_size'] / 1024, 1); ?> KB"
                                                     onclick="openImageModal(this)">
                                                <div class="card-body p-2">
                                                    <p class="card-text small text-truncate mb-1"><?php echo htmlspecialchars($image['original_filename']); ?></p>
                                                    <p class="card-text small text-muted"><?php echo number_format($image['file_size'] / 1024, 1); ?> KB</p>
                                                </div>
                                                <div class="card-footer p-2 text-end">
                                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                                            onclick="deleteExistingFile(<?php echo $image['id']; ?>, this)">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="d-grid mb-3">
                                    <button type="button" class="btn btn-outline-primary" onclick="document.getElementById('image-upload').click()">
                                        <i class="bi bi-image me-2"></i>Dodaj nowe zdjęcia
                                    </button>
                                    <input id="image-upload" type="file" name="images[]" accept="image/*" multiple class="d-none"
                                        onchange="previewImages(this.files, 'images-preview')">
                                </div>
                            </div>
                        </div>
                        <div id="images-preview" class="row g-3"></div>
                    </div>

                    <!-- Files Upload Section -->
                    <div class="border-top pt-4">
                        <div class="d-flex align-items-center mb-3">
                            <i class="bi bi-paperclip text-secondary fs-5 me-2"></i>
                            <h6 class="form-label fw-medium mb-0">Pliki</h6>
                        </div>
                        
                        <!-- Existing Files -->
                        <?php if (isset($existingFiles) && !empty($existingFiles)): ?>
                            <div class="mb-4">
                                <h6 class="text-muted mb-2">Istniejące pliki:</h6>
                                <div id="existing-files">
                                    <?php foreach ($existingFiles as $file): ?>
                                        <div class="file-preview d-flex justify-content-between align-items-center mb-2 p-3 border rounded bg-light" data-file-id="<?php echo $file['id']; ?>">
                                            <div class="d-flex align-items-center">
                                                <?php
                                                $fileExt = strtolower(pathinfo($file['original_filename'], PATHINFO_EXTENSION));
                                                $fileIcon = 'bi-file-earmark';
                                                if (in_array($fileExt, ['pdf'])) $fileIcon = 'bi-file-earmark-pdf';
                                                elseif (in_array($fileExt, ['doc', 'docx'])) $fileIcon = 'bi-file-earmark-word';
                                                elseif (in_array($fileExt, ['xls', 'xlsx'])) $fileIcon = 'bi-file-earmark-excel';
                                                elseif (in_array($fileExt, ['ppt', 'pptx'])) $fileIcon = 'bi-file-earmark-ppt';
                                                elseif (in_array($fileExt, ['zip', 'rar', '7z'])) $fileIcon = 'bi-file-earmark-zip';
                                                elseif (in_array($fileExt, ['txt'])) $fileIcon = 'bi-file-earmark-text';
                                                ?>
                                                <i class="bi <?php echo $fileIcon; ?> fs-4 me-3 text-secondary"></i>
                                                <div>
                                                    <div class="fw-medium">
                                                        <a href="<?php echo htmlspecialchars($file['filepath']); ?>" target="_blank" class="text-decoration-none">
                                                            <?php echo htmlspecialchars($file['original_filename']); ?>
                                                        </a>
                                                    </div>
                                                    <small class="text-muted"><?php echo number_format($file['file_size'] / 1024, 1); ?> KB</small>
                                                </div>
                                            </div>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteExistingFile(<?php echo $file['id']; ?>, this)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="d-grid mb-3">
                                    <button type="button" class="btn btn-outline-secondary" onclick="document.getElementById('file-upload').click()">
                                        <i class="bi bi-paperclip me-2"></i>Dodaj nowe pliki
                                    </button>
                                    <input id="file-upload" type="file" name="files[]" multiple class="d-none"
                                        onchange="previewFiles(this.files, 'files-preview')">
                                </div>
                            </div>
                        </div>
                        <div id="files-preview" class="files-container"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar Options Column -->
        <div class="col-lg-4">
            <!-- Save Button -->
            <div class="d-grid gap-2">
                <button type="submit" class="btn btn-primary py-2">
                    <i class="bi bi-save me-2"></i>Zapisz Stronę
                </button>
                <a href="/pages" class="btn btn-outline-secondary">
                    <i class="bi bi-x-circle me-2"></i>Anuluj
                </a>
            </div>

            <!-- Publish Card -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white py-3">
                    <h5 class="card-title mb-0 fw-bold">
                        <i class="bi bi-send me-2"></i>Publikacja
                    </h5>
                </div>
                <div class="card-body">
                    <div class="form-check form-switch mb-3">
                        <input type="checkbox" class="form-check-input" id="published" name="published" value="1"
                            <?php echo (isset($staticPage['published']) && $staticPage['published']) ? 'checked' : ''; ?>>
                        <label class="form-check-label" for="published">Opublikowana</label>
                    </div>
                    <div class="alert alert-info" role="alert">
                        <div class="d-flex">
                            <i class="bi bi-info-circle me-2 fs-5"></i>
                            <div>
                                <strong>Status:</strong>
                                <?php if (isset($staticPage['published']) && $staticPage['published']): ?>
                                    <span class="text-success">Opublikowana</span>
                                <?php else: ?>
                                    <span class="text-secondary">Szkic</span>
                                <?php endif; ?>
                                <?php if (isset($staticPage['created_at'])): ?>
                                    <div class="mt-2 small">Utworzona: <?php echo htmlspecialchars($staticPage['created_at']); ?></div>
                                <?php endif; ?>
                                <?php if (isset($staticPage['updated_at'])): ?>
                                    <div class="small">Zaktualizowana: <?php echo htmlspecialchars($staticPage['updated_at']); ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

<!-- Image Preview Modal -->
<div class="modal fade" id="imagePreviewModal" tabindex="-1" aria-labelledby="imagePreviewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imagePreviewModalLabel">Podgląd zdjęcia</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center p-0">
                <img id="modalPreviewImage" src="" alt="" class="img-fluid" style="max-height: 70vh; width: auto;">
            </div>
            <div class="modal-footer">
                <div class="d-flex justify-content-between align-items-center w-100">
                    <div>
                        <strong id="modalImageTitle">Nazwa pliku</strong><br>
                        <small class="text-muted" id="modalImageSize">Rozmiar</small>
                    </div>
                    <div>
                        <a id="modalDownloadLink" href="" class="btn btn-outline-primary btn-sm me-2" download>
                            <i class="bi bi-download me-1"></i>Pobierz
                        </a>
                        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Zamknij</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();

$additional_scripts = <<<JS
    <script src="https://cdn.jsdelivr.net/simplemde/latest/simplemde.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            try {
                console.log("Initializing WYSIWYG editors...");
                
                // Get the article ID from the form action URL
                let articleId = 'new';
                const formAction = document.querySelector('form').action;
                if (formAction) {
                    const parts = formAction.split('/');
                    if (parts.length > 0) {
                        const lastPart = parts[parts.length - 1];
                        if (lastPart && !isNaN(parseInt(lastPart))) {
                            articleId = lastPart;
                        }
                    }
                }
                
                // Initialize Markdown Editor for main content
                var contentEditor = new SimpleMDE({
                    element: document.getElementById("content"),
                    spellChecker: false,
                    autosave: {
                        enabled: true,
                        uniqueId: "staticpage-content-" + articleId,
                        delay: 1000,
                    },
                    toolbar: [
                        "bold", "italic", "heading", "|",
                        "quote", "unordered-list", "ordered-list", "|",
                        "link", "image", "table", "|",
                        "preview", "side-by-side", "fullscreen", "|",
                        "guide"
                    ]
                });
                
                console.log("WYSIWYG editor initialized successfully");
                
                // Initialize Drag & Drop for existing images
                initializeDragAndDrop();
                
            } catch (error) {
                console.error("Error initializing editors:", error);
            }
        });

        function initializeDragAndDrop() {
            const existingImages = document.getElementById('existing-images');
            if (existingImages && existingImages.dataset.sortable === 'true') {
                const sortable = Sortable.create(existingImages, {
                    handle: '.drag-handle',
                    animation: 150,
                    ghostClass: 'sortable-ghost',
                    chosenClass: 'sortable-chosen',
                    dragClass: 'sortable-drag',
                    onEnd: function(evt) {
                        // Collect new order
                        const positions = {};
                        const items = existingImages.querySelectorAll('.sortable-item');
                        
                        items.forEach((item, index) => {
                            const fileId = item.dataset.fileId;
                            positions[fileId] = index + 1;
                        });
                        
                        // Send AJAX request to update positions
                        updateFilePositions(positions);
                    }
                });
                
                console.log("Drag & Drop initialized for images");
            }
        }
        
        function updateFilePositions(positions) {
            fetch('/pages/files/reorder', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    positions: positions
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('Pozycje plików zaktualizowane pomyślnie');
                    
                    // Show temporary success indicator
                    showPositionUpdateNotification(true);
                } else {
                    console.error('Błąd aktualizacji pozycji:', data.message);
                    showPositionUpdateNotification(false);
                }
            })
            .catch(error => {
                console.error('Błąd sieci:', error);
                showPositionUpdateNotification(false);
            });
        }
        
        function showPositionUpdateNotification(success) {
            const existingNotification = document.querySelector('.position-update-notification');
            if (existingNotification) {
                existingNotification.remove();
            }
            
            const notification = document.createElement('div');
            notification.className = 'position-update-notification alert alert-' + (success ? 'success' : 'danger') + ' alert-dismissible fade show';
            notification.style.position = 'fixed';
            notification.style.top = '20px';
            notification.style.right = '20px';
            notification.style.zIndex = '9999';
            notification.style.minWidth = '300px';
            
            notification.innerHTML = '<i class="bi bi-' + (success ? 'check-circle' : 'exclamation-triangle') + ' me-2"></i>' +
                (success ? 'Kolejność zdjęć została zaktualizowana' : 'Błąd podczas aktualizacji kolejności') +
                '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
            
            document.body.appendChild(notification);
            
            // Auto-hide after 3 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 3000);
        }

        // Generate slug from title
        function generateSlug(title) {
            const slugField = document.getElementById('slug');
            
            // Only generate if the slug field hasn't been manually edited
            if (!slugField.dataset.userEdited) {
                const slug = title
                    .trim()
                    .toLowerCase()
                    .replace(/[^a-z0-9\s-]/g, '')  // Remove special chars except spaces and hyphens
                    .replace(/\s+/g, '-')          // Replace spaces with hyphens
                    .replace(/-+/g, '-')           // Replace multiple hyphens with a single one
                    .replace(/^-+|-+$/g, '');      // Remove hyphens from start and end
                
                slugField.value = slug;
            }
        }

        // Mark slug as manually edited
        document.getElementById('slug').addEventListener('input', function() {
            this.dataset.userEdited = 'true';
        });

        // Make generateSlug function globally available
        window.generateSlug = generateSlug;

        // Handle tag input
        function handleTagInput(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                const input = e.target;
                const tagName = input.value.trim();

                if (tagName) {
                    // In a real app, you'd send an AJAX request to check if the tag exists
                    // For now, we'll simulate adding a new tag
                    const tagsContainer = document.getElementById('tags-container');
                    const randomId = Math.floor(Math.random() * 1000);

                    const tagElement = document.createElement('span');
                    tagElement.className = 'badge bg-secondary tag-badge';
                    tagElement.innerHTML = tagName + 
                        '<input type="hidden" name="tags[]" value="' + randomId + '">' +
                        '<button type="button" class="btn-close btn-close-white ms-2 btn-close-sm" ' +
                        'onclick="this.parentElement.remove()"></button>';

                    tagsContainer.appendChild(tagElement);
                    input.value = '';
                }
            }
        }

        // Preview files
        function previewFiles(files, previewContainerId) {
            const previewContainer = document.getElementById(previewContainerId);
            previewContainer.innerHTML = '';

            Array.from(files).forEach(file => {
                const filePreview = document.createElement('div');
                filePreview.className = 'file-preview d-flex justify-content-between align-items-center mb-2';
                
                // Determine appropriate icon based on file type
                let fileIcon = 'bi-file-earmark';
                const fileExt = file.name.split('.').pop().toLowerCase();
                
                if (['pdf'].includes(fileExt)) {
                    fileIcon = 'bi-file-earmark-pdf';
                } else if (['doc', 'docx'].includes(fileExt)) {
                    fileIcon = 'bi-file-earmark-word';
                } else if (['xls', 'xlsx'].includes(fileExt)) {
                    fileIcon = 'bi-file-earmark-excel';
                } else if (['ppt', 'pptx'].includes(fileExt)) {
                    fileIcon = 'bi-file-earmark-ppt';
                } else if (['zip', 'rar', '7z'].includes(fileExt)) {
                    fileIcon = 'bi-file-earmark-zip';
                } else if (['txt'].includes(fileExt)) {
                    fileIcon = 'bi-file-earmark-text';
                }
                
                const fileContent = '<div class="d-flex align-items-center">' +
                    '<i class="bi ' + fileIcon + ' fs-4 me-2 text-secondary"></i>' +
                    '<div>' +
                    '<div class="fw-medium">' + file.name + '</div>' +
                    '<small class="text-muted">' + (file.size / 1024).toFixed(1) + ' KB</small>' +
                    '</div>' +
                    '</div>' +
                    '<button type="button" class="btn btn-sm btn-outline-danger" ' +
                    'onclick="this.closest(\'.file-preview\').remove()">' +
                    '<i class="bi bi-trash"></i>' +
                    '</button>';
                
                filePreview.innerHTML = fileContent;
                previewContainer.appendChild(filePreview);
            });
        }

        // Preview images
        function previewImages(files, previewContainerId) {
            const previewContainer = document.getElementById(previewContainerId);
            
            Array.from(files).forEach(file => {
                const col = document.createElement('div');
                col.className = 'col-6 col-md-4 col-lg-6 col-xl-4';

                const imgPreview = document.createElement('div');
                imgPreview.className = 'card preview-card shadow-sm h-100';

                const img = document.createElement('img');
                img.src = URL.createObjectURL(file);
                img.className = 'card-img-top image-preview-trigger';
                img.style.height = '120px';
                img.style.objectFit = 'cover';
                img.style.cursor = 'pointer';
                img.setAttribute('data-bs-toggle', 'tooltip');
                img.setAttribute('data-bs-placement', 'top');
                img.setAttribute('data-bs-title', 'Kliknij aby powiększyć');
                img.setAttribute('data-image-src', img.src);
                img.setAttribute('data-image-title', file.name);
                img.setAttribute('data-image-size', (file.size / 1024).toFixed(1) + ' KB');
                img.onclick = function() { openImageModal(this); };
                
                const cardBody = document.createElement('div');
                cardBody.className = 'card-body p-2';
                
                const cardContent = '<p class="card-text small text-truncate mb-1">' + file.name + '</p>' +
                    '<p class="card-text small text-muted">' + (file.size / 1024).toFixed(1) + ' KB</p>';
                
                cardBody.innerHTML = cardContent;
                
                const cardFooter = document.createElement('div');
                cardFooter.className = 'card-footer p-2 text-end';
                
                const removeBtn = document.createElement('button');
                removeBtn.type = 'button';
                removeBtn.className = 'btn btn-sm btn-outline-danger';
                removeBtn.innerHTML = '<i class="bi bi-trash"></i>';
                removeBtn.onclick = function() {
                    URL.revokeObjectURL(img.src);
                    col.remove();
                };
                
                cardFooter.appendChild(removeBtn);
                imgPreview.appendChild(img);
                imgPreview.appendChild(cardBody);
                imgPreview.appendChild(cardFooter);
                col.appendChild(imgPreview);
                previewContainer.appendChild(col);
            });
            
            // Reinitialize tooltips for new images
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }

        // Delete existing file
        function deleteExistingFile(fileId, button) {
            if (!confirm('Czy na pewno chcesz usunąć ten plik?')) {
                return;
            }

            // Disable button during request
            button.disabled = true;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="bi bi-hourglass-split"></i>';

            fetch('/pages/files/delete/' + fileId, {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Remove the file element from DOM
                    const fileElement = button.closest('[data-file-id="' + fileId + '"]');
                    if (fileElement) {
                        fileElement.remove();
                    }
                    
                    // Show success message (you can customize this)
                    console.log('Plik usunięty pomyślnie');
                } else {
                    alert('Błąd podczas usuwania pliku: ' + data.message);
                    button.disabled = false;
                    button.innerHTML = originalText;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Wystąpił błąd podczas usuwania pliku');
                button.disabled = false;
                button.innerHTML = originalText;
            });
        }

        // Image preview modal functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Bootstrap tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
            
            const imagePreviewModal = document.getElementById('imagePreviewModal');
            if (imagePreviewModal) {
                imagePreviewModal.addEventListener('show.bs.modal', function (event) {
                    const button = event.relatedTarget; // Button that triggered the modal
                    const imageSrc = button.getAttribute('data-image-src');
                    const imageTitle = button.getAttribute('data-image-title');
                    const imageSize = button.getAttribute('data-image-size');
                    
                    // Update modal content
                    const modalImage = document.getElementById('modalPreviewImage');
                    const modalTitle = document.getElementById('modalImageTitle');
                    const modalSize = document.getElementById('modalImageSize');
                    const modalDownloadLink = document.getElementById('modalDownloadLink');
                    
                    modalImage.src = imageSrc;
                    modalImage.alt = imageTitle;
                    modalTitle.textContent = imageTitle;
                    modalSize.textContent = imageSize;
                    modalDownloadLink.href = imageSrc;
                    modalDownloadLink.download = imageTitle;
                });
            }
        });
        
        function openImageModal(imageElement) {
            const imageSrc = imageElement.getAttribute('data-image-src');
            const imageTitle = imageElement.getAttribute('data-image-title');
            const imageSize = imageElement.getAttribute('data-image-size');
            
            // Update modal content
            const modalImage = document.getElementById('modalPreviewImage');
            const modalTitle = document.getElementById('modalImageTitle');
            const modalSize = document.getElementById('modalImageSize');
            const modalDownloadLink = document.getElementById('modalDownloadLink');
            
            modalImage.src = imageSrc;
            modalImage.alt = imageTitle;
            modalTitle.textContent = imageTitle;
            modalSize.textContent = imageSize;
            modalDownloadLink.href = imageSrc;
            modalDownloadLink.download = imageTitle;
            
            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('imagePreviewModal'));
            modal.show();
        }
    </script>
JS;

require_once __DIR__ . '/../LayoutView.php';
