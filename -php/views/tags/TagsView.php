<?php
$translations = include __DIR__ . '/../../lang/pl.php';

$title = $translations['tags.title'] ?? 'Tagi';
$current_page = 'tags';

ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="page-title"><?php echo $translations['tags.title']; ?></h1>
    <a href="/tags/new" class="btn btn-primary">
        <i class="bi bi-tag-plus me-1"></i> <?php echo $translations['tags.new_button']; ?>
    </a>
</div>

<?php if (isset($flash)): ?>
    <div class="alert alert-<?php echo $flash['type']; ?> alert-dismissible fade show shadow-sm" role="alert">
        <i class="bi bi-info-circle me-2"></i> <?php echo htmlspecialchars($flash['message']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<div class="card shadow-sm">
    <div class="table-responsive">
        <table class="table table-striped table-hover mb-0">
            <thead>
                <tr>
                    <th scope="col"><?php echo $translations['tags.table.name']; ?></th>
                    <th scope="col"><?php echo $translations['tags.table.slug']; ?></th>
                    <th scope="col"><?php echo $translations['tags.table.articles_count']; ?></th>
                    <th scope="col" class="text-end"><?php echo $translations['tags.table.actions']; ?></th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($tags)): ?>
                    <tr>
                        <td colspan="4" class="text-center py-4">
                            <div class="text-muted">
                                <i class="bi bi-tags fs-3 d-block mb-2"></i>
                                <?php echo $translations['tags.table.no_results']; ?>
                            </div>
                        </td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($tags as $tag): ?>
                        <tr>
                            <td class="align-middle fw-medium">
                                <i class="bi bi-tag-fill me-2 text-primary"></i>
                                <?php echo htmlspecialchars($tag['name']); ?>
                            </td>
                            <td class="align-middle">
                                <code><?php echo htmlspecialchars($tag['slug']); ?></code>
                            </td>
                            <td class="align-middle">
                                <span class="badge bg-secondary rounded-pill">
                                    0 <i class="bi bi-file-earmark-text ms-1"></i>
                                </span>
                            </td>
                            <td class="align-middle text-end">
                                <div class="btn-group">
                                    <a href="/tags/edit/<?php echo $tag['id']; ?>" class="btn btn-sm btn-primary">
                                        <i class="bi bi-pencil-square"></i>
                                    </a>
                                    <a href="/tags/delete/<?php echo $tag['id']; ?>"
                                        class="btn btn-sm btn-danger"
                                        onclick="return confirm('<?php echo $translations['tags.delete_confirm']; ?>');">
                                        <i class="bi bi-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../views/LayoutView.php';
?>