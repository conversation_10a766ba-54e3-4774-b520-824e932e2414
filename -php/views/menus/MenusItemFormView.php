<!DOCTYPE html>
<html lang="pl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CMS - <?php echo isset($menuItem) ? 'Edytuj' : 'Dodaj'; ?> Element Menu</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>

<body>
    <div class="container mt-4">
        <h1><?php echo isset($menuItem) ? 'Edytuj Element Menu' : 'Dodaj Nowy Element Menu'; ?></h1>
        <h5>dla menu: <?php echo htmlspecialchars($menu['name']); ?></h5>

        <form action="<?php echo isset($menuItem) ? '/menus/' . $menu['id'] . '/items/update/' . $menuItem['id'] : '/menus/' . $menu['id'] . '/items/store'; ?>" method="POST">
            <div class="mb-3">
                <label for="name" class="form-label">Nazwa elementu</label>
                <input type="text" class="form-control" id="name" name="name" value="<?php echo htmlspecialchars($menuItem['title'] ?? ''); ?>" required>
            </div>
            <div class="mb-3">
                <label for="url" class="form-label">URL</label>
                <input type="text" class="form-control" id="url" name="url" value="<?php echo htmlspecialchars($menuItem['url'] ?? ''); ?>" required>
                <div id="urlHelp" class="form-text">Np. /strona-statyczna lub https://zewnetrzny-link.pl.</div>
            </div>

            <button type="submit" class="btn btn-primary">Zapisz element</button>
            <a href="/menus?menu_id=<?php echo $menu['id']; ?>" class="btn btn-secondary">Anuluj</a>
        </form>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>

</html>