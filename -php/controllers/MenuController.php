<?php

require_once BASE_PATH . '/app/Controller.php';
require_once BASE_PATH . '/models/Menu.php';

class MenuController extends Controller {
    private $menuModel;

    public function __construct($dbConnection) {
        parent::__construct($dbConnection);
        $this->menuModel = new Menu($dbConnection);

        // Check if user is logged in for all actions in this controller
        // session_start(); // Session is already started in index.php for protected routes
        if (!isset($_SESSION['user_id'])) {
            // Redirect to login page if not authenticated
            header('Location: /login');
            exit();
        }
    }

    public function index() {
        $menus = $this->menuModel->getAllMenus();
        $itemCounts = $this->menuModel->getItemCountByMenuId();

        // Add item count to each menu
        foreach ($menus as &$menu) {
            $menu['itemCount'] = $itemCounts[$menu['id']] ?? 0;
        }
        unset($menu); // Unset the reference

        // Check if a specific menu is selected for viewing items
        $selectedMenuId = $_GET['menu_id'] ?? null;
        $menuItems = [];
        $selectedMenu = null;

        if ($selectedMenuId) {
            $selectedMenu = $this->menuModel->getMenuById($selectedMenuId);
            if ($selectedMenu) {
                $menuItems = $this->menuModel->getMenuItemsByMenuId($selectedMenuId);
            } else {
                // Handle case where selected menu doesn't exist
                $selectedMenuId = null;
            }
        }


        $this->renderView('menus/MenusView', [
            'menus' => $menus,
            'selectedMenuId' => $selectedMenuId,
            'selectedMenu' => $selectedMenu,
            'menuItems' => $menuItems
        ]);
    }

    // Handles both display of menu form (GET) and processing (POST for update - store handled separately)
    public function form($id = null) {
        $menu = null;
        if ($id) {
            $menu = $this->menuModel->getMenuById($id);
            if (!$menu) {
                http_response_code(404);
                echo "Menu not found.";
                return;
            }
        }

        $this->renderView('menus/MenusFormView', [
            'menu' => $menu
        ]);
    }

    public function store() {
        $name = filter_input(INPUT_POST, 'name', FILTER_SANITIZE_STRING);
        $slug = filter_input(INPUT_POST, 'slug', FILTER_SANITIZE_STRING);

        // Basic validation
        if (empty($name) || empty($slug)) {
            $this->setFlashMessage('Nazwa i slug są wymagane', 'error');
            header('Location: /menus/new');
            exit();
        }

        if ($this->menuModel->createMenu($name, $slug)) {
            $this->setFlashMessage('Menu utworzone pomyślnie!', 'success');
            header('Location: /menus');
            exit();
        } else {
            $this->setFlashMessage('Błąd podczas tworzenia menu. Nazwa może nie być unikalna.', 'error');
            header('Location: /menus/new');
            exit();
        }
    }

    public function update($id) {
        $name = filter_input(INPUT_POST, 'name', FILTER_SANITIZE_STRING);
        $slug = filter_input(INPUT_POST, 'slug', FILTER_SANITIZE_STRING);

        // Basic validation
        if (empty($name) || empty($slug)) {
            $this->setFlashMessage('Nazwa i slug są wymagane', 'error');
            header('Location: /menus/edit/' . $id);
            exit();
        }

        // Check if slug is unique, excluding the current menu
        $existingMenu = $this->menuModel->getMenuBySlug($slug);
        if ($existingMenu && $existingMenu['id'] != $id) {
            $this->setFlashMessage('Slug musi być unikalny', 'error');
            header('Location: /menus/edit/' . $id);
            exit();
        }

        if ($this->menuModel->updateMenu($id, $name, $slug)) {
            $this->setFlashMessage('Menu zaktualizowane pomyślnie!', 'success');
            header('Location: /menus');
            exit();
        } else {
            $this->setFlashMessage('Błąd podczas aktualizacji menu', 'error');
            header('Location: /menus/edit/' . $id);
            exit();
        }
    }

    public function delete($id) {
        if ($this->menuModel->deleteMenu($id)) {
            $this->setFlashMessage('Menu usunięte pomyślnie!', 'success');
            header('Location: /menus');
            exit();
        } else {
            $this->setFlashMessage('Błąd podczas usuwania menu', 'error');
            header('Location: /menus');
            exit();
        }
    }

    // Handles both display of menu item form (GET) and processing (POST for update - store handled separately)
    public function itemForm($menuId, $itemId = null) {
        $menu = $this->menuModel->getMenuById($menuId);
        if (!$menu) {
            http_response_code(404);
            echo "Menu not found.";
            return;
        }

        $menuItem = null;
        if ($itemId) {
            $menuItem = $this->menuModel->getMenuItemById($itemId);
            if (!$menuItem || $menuItem['menu_id'] != $menuId) {
                http_response_code(404);
                echo "Menu item not found in this menu.";
                return;
            }
        }

        $this->renderView('menus/MenusItemFormView', [
            'menu' => $menu,
            'menuItem' => $menuItem
        ]);
    }

    public function storeItem($menuId) {
        $menu = $this->menuModel->getMenuById($menuId);
        if (!$menu) {
            http_response_code(404);
            echo "Menu not found.";
            return;
        }

        $name = filter_input(INPUT_POST, 'name', FILTER_SANITIZE_STRING);
        $url = filter_input(INPUT_POST, 'url', FILTER_SANITIZE_STRING);

        // Basic validation
        if (empty($name) || empty($url)) {
            $this->setFlashMessage('Nazwa i URL są wymagane dla elementu menu', 'error');
            header('Location: /menus/' . $menuId . '/items/new');
            exit();
        }

        // Determine the position - simple approach: last item's position + 1
        $items = $this->menuModel->getMenuItemsByMenuId($menuId);
        $position = count($items) + 1;

        if ($this->menuModel->createMenuItem($menuId, $name, $url, $position)) {
            $this->setFlashMessage('Element menu utworzony pomyślnie!', 'success');
            header('Location: /menus?menu_id=' . $menuId); // Redirect back to menu items list
            exit();
        } else {
            $this->setFlashMessage('Błąd podczas tworzenia elementu menu', 'error');
            header('Location: /menus/' . $menuId . '/items/new');
            exit();
        }
    }

    public function updateItem($menuId, $itemId) {
        $menu = $this->menuModel->getMenuById($menuId);
        if (!$menu) {
            http_response_code(404);
            echo "Menu not found.";
            return;
        }

        $menuItem = $this->menuModel->getMenuItemById($itemId);
        if (!$menuItem || $menuItem['menu_id'] != $menuId) {
            http_response_code(404);
            echo "Menu item not found in this menu.";
            return;
        }

        $name = filter_input(INPUT_POST, 'name', FILTER_SANITIZE_STRING);
        $url = filter_input(INPUT_POST, 'url', FILTER_SANITIZE_STRING);

        // Basic validation
        if (empty($name) || empty($url)) {
            $this->setFlashMessage('Nazwa i URL są wymagane dla elementu menu', 'error');
            header('Location: /menus/' . $menuId . '/items/edit/' . $itemId);
            exit();
        }

        if ($this->menuModel->updateMenuItem($itemId, $name, $url)) {
            $this->setFlashMessage('Element menu zaktualizowany pomyślnie!', 'success');
            header('Location: /menus?menu_id=' . $menuId); // Redirect back to menu items list
            exit();
        } else {
            $this->setFlashMessage('Błąd podczas aktualizacji elementu menu', 'error');
            header('Location: /menus/' . $menuId . '/items/edit/' . $itemId);
            exit();
        }
    }

    public function deleteItem($menuId, $itemId) {
        // TODO: Ensure the item belongs to the menuId

        if ($this->menuModel->deleteMenuItem($itemId)) {
            $this->setFlashMessage('Element menu usunięty pomyślnie!', 'success');
            header('Location: /menus?menu_id=' . $menuId); // Redirect back to menu items list
            exit();
        } else {
            $this->setFlashMessage('Błąd podczas usuwania elementu menu', 'error');
            header('Location: /menus?menu_id=' . $menuId);
            exit();
        }
    }

    public function updateItemOrder($menuId) {
        // Assumes receiving an ordered array of item IDs, e.g., $_POST['item_order'] = [id1, id2, id3]
        $itemOrder = json_decode($_POST['item_order'] ?? '[]', true);

        if (empty($itemOrder) || !is_array($itemOrder)) {
            $this->setFlashMessage('Nieprawidłowe dane kolejności', 'error');
            header('Location: /menus?menu_id=' . $menuId);
            exit();
        }

        // Sanitize item IDs
        $sanitizedItemOrder = array_filter($itemOrder, 'is_numeric');

        if ($this->menuModel->updateMenuItemOrder($menuId, $sanitizedItemOrder)) {
            $this->setFlashMessage('Kolejność elementów menu zaktualizowana pomyślnie!', 'success');
            header('Location: /menus?menu_id=' . $menuId);
            exit();
        } else {
            $this->setFlashMessage('Błąd podczas aktualizacji kolejności elementów menu', 'error');
            header('Location: /menus?menu_id=' . $menuId);
            exit();
        }
    }
}
