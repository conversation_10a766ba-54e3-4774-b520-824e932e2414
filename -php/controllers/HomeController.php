<?php

require_once BASE_PATH . '/app/Controller.php';
require_once BASE_PATH . '/models/Article.php';
require_once BASE_PATH . '/models/Category.php';
require_once BASE_PATH . '/models/Tag.php';
require_once BASE_PATH . '/models/Gallery.php';
require_once BASE_PATH . '/models/Banner.php';
require_once BASE_PATH . '/models/StaticPage.php';
require_once BASE_PATH . '/models/User.php';

class HomeController extends Controller {
    public function __construct($dbConnection) {
        parent::__construct($dbConnection);

        // Check if user is logged in for all actions in this controller
        if (!isset($_SESSION['user_id'])) {
            // Redirect to login page if not authenticated
            header('Location: /login');
            exit();
        }
    }

    public function index() {
        $articleModel = new Article($this->db);
        $categoryModel = new Category($this->db);
        $tagModel = new Tag($this->db);
        $galleryModel = new Gallery($this->db);
        $bannerModel = new Banner($this->db);
        $staticPageModel = new StaticPage($this->db);
        $userModel = new User($this->db);

        $stats = [
            ['title' => 'Articles', 'count' => $articleModel->countAll(), 'link' => '/articles'],
            ['title' => 'Categories', 'count' => $categoryModel->countAll(), 'link' => '/categories'],
            ['title' => 'Tags', 'count' => $tagModel->countAll(), 'link' => '/tags'],
            ['title' => 'Gallery Items', 'count' => $galleryModel->countAll(), 'link' => '/gallery'],
            ['title' => 'Banners', 'count' => $bannerModel->countAll(), 'link' => '/banners'],
            ['title' => 'Static Pages', 'count' => $staticPageModel->countAll(), 'link' => '/pages'],
            ['title' => 'Users', 'count' => $userModel->countAll(), 'link' => '/users'],
        ];

        $recentArticles = $articleModel->getRecent(5); // Get last 5 articles

        $this->renderView('dashboard/DashboardView', ['stats' => $stats, 'recentArticles' => $recentArticles]);
    }
}
