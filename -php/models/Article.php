<?php

class Article {
    private $db;
    private $uploadDir = BASE_PATH . '/public/uploads/articles/';

    public function __construct($dbConnection) {
        $this->db = $dbConnection;
        // Ensure upload directory exists
        if (!is_dir($this->uploadDir)) {
            mkdir($this->uploadDir, 0777, true);
        }
    }

    public function getAllArticles() {
        $stmt = $this->db->query("SELECT a.*, c.name as category_name FROM articles a LEFT JOIN categories c ON a.category_id = c.id");
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getArticleById($id) {
        $stmt = $this->db->prepare("SELECT * FROM articles WHERE id = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function createArticle($title, $slug, $excerpt, $content, $categoryId, $published, $publishedStart = null, $publishedEnd = null, $attachmentFilename = null, $attachmentFilepath = null) {
        $stmt = $this->db->prepare("INSERT INTO articles (title, slug, excerpt, content, category_id, published, published_start, published_end, attachment_filename, attachment_filepath) VALUES (:title, :slug, :excerpt, :content, :category_id, :published, :published_start, :published_end, :attachment_filename, :attachment_filepath)");
        $stmt->bindParam(':title', $title, PDO::PARAM_STR);
        $stmt->bindParam(':slug', $slug, PDO::PARAM_STR);
        $stmt->bindParam(':excerpt', $excerpt, PDO::PARAM_STR);
        $stmt->bindParam(':content', $content, PDO::PARAM_STR);
        $stmt->bindParam(':category_id', $categoryId, PDO::PARAM_INT);
        $stmt->bindParam(':published', $published, PDO::PARAM_INT);
        $stmt->bindParam(':published_start', $publishedStart, $publishedStart ? PDO::PARAM_STR : PDO::PARAM_NULL);
        $stmt->bindParam(':published_end', $publishedEnd, $publishedEnd ? PDO::PARAM_STR : PDO::PARAM_NULL);
        $stmt->bindParam(':attachment_filename', $attachmentFilename, $attachmentFilename ? PDO::PARAM_STR : PDO::PARAM_NULL);
        $stmt->bindParam(':attachment_filepath', $attachmentFilepath, $attachmentFilepath ? PDO::PARAM_STR : PDO::PARAM_NULL);
        return $stmt->execute();
    }

    public function updateArticle($id, $title, $slug, $excerpt, $content, $categoryId, $published, $publishedStart = null, $publishedEnd = null, $attachmentFilename = null, $attachmentFilepath = null) {
        $stmt = $this->db->prepare("UPDATE articles SET title = :title, slug = :slug, excerpt = :excerpt, content = :content, category_id = :category_id, published = :published, published_start = :published_start, published_end = :published_end, attachment_filename = :attachment_filename, attachment_filepath = :attachment_filepath, updated_at = CURRENT_TIMESTAMP WHERE id = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->bindParam(':title', $title, PDO::PARAM_STR);
        $stmt->bindParam(':slug', $slug, PDO::PARAM_STR);
        $stmt->bindParam(':excerpt', $excerpt, PDO::PARAM_STR);
        $stmt->bindParam(':content', $content, PDO::PARAM_STR);
        $stmt->bindParam(':category_id', $categoryId, PDO::PARAM_INT);
        $stmt->bindParam(':published', $published, PDO::PARAM_INT);
        $stmt->bindParam(':published_start', $publishedStart, $publishedStart ? PDO::PARAM_STR : PDO::PARAM_NULL);
        $stmt->bindParam(':published_end', $publishedEnd, $publishedEnd ? PDO::PARAM_STR : PDO::PARAM_NULL);
        $stmt->bindParam(':attachment_filename', $attachmentFilename, $attachmentFilename ? PDO::PARAM_STR : PDO::PARAM_NULL);
        $stmt->bindParam(':attachment_filepath', $attachmentFilepath, $attachmentFilepath ? PDO::PARAM_STR : PDO::PARAM_NULL);
        return $stmt->execute();
    }

    public function deleteArticle($id) {
        $article = $this->getArticleById($id);
        if (!$article) {
            return false; // Article not found
        }

        // Delete attachment file if exists
        if (!empty($article['attachment_filepath'])) {
            $fullFilepath = BASE_PATH . '/public' . $article['attachment_filepath'];
            if (file_exists($fullFilepath)) {
                unlink($fullFilepath);
            }
        }

        // Delete record from database
        $stmt = $this->db->prepare("DELETE FROM articles WHERE id = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        return $stmt->execute();
    }

    public function getTagsForArticle($articleId) {
        $stmt = $this->db->prepare("
            SELECT t.name
            FROM tags t
            JOIN article_tags at ON t.id = at.tag_id
            WHERE at.article_id = :article_id
        ");
        $stmt->bindParam(':article_id', $articleId, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_COLUMN);
    }

    public function searchArticles($searchTerm) {
        $stmt = $this->db->prepare("
            SELECT a.*, c.name as category_name
            FROM articles a
            LEFT JOIN categories c ON a.category_id = c.id
            WHERE a.title LIKE :searchTerm OR c.name LIKE :searchTerm
        ");
        $searchTerm = '%' . $searchTerm . '%';
        $stmt->bindParam(':searchTerm', $searchTerm, PDO::PARAM_STR);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function syncTags($articleId, $tagIds) {
        // Start a transaction
        $this->db->beginTransaction();

        try {
            // Delete existing tags for the article
            $deleteStmt = $this->db->prepare("DELETE FROM article_tags WHERE article_id = :article_id");
            $deleteStmt->bindParam(':article_id', $articleId, PDO::PARAM_INT);
            $deleteStmt->execute();

            // Add new tags
            if (!empty($tagIds)) {
                $insertStmt = $this->db->prepare("INSERT INTO article_tags (article_id, tag_id) VALUES (:article_id, :tag_id)");
                foreach ($tagIds as $tagId) {
                    $insertStmt->bindParam(':article_id', $articleId, PDO::PARAM_INT);
                    $insertStmt->bindParam(':tag_id', $tagId, PDO::PARAM_INT);
                    $insertStmt->execute();
                }
            }

            // Commit the transaction
            $this->db->commit();
            return true;
        } catch (Exception $e) {
            // Rollback the transaction on error
            $this->db->rollBack();
            // In a real application, log the error
            error_log("Error syncing tags for article ID " . $articleId . ": " . $e->getMessage());
            return false;
        }
    }

    public function countAll() {
        $stmt = $this->db->query("SELECT COUNT(*) FROM articles");
        return $stmt->fetchColumn();
    }

    public function getRecent($limit) {
        $stmt = $this->db->prepare("SELECT a.*, c.name as category_name FROM articles a LEFT JOIN categories c ON a.category_id = c.id ORDER BY a.created_at DESC LIMIT :limit");
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Metody dla frontendu - tylko opublikowane artykuły
    public function getPublishedArticles($limit = null, $categoryId = null) {
        $sql = "SELECT a.*, c.name as category_name, c.slug as category_slug
                FROM articles a
                LEFT JOIN categories c ON a.category_id = c.id
                WHERE a.published = 1
                AND (a.published_start IS NULL OR a.published_start <= datetime('now'))
                AND (a.published_end IS NULL OR a.published_end >= datetime('now'))
                ORDER BY a.created_at DESC";

        if ($categoryId) {
            $sql = str_replace("WHERE a.published = 1", "WHERE a.published = 1 AND a.category_id = :category_id", $sql);
        }

        if ($limit) {
            $sql .= " LIMIT :limit";
        }

        $stmt = $this->db->prepare($sql);

        if ($categoryId) {
            $stmt->bindParam(':category_id', $categoryId, PDO::PARAM_INT);
        }

        if ($limit) {
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        }

        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getPublishedArticleBySlug($slug) {
        $stmt = $this->db->prepare("
            SELECT a.*, c.name as category_name, c.slug as category_slug
            FROM articles a
            LEFT JOIN categories c ON a.category_id = c.id
            WHERE a.slug = :slug
            AND a.published = 1
            AND (a.published_start IS NULL OR a.published_start <= datetime('now'))
            AND (a.published_end IS NULL OR a.published_end >= datetime('now'))
        ");
        $stmt->bindParam(':slug', $slug, PDO::PARAM_STR);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function searchPublishedArticles($query) {
        $stmt = $this->db->prepare("
            SELECT a.*, c.name as category_name, c.slug as category_slug
            FROM articles a
            LEFT JOIN categories c ON a.category_id = c.id
            WHERE a.published = 1
            AND (a.published_start IS NULL OR a.published_start <= datetime('now'))
            AND (a.published_end IS NULL OR a.published_end >= datetime('now'))
            AND (a.title LIKE :query OR a.content LIKE :query OR a.excerpt LIKE :query)
            ORDER BY a.created_at DESC
        ");
        $searchQuery = '%' . $query . '%';
        $stmt->bindParam(':query', $searchQuery, PDO::PARAM_STR);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function uploadAttachment($file) {
        if ($file['error'] !== UPLOAD_ERR_OK) {
            // Handle upload errors
            return false;
        }

        $filename = uniqid() . '_' . basename($file['name']);
        $filepath = $this->uploadDir . $filename;

        if (move_uploaded_file($file['tmp_name'], $filepath)) {
            // Return filename and relative path for database
            return [
                'filename' => $filename,
                'filepath' => '/uploads/articles/' . $filename // Store relative path in DB
            ];
        } else {
            // Handle move failed error
            return false;
        }
    }

    public function uploadMultipleFiles($files, $articleId) {
        $uploadedFiles = [];

        // Debug: log received files
        error_log("uploadMultipleFiles called for article ID: $articleId");
        error_log("Files received: " . print_r($files, true));

        // Dozwolone typy plików
        $allowedImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        $allowedDocumentTypes = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-powerpoint',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'application/zip',
            'application/x-rar-compressed',
            'application/x-7z-compressed',
            'text/plain',
            'text/csv'
        ];

        $allowedTypes = array_merge($allowedImageTypes, $allowedDocumentTypes);
        $maxFileSize = 10 * 1024 * 1024; // 10MB

        if (!empty($files['tmp_name'])) {
            foreach ($files['tmp_name'] as $index => $tmpName) {
                if ($files['error'][$index] === UPLOAD_ERR_OK) {
                    $originalFilename = $files['name'][$index];
                    $mimeType = $files['type'][$index];
                    $fileSize = $files['size'][$index];

                    // Walidacja typu pliku
                    if (!in_array($mimeType, $allowedTypes)) {
                        error_log("Nieprawidłowy typ pliku: $mimeType dla pliku $originalFilename");
                        continue;
                    }

                    // Walidacja rozmiaru pliku
                    if ($fileSize > $maxFileSize) {
                        error_log("Plik zbyt duży: $fileSize bajtów dla pliku $originalFilename");
                        continue;
                    }

                    // Dodatkowa walidacja przez finfo
                    $finfo = finfo_open(FILEINFO_MIME_TYPE);
                    $detectedMimeType = finfo_file($finfo, $tmpName);
                    finfo_close($finfo);

                    // Sprawdź czy wykryty typ jest w dozwolonych typach (zamiast dokładnego dopasowania)
                    if (!in_array($detectedMimeType, $allowedTypes)) {
                        error_log("Niewspierany wykryty typ MIME: $detectedMimeType dla pliku $originalFilename");
                        continue;
                    }

                    // Użyj wykrytego typu MIME zamiast deklarowanego
                    $mimeType = $detectedMimeType;

                    $filename = uniqid() . '_' . basename($originalFilename);
                    $filepath = $this->uploadDir . $filename;

                    // Determine file type based on MIME type
                    $fileType = 'attachment';
                    if (in_array($mimeType, $allowedImageTypes)) {
                        $fileType = 'image';
                    }

                    if (move_uploaded_file($tmpName, $filepath)) {
                        error_log("File uploaded successfully: $filepath");

                        // Save file info to database
                        $fileId = $this->saveFileToDatabase($articleId, $filename, $originalFilename, '/uploads/articles/' . $filename, $fileType, $mimeType, $fileSize);

                        if ($fileId) {
                            error_log("File saved to database with ID: $fileId");
                            $uploadedFiles[] = [
                                'id' => $fileId,
                                'filename' => $filename,
                                'original_filename' => $originalFilename,
                                'filepath' => '/uploads/articles/' . $filename,
                                'file_type' => $fileType,
                                'mime_type' => $mimeType,
                                'file_size' => $fileSize
                            ];
                        } else {
                            error_log("Failed to save file to database: $originalFilename");
                        }
                    } else {
                        error_log("Failed to move uploaded file: $tmpName to $filepath");
                    }
                }
            }
        }

        return $uploadedFiles;
    }

    private function saveFileToDatabase($articleId, $filename, $originalFilename, $filepath, $fileType, $mimeType, $fileSize) {
        error_log("Saving file to database: articleId=$articleId, filename=$filename, type=$fileType");

        // Get next position for this article and file type
        $positionStmt = $this->db->prepare("SELECT COALESCE(MAX(position), 0) + 1 as next_position FROM article_files WHERE article_id = :article_id AND file_type = :file_type");
        $positionStmt->bindParam(':article_id', $articleId, PDO::PARAM_INT);
        $positionStmt->bindParam(':file_type', $fileType, PDO::PARAM_STR);
        $positionStmt->execute();
        $nextPosition = $positionStmt->fetch(PDO::FETCH_ASSOC)['next_position'];

        $stmt = $this->db->prepare("INSERT INTO article_files (article_id, filename, original_filename, filepath, file_type, mime_type, file_size, position) VALUES (:article_id, :filename, :original_filename, :filepath, :file_type, :mime_type, :file_size, :position)");
        $stmt->bindParam(':article_id', $articleId, PDO::PARAM_INT);
        $stmt->bindParam(':filename', $filename, PDO::PARAM_STR);
        $stmt->bindParam(':original_filename', $originalFilename, PDO::PARAM_STR);
        $stmt->bindParam(':filepath', $filepath, PDO::PARAM_STR);
        $stmt->bindParam(':file_type', $fileType, PDO::PARAM_STR);
        $stmt->bindParam(':mime_type', $mimeType, PDO::PARAM_STR);
        $stmt->bindParam(':file_size', $fileSize, PDO::PARAM_INT);
        $stmt->bindParam(':position', $nextPosition, PDO::PARAM_INT);

        if ($stmt->execute()) {
            $lastId = $this->db->lastInsertId();
            error_log("File saved to database successfully with ID: $lastId, position: $nextPosition");
            return $lastId;
        } else {
            $errorInfo = $stmt->errorInfo();
            error_log("Database error saving file: " . print_r($errorInfo, true));
            return false;
        }
    }

    public function getArticleFiles($articleId, $fileType = null) {
        $sql = "SELECT * FROM article_files WHERE article_id = :article_id";
        if ($fileType) {
            $sql .= " AND file_type = :file_type";
        }
        $sql .= " ORDER BY position ASC, created_at ASC";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':article_id', $articleId, PDO::PARAM_INT);
        if ($fileType) {
            $stmt->bindParam(':file_type', $fileType, PDO::PARAM_STR);
        }
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function deleteArticleFile($fileId) {
        // Get file info first
        $stmt = $this->db->prepare("SELECT * FROM article_files WHERE id = :id");
        $stmt->bindParam(':id', $fileId, PDO::PARAM_INT);
        $stmt->execute();
        $file = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($file) {
            // Delete physical file
            $fullFilepath = BASE_PATH . '/public' . $file['filepath'];
            if (file_exists($fullFilepath)) {
                unlink($fullFilepath);
            }

            // Delete from database
            $stmt = $this->db->prepare("DELETE FROM article_files WHERE id = :id");
            $stmt->bindParam(':id', $fileId, PDO::PARAM_INT);
            return $stmt->execute();
        }

        return false;
    }

    public function updateFilePosition($fileId, $position) {
        $stmt = $this->db->prepare("UPDATE article_files SET position = :position WHERE id = :id");
        $stmt->bindParam(':position', $position, PDO::PARAM_INT);
        $stmt->bindParam(':id', $fileId, PDO::PARAM_INT);
        return $stmt->execute();
    }

    public function updateFilePositions($filePositions) {
        // Start transaction for batch update
        $this->db->beginTransaction();

        try {
            $stmt = $this->db->prepare("UPDATE article_files SET position = :position WHERE id = :id");

            foreach ($filePositions as $fileId => $position) {
                $stmt->bindParam(':position', $position, PDO::PARAM_INT);
                $stmt->bindParam(':id', $fileId, PDO::PARAM_INT);
                $stmt->execute();
            }

            $this->db->commit();
            return true;
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Error updating file positions: " . $e->getMessage());
            return false;
        }
    }

    public function updateArticleAttachments($articleId, $attachments) {
        // For now we'll just use the first attachment
        // In future we might want to implement multiple attachments
        $attachment = $attachments[0] ?? null;

        if ($attachment) {
            $stmt = $this->db->prepare("UPDATE articles SET attachment_filename = :filename, attachment_filepath = :filepath WHERE id = :id");
            $stmt->bindParam(':filename', $attachment['filename'], PDO::PARAM_STR);
            $stmt->bindParam(':filepath', $attachment['filepath'], PDO::PARAM_STR);
            $stmt->bindParam(':id', $articleId, PDO::PARAM_INT);
            return $stmt->execute();
        }
        return false;
    }
}
