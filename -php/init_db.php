<?php
define('BASE_PATH', dirname(__DIR__));
require_once __DIR__ . '/app/Database.php';

try {
    $database = new Database();
    $db = $database->getConnection();

    // Skrypt SQL inicjalizacji bazy danych
    $sql = <<<SQL
-- 1. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> istniejących tabel
DROP TABLE IF EXISTS article_tags;
DROP TABLE IF EXISTS articles;
DROP TABLE IF EXISTS categories;
DROP TABLE IF EXISTS tags;
DROP TABLE IF EXISTS static_pages;
DROP TABLE IF EXISTS menu_items;
DROP TABLE IF EXISTS menus;
DROP TABLE IF EXISTS gallery_images;
DROP TABLE IF EXISTS gallery_categories;
DROP TABLE IF EXISTS banners;
DROP TABLE IF EXISTS users;
DROP TABLE IF EXISTS article_files;
DROP TABLE IF EXISTS static_page_files;

-- 2. Tworzenie tabel
CREATE TABLE categories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT UNIQUE NOT NULL,
    slug TEXT UNIQUE NOT NULL
);

CREATE TABLE tags (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT UNIQUE NOT NULL,
    slug TEXT UNIQUE NOT NULL
);

CREATE TABLE articles (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,
    slug TEXT UNIQUE NOT NULL,
    excerpt TEXT DEFAULT NULL,
    content TEXT,
    category_id INTEGER,
    published INTEGER DEFAULT 0,
    published_start DATETIME DEFAULT NULL,
    published_end DATETIME DEFAULT NULL,
    attachment_filename TEXT DEFAULT NULL,
    attachment_filepath TEXT DEFAULT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL
);

CREATE TABLE article_files (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    article_id INTEGER NOT NULL,
    filename TEXT NOT NULL,
    original_filename TEXT NOT NULL,
    filepath TEXT NOT NULL,
    file_type TEXT NOT NULL, -- 'image' or 'attachment'
    mime_type TEXT,
    file_size INTEGER,
    position INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (article_id) REFERENCES articles(id) ON DELETE CASCADE
);

CREATE TABLE static_page_files (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    static_page_id INTEGER NOT NULL,
    filename TEXT NOT NULL,
    original_filename TEXT NOT NULL,
    filepath TEXT NOT NULL,
    file_type TEXT NOT NULL, -- 'image' or 'attachment'
    mime_type TEXT,
    file_size INTEGER,
    position INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (static_page_id) REFERENCES static_pages(id) ON DELETE CASCADE
);

CREATE TABLE article_tags (
    article_id INTEGER,
    tag_id INTEGER,
    PRIMARY KEY (article_id, tag_id),
    FOREIGN KEY (article_id) REFERENCES articles(id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE
);

CREATE TABLE static_pages (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,
    slug TEXT UNIQUE NOT NULL,
    content TEXT,
    published INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE menus (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    slug TEXT UNIQUE NOT NULL
);

CREATE TABLE menu_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    menu_id INTEGER NOT NULL,
    parent_id INTEGER DEFAULT NULL,
    title TEXT NOT NULL,
    url TEXT NOT NULL,
    position INTEGER DEFAULT 0,
    FOREIGN KEY (menu_id) REFERENCES menus(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES menu_items(id) ON DELETE CASCADE
);

CREATE TABLE gallery_categories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    slug TEXT UNIQUE NOT NULL
);

CREATE TABLE gallery_images (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    category_id INTEGER,
    title TEXT,
    description TEXT,
    filename TEXT NOT NULL,
    position INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES gallery_categories(id) ON DELETE SET NULL
);

CREATE TABLE banners (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,
    content TEXT,
    image TEXT,
    position INTEGER DEFAULT 0,
    active INTEGER DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE NOT NULL,
    email TEXT UNIQUE NOT NULL,
    password TEXT NOT NULL,
    role TEXT DEFAULT 'editor',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 3. Wstawianie przykładowych danych
INSERT INTO categories (name, slug) VALUES 
    ('Technologia', 'technologia'),
    ('Nauka', 'nauka'),
    ('Polityka', 'polityka');

INSERT INTO tags (name, slug) VALUES 
    ('AI', 'ai'),
    ('Kosmos', 'kosmos'),
    ('Ekonomia', 'ekonomia');

INSERT INTO articles (title, slug, excerpt, content, category_id, published, published_start, published_end) VALUES 
    ('Pierwszy artykuł', 'pierwszy-artykul', 'Krótki opis pierwszego artykułu', 'Pełna treść pierwszego artykułu', 1, 1, datetime('now'), datetime('now', '+1 month')),
    ('Drugi artykuł', 'drugi-artykul', 'Krótki opis drugiego artykułu', 'Pełna treść drugiego artykułu', 2, 1, NULL, NULL),
    ('Trzeci artykuł', 'trzeci-artykul', 'Krótki opis trzeciego artykułu', 'Pełna treść trzeciego artykułu', 3, 0, NULL, NULL);

INSERT INTO article_tags (article_id, tag_id) VALUES 
    (1, 1), (1, 2), (2, 2), (3, 3);

INSERT INTO static_pages (title, slug, content, published) VALUES
    ('O nas', 'o-nas', 'Treść strony o nas', 1),
    ('Kontakt', 'kontakt', 'Dane kontaktowe', 1);

INSERT INTO menus (name, slug) VALUES
    ('Główne menu', 'main-menu'),
    ('Stopka', 'footer');

INSERT INTO menu_items (menu_id, title, url, position) VALUES
    (1, 'Strona główna', '/', 1),
    (1, 'Artykuły', '/artykuly', 2),
    (1, 'Kontakt', '/kontakt', 3),
    (2, 'Polityka prywatności', '/polityka-prywatnosci', 1),
    (2, 'Regulamin', '/regulamin', 2);

INSERT INTO gallery_categories (name, slug) VALUES
    ('Wydarzenia', 'wydarzenia'),
    ('Produkty', 'produkty');

INSERT INTO gallery_images (category_id, title, filename) VALUES
    (1, 'Konferencja 2023', 'konferencja.jpg'),
    (2, 'Nowy produkt', 'produkt.jpg');

INSERT INTO banners (title, content, image, position) VALUES
    ('Promocja!', 'Specjalna oferta dla nowych klientów', 'banner1.jpg', 1),
    ('Nowości', 'Zobacz nasze najnowsze produkty', 'banner2.jpg', 2);

INSERT INTO users (username, email, password, role) VALUES
    ('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin'),
    ('editor', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'editor');
SQL;

    // Wykonanie skryptu
    $db->exec($sql);

    echo "Baza danych została pomyślnie zainicjalizowana.\n";
} catch (PDOException $e) {
    die("Błąd bazy danych: " . $e->getMessage() . "\n");
} catch (Exception $e) {
    die("Błąd: " . $e->getMessage() . "\n");
}
