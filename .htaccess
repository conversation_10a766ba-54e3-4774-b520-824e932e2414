RewriteEngine On

# Przekieruj wszystkie żądania do index.php, chyba że plik/katalog istnieje
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# Obsługa plików statycznych z themes
RewriteCond %{REQUEST_FILENAME} -f
RewriteRule ^cms-content/themes/(.*)$ cms-content/themes/$1 [L]

# Obsługa plików uploads
RewriteCond %{REQUEST_FILENAME} -f
RewriteRule ^cms-content/uploads/(.*)$ cms-content/uploads/$1 [L]

# Bezpieczeństwo - ukryj pliki .php w URL (opcjonalne)
RewriteCond %{THE_REQUEST} /([^?]+)\.php[?\s] [NC]
RewriteRule ^ /%1? [NC,L,R=301]
