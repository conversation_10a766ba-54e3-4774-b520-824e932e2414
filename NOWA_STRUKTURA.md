# Nowa struktura katalogów CMS

## Przegląd zmian

Aplikacja CMS została zreorganizowana zgodnie z nowymi wymaganiami. Oto główne zmiany:

### Nowa struktura katalogów

```
nowy-cms/
├── index.php                    # Główny router frontendu
├── cms-config.php              # Główny plik konfiguracyjny
├── database.sqlite             # Baza danych (przeniesiona z php/)
├── .htaccess                   # Konfiguracja Apache
├── cms-admin/                  # Backend/Panel administracyjny
│   ├── public/
│   │   └── index.php          # Router backendu
│   ├── app/                   # Klasy aplikacji
│   ├── controllers/           # Kontrolery
│   ├── models/                # Modele
│   ├── views/                 # Widoki
│   ├── lang/                  # Pliki językowe
│   └── init_db.php           # Inicjalizacja bazy danych
├── cms-content/               # Frontend/Treści
│   ├── app/                   # Klasy frontendu
│   ├── controllers/           # Kontrolery frontendu
│   ├── themes/                # Skórki/motywy
│   │   └── zrywbud/          # Domyślna skórka
│   │       ├── templates/     # Szab<PERSON>y stron
│   │       ├── partials/      # Częściowe szablony
│   │       ├── style.css      # Style CSS
│   │       ├── script.js      # Skrypty JavaScript
│   │       └── gallery.js     # Skrypty galerii
│   └── uploads/               # Wszystkie pliki uploadowane
│       ├── articles/          # Pliki artykułów
│       ├── banners/           # Obrazy banerów
│       ├── gallery/           # Zdjęcia galerii
│       └── static_pages/      # Pliki stron statycznych
└── [stare katalogi]           # Zachowane dla kompatybilności
```

## Kluczowe pliki

### cms-config.php
Główny plik konfiguracyjny zawierający:
- Definicje ścieżek dla całej aplikacji
- Ustawienia bazy danych
- Konfigurację uploadu
- Funkcje pomocnicze

### index.php (główny)
Router frontendu obsługujący:
- Wszystkie żądania frontendowe
- Przekierowania do panelu administracyjnego (/cms-admin)
- Obsługę plików statycznych (themes, uploads)

### cms-admin/public/index.php
Router backendu obsługujący:
- Panel administracyjny
- Autentykację użytkowników
- Zarządzanie treścią

## Uruchamianie aplikacji

### Frontend (strona publiczna)
```bash
cd nowy-cms
php -S localhost:8080 index.php
```
Dostęp: http://localhost:8080

### Backend (panel administracyjny)
```bash
cd nowy-cms
php -S localhost:8081 -t cms-admin/public
```
Dostęp: http://localhost:8081

### Alternatywnie - jeden serwer
```bash
cd nowy-cms
php -S localhost:8080 index.php
```
- Frontend: http://localhost:8080
- Backend: http://localhost:8080/cms-admin

## Migracja danych

Wszystkie pliki zostały automatycznie przeniesione:
- Uploads z różnych lokalizacji → `/cms-content/uploads/`
- Skórki z `/frontend/skins/` → `/cms-content/themes/`
- Backend z `/php/` → `/cms-admin/`
- Baza danych z `/php/database.sqlite` → `/database.sqlite`

## Ścieżki URL

### Frontend
- Strona główna: `/`
- Artykuły: `/artykuly`
- Galeria: `/galeria`
- Strony statyczne: `/{slug}`

### Backend
- Panel główny: `/cms-admin/`
- Logowanie: `/cms-admin/login`

### Pliki statyczne
- Themes: `/cms-content/themes/{theme}/`
- Uploads: `/cms-content/uploads/{type}/`

## Kompatybilność

Aplikacja zachowuje pełną kompatybilność wsteczną:
- Wszystkie funkcje działają bez zmian
- Stare ścieżki są automatycznie przekierowywane
- Baza danych pozostaje niezmieniona

## Zalety nowej struktury

1. **Klarowność**: Wyraźny podział na backend i frontend
2. **Bezpieczeństwo**: Panel administracyjny w osobnym katalogu
3. **Organizacja**: Wszystkie uploads w jednym miejscu
4. **Skalowalność**: Łatwiejsze dodawanie nowych themes
5. **Maintenance**: Prostsze zarządzanie i aktualizacje
