<?php

class FrontendController {
    protected $db;
    protected $templateEngine;
    protected $activeSkin;

    // Modele
    protected $articleModel;
    protected $staticPageModel;
    protected $galleryModel;
    protected $menuModel;
    protected $categoryModel;
    protected $bannerModel;

    public function __construct($dbConnection, $templateEngine, $activeSkin) {
        $this->db = $dbConnection;
        $this->templateEngine = $templateEngine;
        $this->activeSkin = $activeSkin;

        // Inicjalizacja modeli
        $this->articleModel = new Article($dbConnection);
        $this->staticPageModel = new StaticPage($dbConnection);
        $this->galleryModel = new Gallery($dbConnection);
        $this->menuModel = new Menu($dbConnection);
        $this->categoryModel = new Category($dbConnection);
        $this->bannerModel = new Banner($dbConnection);

        // Ustaw globalne dane dla szablonów
        $this->setGlobalTemplateData();
    }

    private function setGlobalTemplateData() {
        // Pobierz menu dla nawigacji
        $mainMenu = $this->menuModel->getPublishedMenuBySlug('main-menu');
        $mainMenuItems = [];
        if ($mainMenu) {
            $mainMenuItems = $this->menuModel->getPublishedMenuItems($mainMenu['id']);
        }

        $footerMenu = $this->menuModel->getPublishedMenuBySlug('footer');
        $footerMenuItems = [];
        if ($footerMenu) {
            $footerMenuItems = $this->menuModel->getPublishedMenuItems($footerMenu['id']);
        }

        // Ustaw globalne dane
        $this->templateEngine->setGlobalData('site_name', 'ZRYWBUD');
        $this->templateEngine->setGlobalData('skin_url', $this->templateEngine->getSkinUrl());
        $this->templateEngine->setGlobalData('main_menu', $mainMenuItems);
        $this->templateEngine->setGlobalData('footer_menu', $footerMenuItems);
        $this->templateEngine->setGlobalData('current_year', date('Y'));
    }

    public function home() {
        // Pobierz dane dla strony głównej
        $recentArticles = $this->articleModel->getPublishedArticles(6);
        $galleryImages = $this->galleryModel->getPublishedImages(null, 6);
        $banners = $this->bannerModel->getActiveBanners();

        // Dodaj tagi do artykułów
        foreach ($recentArticles as &$article) {
            $article['tags'] = $this->articleModel->getTagsForArticle($article['id']);
        }

        $this->templateEngine->render('home', [
            'title' => 'Strona główna - ZRYWBUD',
            'meta_description' => 'Profesjonalne remonty dachów i elewacji. Wieloletnie doświadczenie, najwyższa jakość i konkurencyjne ceny.',
            'recent_articles' => $recentArticles,
            'gallery_images' => $galleryImages,
            'banners' => $banners
        ]);
    }

    public function articles() {
        $page = (int)($_GET['page'] ?? 1);
        $limit = 12;
        $offset = ($page - 1) * $limit;

        $articles = $this->articleModel->getPublishedArticles($limit);
        $categories = $this->categoryModel->getAllCategories();

        // Dodaj tagi do artykułów
        foreach ($articles as &$article) {
            $article['tags'] = $this->articleModel->getTagsForArticle($article['id']);
        }

        $this->templateEngine->render('articles', [
            'title' => 'Artykuły - ZRYWBUD',
            'meta_description' => 'Najnowsze artykuły o remontach dachów i elewacji.',
            'articles' => $articles,
            'categories' => $categories,
            'current_page' => $page
        ]);
    }

    public function article($slug) {
        $article = $this->articleModel->getPublishedArticleBySlug($slug);

        if (!$article) {
            http_response_code(404);
            $this->templateEngine->render('404', [
                'title' => 'Artykuł nie znaleziony',
                'message' => 'Przepraszamy, artykuł którego szukasz nie istnieje lub nie jest opublikowany.'
            ]);
            return;
        }

        // Pobierz tagi artykułu
        $article['tags'] = $this->articleModel->getTagsForArticle($article['id']);

        // Pobierz pliki artykułu
        $article['images'] = $this->articleModel->getArticleFiles($article['id'], 'image');
        $article['files'] = $this->articleModel->getArticleFiles($article['id'], 'attachment');

        // Pobierz powiązane artykuły
        $relatedArticles = $this->articleModel->getPublishedArticles(3, $article['category_id']);

        // Usuń bieżący artykuł z powiązanych
        $relatedArticles = array_filter($relatedArticles, function ($related) use ($article) {
            return $related['id'] !== $article['id'];
        });

        $this->templateEngine->render('article', [
            'title' => $article['title'] . ' - ZRYWBUD',
            'meta_description' => $article['excerpt'] ?: substr(strip_tags($article['content']), 0, 160),
            'article' => $article,
            'related_articles' => array_slice($relatedArticles, 0, 3)
        ]);
    }

    public function category($slug) {
        $category = $this->categoryModel->getCategoryBySlug($slug);

        if (!$category) {
            http_response_code(404);
            $this->templateEngine->render('404', [
                'title' => 'Kategoria nie znaleziona',
                'message' => 'Przepraszamy, kategoria której szukasz nie istnieje.'
            ]);
            return;
        }

        $articles = $this->articleModel->getPublishedArticles(null, $category['id']);

        // Dodaj tagi do artykułów
        foreach ($articles as &$article) {
            $article['tags'] = $this->articleModel->getTagsForArticle($article['id']);
        }

        $this->templateEngine->render('category', [
            'title' => $category['name'] . ' - ZRYWBUD',
            'meta_description' => 'Artykuły z kategorii: ' . $category['name'],
            'category' => $category,
            'articles' => $articles
        ]);
    }

    public function gallery() {
        $categories = $this->galleryModel->getPublishedCategories();
        $allImages = $this->galleryModel->getPublishedImages();

        $this->templateEngine->render('gallery', [
            'title' => 'Galeria - ZRYWBUD',
            'meta_description' => 'Galeria naszych realizacji - remonty dachów i elewacji.',
            'categories' => $categories,
            'images' => $allImages
        ]);
    }

    public function galleryCategory($categorySlug) {
        // Znajdź kategorię po slug
        $categories = $this->galleryModel->getPublishedCategories();
        $category = null;

        foreach ($categories as $cat) {
            if ($cat['slug'] === $categorySlug) {
                $category = $cat;
                break;
            }
        }

        if (!$category) {
            http_response_code(404);
            $this->templateEngine->render('404', [
                'title' => 'Kategoria galerii nie znaleziona',
                'message' => 'Przepraszamy, kategoria galerii której szukasz nie istnieje.'
            ]);
            return;
        }

        $images = $this->galleryModel->getPublishedImages($category['id']);

        $this->templateEngine->render('gallery-category', [
            'title' => $category['name'] . ' - Galeria - ZRYWBUD',
            'meta_description' => 'Galeria zdjęć z kategorii: ' . $category['name'],
            'category' => $category,
            'images' => $images,
            'all_categories' => $categories
        ]);
    }

    public function page($slug) {
        $page = $this->staticPageModel->getPublishedPageBySlug($slug);

        if (!$page) {
            http_response_code(404);
            $this->templateEngine->render('404', [
                'title' => 'Strona nie znaleziona',
                'message' => 'Przepraszamy, strona której szukasz nie istnieje lub nie jest opublikowana.'
            ]);
            return;
        }

        // Pobierz pliki strony
        $page['images'] = $this->staticPageModel->getStaticPageFiles($page['id'], 'image');
        $page['files'] = $this->staticPageModel->getStaticPageFiles($page['id'], 'attachment');

        $this->templateEngine->render('page', [
            'title' => $page['title'] . ' - ZRYWBUD',
            'meta_description' => substr(strip_tags($page['content']), 0, 160),
            'page' => $page
        ]);
    }

    public function search() {
        $query = $_GET['q'] ?? '';
        $results = [];

        if (!empty($query) && strlen($query) >= 3) {
            // Wyszukaj w artykułach
            $articles = $this->articleModel->searchPublishedArticles($query);
            foreach ($articles as $article) {
                $results[] = [
                    'type' => 'article',
                    'title' => $article['title'],
                    'url' => '/artykuly/' . $article['slug'],
                    'excerpt' => $article['excerpt'] ?: substr(strip_tags($article['content']), 0, 160),
                    'date' => $article['created_at'],
                    'category' => $article['category_name']
                ];
            }

            // Wyszukaj w stronach statycznych
            $pages = $this->staticPageModel->searchPublishedPages($query);
            foreach ($pages as $page) {
                $results[] = [
                    'type' => 'page',
                    'title' => $page['title'],
                    'url' => '/' . $page['slug'],
                    'excerpt' => substr(strip_tags($page['content']), 0, 160),
                    'date' => $page['created_at'],
                    'category' => 'Strona'
                ];
            }
        }

        $this->templateEngine->render('search', [
            'title' => 'Wyszukiwanie - ZRYWBUD',
            'meta_description' => 'Wyniki wyszukiwania dla: ' . htmlspecialchars($query),
            'query' => $query,
            'results' => $results,
            'total_results' => count($results)
        ]);
    }

    public function sitemap() {
        header('Content-Type: application/xml; charset=utf-8');

        // Pobierz wszystkie opublikowane treści
        $articles = $this->articleModel->getPublishedArticles();
        $pages = $this->staticPageModel->getPublishedPages();
        $categories = $this->categoryModel->getAllCategories();

        $this->templateEngine->render('sitemap', [
            'articles' => $articles,
            'pages' => $pages,
            'categories' => $categories,
            'base_url' => 'http://' . $_SERVER['HTTP_HOST']
        ]);
    }

    public function rss() {
        header('Content-Type: application/rss+xml; charset=utf-8');

        $articles = $this->articleModel->getPublishedArticles(20);

        $this->templateEngine->render('rss', [
            'articles' => $articles,
            'base_url' => 'http://' . $_SERVER['HTTP_HOST'],
            'site_name' => 'ZRYWBUD',
            'site_description' => 'Profesjonalne remonty dachów i elewacji'
        ]);
    }
}
