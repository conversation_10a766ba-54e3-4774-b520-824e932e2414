<?php

class FrontendRouter {
    private $routes = [];
    private $dbConnection;
    private $templateEngine;
    private $activeSkin;

    public function __construct($dbConnection, $templateEngine, $activeSkin) {
        $this->dbConnection = $dbConnection;
        $this->templateEngine = $templateEngine;
        $this->activeSkin = $activeSkin;
    }

    public function addRoute($method, $path, $action) {
        $this->routes[] = [
            'method' => $method,
            'path' => $path,
            'action' => $action
        ];
    }

    public function handleRequest($method, $uri) {
        // Usuń query string
        $uri = strtok($uri, '?');
        
        foreach ($this->routes as $route) {
            if ($route['method'] !== $method) {
                continue;
            }

            // Proste dopasowanie ścieżki
            if ($route['path'] === $uri) {
                $this->callAction($route['action'], []);
                return;
            }

            // Ścieżka z parametrami
            $routeParts = explode('/', trim($route['path'], '/'));
            $uriParts = explode('/', trim($uri, '/'));

            if (count($routeParts) !== count($uriParts)) {
                continue;
            }

            $params = [];
            $match = true;

            for ($i = 0; $i < count($routeParts); $i++) {
                if (strpos($routeParts[$i], '{') === 0 && strpos($routeParts[$i], '}') === strlen($routeParts[$i]) - 1) {
                    $paramName = trim($routeParts[$i], '{}');
                    $params[$paramName] = $uriParts[$i];
                } elseif ($routeParts[$i] !== $uriParts[$i]) {
                    $match = false;
                    break;
                }
            }

            if ($match) {
                $this->callAction($route['action'], $params);
                return;
            }
        }

        // 404 Not Found
        http_response_code(404);
        $this->templateEngine->render('404', [
            'title' => 'Strona nie znaleziona',
            'message' => 'Przepraszamy, strona której szukasz nie istnieje.'
        ]);
    }

    private function callAction($action, $params) {
        list($controllerName, $methodName) = explode('@', $action);
        
        $controllerFile = FRONTEND_PATH . '/controllers/' . $controllerName . '.php';
        
        if (!file_exists($controllerFile)) {
            http_response_code(500);
            echo "Error: Controller file not found";
            return;
        }

        require_once $controllerFile;

        if (!class_exists($controllerName)) {
            http_response_code(500);
            echo "Error: Controller class not found";
            return;
        }

        $controller = new $controllerName($this->dbConnection, $this->templateEngine, $this->activeSkin);

        if (!method_exists($controller, $methodName)) {
            http_response_code(500);
            echo "Error: Method not found";
            return;
        }

        // Konwertuj tablicę asocjacyjną na indeksowaną dla call_user_func_array
        $paramValues = array_values($params);
        call_user_func_array([$controller, $methodName], $paramValues);
    }
}
