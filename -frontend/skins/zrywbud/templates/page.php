<?php
// Rozpocznij buforowanie zawartości
ob_start();
?>

<section class="py-5" style="margin-top: 100px;">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <nav aria-label="breadcrumb" class="mb-4">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="/">Strona główna</a></li>
                        <li class="breadcrumb-item active" aria-current="page"><?= htmlspecialchars($page['title']) ?></li>
                    </ol>
                </nav>

                <article class="page-content">
                    <header class="mb-5">
                        <h1 class="display-5 fw-bold mb-3"><?= htmlspecialchars($page['title']) ?></h1>
                        <div class="text-muted">
                            <small>
                                <i class="bi bi-calendar me-1"></i>
                                Opublikowano: <?= date('d.m.Y', strtotime($page['created_at'])) ?>
                                <?php if ($page['updated_at'] !== $page['created_at']): ?>
                                    | Zaktualizowano: <?= date('d.m.Y', strtotime($page['updated_at'])) ?>
                                <?php endif; ?>
                            </small>
                        </div>
                    </header>

                    <div class="page-body">
                        <?= $page['content'] ?>
                    </div>

                    <?php if (!empty($page['images'])): ?>
                        <div class="page-images mt-5">
                            <h4 class="mb-3">Galeria</h4>
                            <div class="row g-3">
                                <?php foreach ($page['images'] as $image): ?>
                                    <div class="col-md-4">
                                        <a href="<?= htmlspecialchars($image['filepath']) ?>" data-bs-toggle="modal" data-bs-target="#imageModal" data-image="<?= htmlspecialchars($image['filepath']) ?>" data-title="<?= htmlspecialchars($image['original_filename']) ?>">
                                            <img src="<?= htmlspecialchars($image['filepath']) ?>"
                                                alt="<?= htmlspecialchars($image['original_filename']) ?>"
                                                class="img-fluid rounded shadow-sm">
                                        </a>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($page['files'])): ?>
                        <div class="page-files mt-5">
                            <h4 class="mb-3">Pliki do pobrania</h4>
                            <div class="list-group">
                                <?php foreach ($page['files'] as $file): ?>
                                    <a href="<?= htmlspecialchars($file['filepath']) ?>"
                                        class="list-group-item list-group-item-action d-flex justify-content-between align-items-center"
                                        download="<?= htmlspecialchars($file['original_filename']) ?>">
                                        <div>
                                            <i class="bi bi-file-earmark-arrow-down me-2"></i>
                                            <?= htmlspecialchars($file['original_filename']) ?>
                                        </div>
                                        <small class="text-muted">
                                            <?= round($file['file_size'] / 1024, 1) ?> KB
                                        </small>
                                    </a>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </article>

                <!-- Contact CTA -->
                <div class="mt-5 p-4 bg-light rounded">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5 class="mb-2">Masz pytania?</h5>
                            <p class="mb-0 text-muted">Skontaktuj się z nami, aby uzyskać więcej informacji lub bezpłatną wycenę.</p>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <a href="/kontakt" class="btn btn-zrywbud-red">
                                <i class="bi bi-envelope me-2"></i>Skontaktuj się
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalTitle"></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <img id="imageModalImg" src="" alt="" class="img-fluid">
            </div>
        </div>
    </div>
</div>

<script>
    // Handle image modal
    document.addEventListener('DOMContentLoaded', function() {
        const imageModal = document.getElementById('imageModal');
        if (imageModal) {
            imageModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const imageSrc = button.getAttribute('data-image');
                const imageTitle = button.getAttribute('data-title');

                const modalImg = imageModal.querySelector('#imageModalImg');
                const modalTitle = imageModal.querySelector('#imageModalTitle');

                modalImg.src = imageSrc;
                modalTitle.textContent = imageTitle;
            });
        }
    });
</script>

<?php
// Pobierz zawartość bufora
$content = ob_get_clean();

// Załaduj layout bezpośrednio
extract([
    'title' => $title,
    'meta_description' => $meta_description,
    'content' => $content
]);

include __DIR__ . '/layout.php';
?>