<?php
// Rozpocznij buforowanie zawartości
ob_start();
?>

<section class="py-5" style="margin-top: 100px;">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <nav aria-label="breadcrumb" class="mb-4">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="/">Strona główna</a></li>
                        <li class="breadcrumb-item"><a href="/artykuly">Artykuły</a></li>
                        <?php if ($article['category_name']): ?>
                            <li class="breadcrumb-item"><a href="/kategoria/<?= htmlspecialchars($article['category_slug']) ?>"><?= htmlspecialchars($article['category_name']) ?></a></li>
                        <?php endif; ?>
                        <li class="breadcrumb-item active" aria-current="page"><?= htmlspecialchars($article['title']) ?></li>
                    </ol>
                </nav>
                
                <article class="article-content">
                    <header class="mb-5">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <?php if ($article['category_name']): ?>
                                <span class="badge bg-zrywbud-red fs-6"><?= htmlspecialchars($article['category_name']) ?></span>
                            <?php endif; ?>
                            <small class="text-muted">
                                <i class="bi bi-calendar me-1"></i>
                                <?= date('d.m.Y', strtotime($article['created_at'])) ?>
                                <?php if ($article['updated_at'] !== $article['created_at']): ?>
                                    | Zaktualizowano: <?= date('d.m.Y', strtotime($article['updated_at'])) ?>
                                <?php endif; ?>
                            </small>
                        </div>
                        
                        <h1 class="display-5 fw-bold mb-4"><?= htmlspecialchars($article['title']) ?></h1>
                        
                        <?php if ($article['excerpt']): ?>
                            <div class="lead text-muted mb-4">
                                <?= htmlspecialchars($article['excerpt']) ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($article['tags'])): ?>
                            <div class="article-tags mb-4">
                                <?php foreach ($article['tags'] as $tag): ?>
                                    <span class="badge bg-light text-dark me-2">#<?= htmlspecialchars($tag) ?></span>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </header>
                    
                    <div class="article-body">
                        <?= $article['content'] ?>
                    </div>
                    
                    <?php if (!empty($article['images'])): ?>
                        <div class="article-images mt-5">
                            <h4 class="mb-3">Galeria zdjęć</h4>
                            <div class="row g-3">
                                <?php foreach ($article['images'] as $image): ?>
                                    <div class="col-md-4">
                                        <a href="<?= htmlspecialchars($image['filepath']) ?>" data-bs-toggle="modal" data-bs-target="#imageModal" data-image="<?= htmlspecialchars($image['filepath']) ?>" data-title="<?= htmlspecialchars($image['original_filename']) ?>">
                                            <img src="<?= htmlspecialchars($image['filepath']) ?>" 
                                                 alt="<?= htmlspecialchars($image['original_filename']) ?>" 
                                                 class="img-fluid rounded shadow-sm hover-scale">
                                        </a>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($article['files'])): ?>
                        <div class="article-files mt-5">
                            <h4 class="mb-3">Pliki do pobrania</h4>
                            <div class="list-group">
                                <?php foreach ($article['files'] as $file): ?>
                                    <a href="<?= htmlspecialchars($file['filepath']) ?>" 
                                       class="list-group-item list-group-item-action d-flex justify-content-between align-items-center"
                                       download="<?= htmlspecialchars($file['original_filename']) ?>">
                                        <div>
                                            <i class="bi bi-file-earmark-arrow-down me-2 text-zrywbud-red"></i>
                                            <?= htmlspecialchars($file['original_filename']) ?>
                                        </div>
                                        <small class="text-muted">
                                            <?= round($file['file_size'] / 1024, 1) ?> KB
                                        </small>
                                    </a>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </article>
                
                <!-- Social Share -->
                <div class="mt-5 p-4 bg-light rounded">
                    <h5 class="mb-3">Podziel się artykułem</h5>
                    <div class="d-flex gap-2">
                        <a href="https://www.facebook.com/sharer/sharer.php?u=<?= urlencode($_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']) ?>" 
                           target="_blank" class="btn btn-outline-primary btn-sm">
                            <i class="bi bi-facebook me-1"></i>Facebook
                        </a>
                        <a href="https://twitter.com/intent/tweet?url=<?= urlencode($_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']) ?>&text=<?= urlencode($article['title']) ?>" 
                           target="_blank" class="btn btn-outline-info btn-sm">
                            <i class="bi bi-twitter me-1"></i>Twitter
                        </a>
                        <button class="btn btn-outline-secondary btn-sm" onclick="copyToClipboard()">
                            <i class="bi bi-link me-1"></i>Kopiuj link
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Sidebar -->
            <div class="col-lg-4">
                <div class="sticky-top" style="top: 120px;">
                    <!-- Related Articles -->
                    <?php if (!empty($related_articles)): ?>
                        <div class="card border-0 shadow-sm mb-4">
                            <div class="card-header bg-zrywbud-red text-white">
                                <h6 class="mb-0"><i class="bi bi-newspaper me-2"></i>Powiązane artykuły</h6>
                            </div>
                            <div class="card-body">
                                <?php foreach ($related_articles as $related): ?>
                                    <div class="mb-3 pb-3 border-bottom">
                                        <h6 class="mb-2">
                                            <a href="/artykuly/<?= htmlspecialchars($related['slug']) ?>" class="text-decoration-none">
                                                <?= htmlspecialchars($related['title']) ?>
                                            </a>
                                        </h6>
                                        <small class="text-muted">
                                            <i class="bi bi-calendar me-1"></i>
                                            <?= date('d.m.Y', strtotime($related['created_at'])) ?>
                                        </small>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Contact CTA -->
                    <div class="card border-0 shadow-sm bg-zrywbud-red text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-telephone display-4 mb-3"></i>
                            <h5>Potrzebujesz pomocy?</h5>
                            <p class="mb-3">Skontaktuj się z nami, aby otrzymać bezpłatną wycenę.</p>
                            <a href="/kontakt" class="btn btn-light btn-sm">Skontaktuj się</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalTitle"></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <img id="imageModalImg" src="" alt="" class="img-fluid">
            </div>
        </div>
    </div>
</div>

<script>
// Handle image modal
document.addEventListener('DOMContentLoaded', function() {
    const imageModal = document.getElementById('imageModal');
    if (imageModal) {
        imageModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const imageSrc = button.getAttribute('data-image');
            const imageTitle = button.getAttribute('data-title');
            
            const modalImg = imageModal.querySelector('#imageModalImg');
            const modalTitle = imageModal.querySelector('#imageModalTitle');
            
            modalImg.src = imageSrc;
            modalTitle.textContent = imageTitle;
        });
    }
});

// Copy to clipboard function
function copyToClipboard() {
    const url = window.location.href;
    navigator.clipboard.writeText(url).then(function() {
        alert('Link skopiowany do schowka!');
    });
}
</script>

<?php
// Pobierz zawartość bufora
$content = ob_get_clean();

// Załaduj layout bezpośrednio
extract([
    'title' => $title,
    'meta_description' => $meta_description,
    'content' => $content
]);

include __DIR__ . '/layout.php';
?>
