<?php
// Rozpocznij buforowanie zawartości
ob_start();
?>

<section class="py-5" style="margin-top: 100px;">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <nav aria-label="breadcrumb" class="mb-4">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="/">Strona główna</a></li>
                        <li class="breadcrumb-item"><a href="/artykuly">Artykuły</a></li>
                        <li class="breadcrumb-item active" aria-current="page"><?= htmlspecialchars($category['name']) ?></li>
                    </ol>
                </nav>
                
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h1 class="h2 mb-2">Kategoria: <?= htmlspecialchars($category['name']) ?></h1>
                        <p class="text-muted mb-0">Znaleziono <?= count($articles) ?> artykułów</p>
                    </div>
                    <a href="/artykuly" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left me-1"></i>Wszystkie artykuły
                    </a>
                </div>

                <?php if (!empty($articles)): ?>
                    <div class="row g-4">
                        <?php foreach ($articles as $article): ?>
                            <div class="col-md-6">
                                <article class="card h-100 border-0 shadow-sm">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <span class="badge bg-zrywbud-red"><?= htmlspecialchars($category['name']) ?></span>
                                            <small class="text-muted"><?= date('d.m.Y', strtotime($article['created_at'])) ?></small>
                                        </div>
                                        
                                        <h5 class="card-title">
                                            <a href="/artykuly/<?= htmlspecialchars($article['slug']) ?>" class="text-decoration-none text-dark">
                                                <?= htmlspecialchars($article['title']) ?>
                                            </a>
                                        </h5>
                                        
                                        <?php if ($article['excerpt']): ?>
                                            <p class="card-text text-muted"><?= htmlspecialchars(substr($article['excerpt'], 0, 150)) ?>...</p>
                                        <?php endif; ?>
                                        
                                        <?php if (!empty($article['tags'])): ?>
                                            <div class="mb-3">
                                                <?php foreach (array_slice($article['tags'], 0, 3) as $tag): ?>
                                                    <span class="badge bg-light text-dark me-1">#<?= htmlspecialchars($tag) ?></span>
                                                <?php endforeach; ?>
                                                <?php if (count($article['tags']) > 3): ?>
                                                    <span class="badge bg-light text-dark">+<?= count($article['tags']) - 3 ?></span>
                                                <?php endif; ?>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <a href="/artykuly/<?= htmlspecialchars($article['slug']) ?>" class="btn btn-outline-primary btn-sm">
                                            Czytaj więcej <i class="bi bi-arrow-right ms-1"></i>
                                        </a>
                                    </div>
                                </article>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="bi bi-folder-x display-1 text-muted mb-4"></i>
                        <h3>Brak artykułów w tej kategorii</h3>
                        <p class="text-muted">W kategorii "<?= htmlspecialchars($category['name']) ?>" nie ma jeszcze opublikowanych artykułów.</p>
                        <div class="mt-4">
                            <a href="/artykuly" class="btn btn-zrywbud-red me-2">Zobacz wszystkie artykuły</a>
                            <a href="/" class="btn btn-outline-secondary">Wróć do strony głównej</a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
            
            <!-- Sidebar -->
            <div class="col-lg-4">
                <div class="sticky-top" style="top: 120px;">
                    <!-- Category Info -->
                    <div class="card border-0 shadow-sm mb-4">
                        <div class="card-header bg-zrywbud-red text-white">
                            <h6 class="mb-0"><i class="bi bi-info-circle me-2"></i>O kategorii</h6>
                        </div>
                        <div class="card-body">
                            <h6><?= htmlspecialchars($category['name']) ?></h6>
                            <p class="text-muted mb-3">
                                Kategoria zawiera artykuły związane z tematyką: <?= htmlspecialchars(strtolower($category['name'])) ?>.
                            </p>
                            <div class="d-flex justify-content-between text-sm">
                                <span>Liczba artykułów:</span>
                                <strong><?= count($articles) ?></strong>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Other Categories -->
                    <div class="card border-0 shadow-sm mb-4">
                        <div class="card-header bg-light">
                            <h6 class="mb-0"><i class="bi bi-folder me-2"></i>Inne kategorie</h6>
                        </div>
                        <div class="card-body">
                            <div class="list-group list-group-flush">
                                <a href="/artykuly" class="list-group-item list-group-item-action border-0 px-0">
                                    <i class="bi bi-grid me-2"></i>Wszystkie artykuły
                                </a>
                                <!-- Tutaj można dodać inne kategorie z bazy danych -->
                            </div>
                        </div>
                    </div>
                    
                    <!-- Contact CTA -->
                    <div class="card border-0 shadow-sm bg-zrywbud-red text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-telephone display-4 mb-3"></i>
                            <h5>Potrzebujesz pomocy?</h5>
                            <p class="mb-3">Skontaktuj się z nami, aby otrzymać bezpłatną wycenę.</p>
                            <a href="/kontakt" class="btn btn-light btn-sm">Skontaktuj się</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php
// Pobierz zawartość bufora
$content = ob_get_clean();

// Załaduj layout bezpośrednio
extract([
    'title' => $title,
    'meta_description' => $meta_description,
    'content' => $content
]);

include __DIR__ . '/layout.php';
?>
