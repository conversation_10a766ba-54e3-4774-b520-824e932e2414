<?php
// Rozpocznij buforowanie zawartości
ob_start();
?>

<!-- Hero Section -->
<section class="gallery-hero d-flex align-items-center">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h1 class="display-4 fw-bold text-white mb-4 fade-in-up">
                    <?= htmlspecialchars($category['name']) ?>
                </h1>
                <p class="lead text-white mb-5 fade-in-up" style="animation-delay: 0.4s;">
                    Galeria zdjęć z kategorii: <?= htmlspecialchars($category['name']) ?>
                </p>
                <nav aria-label="breadcrumb" class="fade-in-up" style="animation-delay: 0.6s;">
                    <ol class="breadcrumb justify-content-center">
                        <li class="breadcrumb-item"><a href="/" class="text-white">Strona główna</a></li>
                        <li class="breadcrumb-item"><a href="/galeria" class="text-white">Galeria</a></li>
                        <li class="breadcrumb-item active text-zrywbud-red" aria-current="page"><?= htmlspecialchars($category['name']) ?></li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</section>

<!-- Gallery Section -->
<section class="py-5">
    <div class="container">
        <!-- Category Navigation -->
        <?php if (!empty($all_categories)): ?>
        <div class="text-center mb-5">
            <div class="btn-group flex-wrap" role="group">
                <a href="/galeria" class="btn btn-outline-secondary">
                    <i class="bi bi-grid-3x3-gap me-2"></i>Wszystkie
                </a>
                <?php foreach ($all_categories as $cat): ?>
                    <a href="/galeria/<?= htmlspecialchars($cat['slug']) ?>" 
                       class="btn <?= $cat['id'] == $category['id'] ? 'btn-zrywbud-red' : 'btn-outline-secondary' ?>">
                        <i class="bi bi-folder me-2"></i><?= htmlspecialchars($cat['name']) ?>
                    </a>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Category Info -->
        <div class="row mb-5">
            <div class="col-lg-8 mx-auto text-center">
                <h2 class="h4 mb-3"><?= htmlspecialchars($category['name']) ?></h2>
                <p class="text-muted">
                    Znaleziono <?= count($images) ?> zdjęć w tej kategorii.
                    <?php if (count($images) === 0): ?>
                        Sprawdź inne kategorie lub wróć do głównej galerii.
                    <?php endif; ?>
                </p>
            </div>
        </div>

        <!-- Gallery Grid -->
        <div class="row g-4" id="galleryContainer">
            <?php if (!empty($images)): ?>
                <?php foreach ($images as $image): ?>
                    <div class="col-lg-4 col-md-6 gallery-item">
                        <div class="gallery-card">
                            <img src="<?= htmlspecialchars($image['filepath']) ?>" 
                                 alt="<?= htmlspecialchars($image['title'] ?? 'Realizacja ZRYWBUD') ?>" 
                                 class="img-fluid">
                            <div class="gallery-overlay">
                                <span class="badge bg-zrywbud-red mb-2"><?= htmlspecialchars($category['name']) ?></span>
                                <h5><?= htmlspecialchars($image['title'] ?? 'Realizacja ZRYWBUD') ?></h5>
                                <?php if ($image['description']): ?>
                                    <p><?= htmlspecialchars($image['description']) ?></p>
                                <?php endif; ?>
                                <button class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#galleryModal" 
                                        data-title="<?= htmlspecialchars($image['title'] ?? 'Realizacja ZRYWBUD') ?>"
                                        data-image="<?= htmlspecialchars($image['filepath']) ?>"
                                        data-description="<?= htmlspecialchars($image['description'] ?? '') ?>"
                                        data-category="<?= htmlspecialchars($category['name']) ?>">
                                    Zobacz szczegóły
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="col-12 text-center">
                    <div class="py-5">
                        <i class="bi bi-images display-1 text-muted mb-4"></i>
                        <h3>Brak zdjęć w tej kategorii</h3>
                        <p class="text-muted mb-4">
                            W kategorii "<?= htmlspecialchars($category['name']) ?>" nie ma jeszcze żadnych zdjęć.
                        </p>
                        <div class="d-flex flex-column flex-sm-row gap-3 justify-content-center">
                            <a href="/galeria" class="btn btn-zrywbud-red">
                                <i class="bi bi-grid me-2"></i>Zobacz wszystkie zdjęcia
                            </a>
                            <a href="/" class="btn btn-outline-secondary">
                                <i class="bi bi-house me-2"></i>Wróć do strony głównej
                            </a>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Back to Gallery -->
        <?php if (!empty($images)): ?>
        <div class="text-center mt-5">
            <a href="/galeria" class="btn btn-outline-secondary btn-lg">
                <i class="bi bi-arrow-left me-2"></i>Wróć do głównej galerii
            </a>
        </div>
        <?php endif; ?>
    </div>
</section>

<!-- Gallery Modal -->
<div class="modal fade" id="galleryModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle"></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-lg-8">
                        <img id="modalImage" src="" alt="" class="img-fluid rounded">
                    </div>
                    <div class="col-lg-4">
                        <span id="modalCategory" class="badge bg-zrywbud-red mb-3"></span>
                        <p id="modalDescription" class="text-muted"></p>
                        <div class="mt-4">
                            <h6>Szczegóły projektu:</h6>
                            <ul class="list-unstyled">
                                <li><i class="bi bi-check-circle text-success me-2"></i>Profesjonalne materiały</li>
                                <li><i class="bi bi-check-circle text-success me-2"></i>Gwarancja na wykonane prace</li>
                                <li><i class="bi bi-check-circle text-success me-2"></i>Terminowa realizacja</li>
                                <li><i class="bi bi-check-circle text-success me-2"></i>Doświadczony zespół</li>
                            </ul>
                        </div>
                        <a href="/kontakt" class="btn btn-zrywbud-red w-100 mt-3" data-bs-dismiss="modal">
                            <i class="bi bi-envelope me-2"></i>Zapytaj o podobny projekt
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Pobierz zawartość bufora
$content = ob_get_clean();

// Dodatkowe skrypty dla galerii
$additional_scripts = '<script src="' . $skin_url . '/gallery.js"></script>';

// Załaduj layout bezpośrednio
extract([
    'title' => $title,
    'meta_description' => $meta_description,
    'content' => $content,
    'additional_scripts' => $additional_scripts
]);

include __DIR__ . '/layout.php';
?>
