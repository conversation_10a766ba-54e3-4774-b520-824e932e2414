# Frontend CMS - System Szablonów

## Opis

To jest frontend dla systemu CMS napisany w PHP z obsługą wielu skórek. System oddziela backend (zarządzanie treścią) od frontendu (wyświetlanie treści).

## Struktura katalogów

```
frontend/
├── index.php                 # Główny punkt wejścia
├── .htaccess                 # Konfiguracja Apache
├── app/                      # Klasy aplikacji
│   ├── FrontendRouter.php    # Router frontendu
│   ├── TemplateEngine.php    # Silnik szablonów
│   └── FrontendController.php # Główny kontroler
├── controllers/              # Kontrolery
│   └── FrontendController.php # Symlink do app/FrontendController.php
└── skins/                    # Skórki/motywy
    └── zrywbud/              # Domyślna skórka
        ├── templates/        # Główne szablony
        │   ├── layout.php    # Główny layout
        │   ├── home.php      # Strona główna
        │   ├── articles.php  # Lista artykułów
        │   ├── gallery.php   # Galeria
        │   ├── page.php      # Strona statyczna
        │   └── 404.php       # Strona błędu
        ├── partials/         # Częściowe szablony
        │   ├── header.php    # Nagłówek
        │   └── footer.php    # Stopka
        ├── style.css         # Style CSS
        ├── script.js         # Skrypty JavaScript
        └── gallery.js        # Skrypty galerii
```

## Funkcjonalności

### Dostępne trasy:
- `/` - Strona główna
- `/artykuly` - Lista artykułów
- `/artykuly/{slug}` - Pojedynczy artykuł
- `/kategoria/{slug}` - Artykuły z kategorii
- `/galeria` - Galeria zdjęć
- `/galeria/{category}` - Galeria kategorii
- `/{slug}` - Strony statyczne

### System szablonów:
- **Layout** - główny szablon z HTML, head, header, footer
- **Templates** - szablony stron (home, articles, gallery, etc.)
- **Partials** - częściowe szablony (header, footer, etc.)

### Obsługa wielu skórek:
- Skórki w katalogu `skins/`
- Przełączanie przez parametr `?skin=nazwa_skorki`
- Każda skórka ma własne CSS, JS i szablony

## Instalacja

1. **Skopiuj pliki frontendu** do katalogu `frontend/` w głównym katalogu projektu

2. **Skonfiguruj serwer web** aby wskazywał na katalog `frontend/` dla domeny frontendu

3. **Upewnij się że backend działa** - frontend korzysta z modeli backendu

4. **Sprawdź uprawnienia** - katalogi muszą być czytelne przez serwer web

## Konfiguracja Apache

Przykład konfiguracji VirtualHost:

```apache
<VirtualHost *:80>
    ServerName frontend.example.com
    DocumentRoot /path/to/project/frontend
    
    <Directory /path/to/project/frontend>
        AllowOverride All
        Require all granted
    </Directory>
</VirtualHost>
```

## Tworzenie nowej skórki

1. **Utwórz katalog** `skins/nazwa_skorki/`

2. **Skopiuj strukturę** z istniejącej skórki:
   ```bash
   cp -r skins/zrywbud skins/nowa_skorka
   ```

3. **Dostosuj szablony** w katalogach `templates/` i `partials/`

4. **Zmień style** w pliku `style.css`

5. **Dostosuj skrypty** w plikach `script.js` i `gallery.js`

## API dla szablonów

### Globalne zmienne dostępne w szablonach:
- `$site_name` - nazwa strony
- `$skin_url` - URL do zasobów skórki
- `$main_menu` - elementy głównego menu
- `$footer_menu` - elementy menu stopki
- `$current_year` - bieżący rok

### Metody template engine:
- `$this->render('template', $data)` - renderuj szablon
- `$this->renderPartial('partial', $data)` - renderuj częściowy szablon
- `$this->getPartial('partial', $data)` - pobierz częściowy szablon jako string

## Bezpieczeństwo

- Wszystkie dane wyjściowe są escapowane przez `htmlspecialchars()`
- Tylko opublikowane treści są wyświetlane
- Walidacja dat publikacji artykułów
- Ochrona przed bezpośrednim dostępem do plików PHP

## Rozwój

### Dodawanie nowych tras:
1. Dodaj trasę w `frontend/index.php`
2. Dodaj metodę w `FrontendController`
3. Utwórz szablon w `skins/nazwa_skorki/templates/`

### Dodawanie nowych funkcji do szablonów:
1. Rozszerz `TemplateEngine` o nowe metody
2. Dodaj globalne dane w `FrontendController::setGlobalTemplateData()`

## Debugowanie

- Błędy są wyświetlane w trybie development
- Logi błędów w `/tmp/php_debug.log`
- Sprawdź uprawnienia do plików i katalogów
- Sprawdź konfigurację .htaccess
