<?php
// Frontend główny punkt wejścia

// Włącz raportowanie błędów dla developmentu
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Defin<PERSON><PERSON>
define('FRONTEND_PATH', __DIR__);
define('BASE_PATH', dirname(__DIR__));

// Załaduj klasy backendu
require_once BASE_PATH . '/php/app/Database.php';
require_once BASE_PATH . '/php/models/Article.php';
require_once BASE_PATH . '/php/models/StaticPage.php';
require_once BASE_PATH . '/php/models/Gallery.php';
require_once BASE_PATH . '/php/models/Menu.php';
require_once BASE_PATH . '/php/models/Category.php';
require_once BASE_PATH . '/php/models/Banner.php';

// Załaduj klasy frontendu
require_once FRONTEND_PATH . '/app/FrontendRouter.php';
require_once FRONTEND_PATH . '/app/TemplateEngine.php';
require_once FRONTEND_PATH . '/app/FrontendController.php';

// Inicjalizacja bazy danych
$database = new Database();
$db = $database->getConnection();

// Określ aktywną skórkę (domyślnie zrywbud)
$activeSkin = $_GET['skin'] ?? 'zrywbud';

// Walidacja skórki - sprawdź czy katalog istnieje
$skinPath = FRONTEND_PATH . '/skins/' . $activeSkin;
if (!is_dir($skinPath)) {
    $activeSkin = 'zrywbud'; // fallback do domyślnej skórki
    $skinPath = FRONTEND_PATH . '/skins/' . $activeSkin;
}

// Inicjalizacja template engine
$templateEngine = new TemplateEngine($skinPath);

// Inicjalizacja routera
$router = new FrontendRouter($db, $templateEngine, $activeSkin);

// Definicja tras
$router->addRoute('GET', '/', 'FrontendController@home');
$router->addRoute('GET', '/artykuly', 'FrontendController@articles');
$router->addRoute('GET', '/artykuly/{slug}', 'FrontendController@article');
$router->addRoute('GET', '/kategoria/{slug}', 'FrontendController@category');
$router->addRoute('GET', '/galeria', 'FrontendController@gallery');
$router->addRoute('GET', '/galeria/{category}', 'FrontendController@galleryCategory');
$router->addRoute('GET', '/szukaj', 'FrontendController@search');
$router->addRoute('GET', '/sitemap.xml', 'FrontendController@sitemap');
$router->addRoute('GET', '/rss.xml', 'FrontendController@rss');
$router->addRoute('GET', '/{slug}', 'FrontendController@page'); // strony statyczne na końcu

// Obsługa żądania
$method = $_SERVER['REQUEST_METHOD'];
$uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

// Usuń /frontend z URI jeśli istnieje
$uri = preg_replace('#^/frontend#', '', $uri);

$router->handleRequest($method, $uri);
