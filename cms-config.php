<?php
/**
 * Główny plik konfiguracyjny CMS
 */

// <PERSON><PERSON><PERSON><PERSON>żek
define('CMS_ROOT', __DIR__);
define('CMS_ADMIN_PATH', CMS_ROOT . '/cms-admin');
define('CMS_CONTENT_PATH', CMS_ROOT . '/cms-content');
define('CMS_UPLOADS_PATH', CMS_CONTENT_PATH . '/uploads');
define('CMS_THEMES_PATH', CMS_CONTENT_PATH . '/themes');

// Konfiguracja bazy danych
define('DB_FILE', CMS_ROOT . '/database.sqlite');

// Konfiguracja uploadu
define('UPLOAD_MAX_SIZE', '50M');
define('POST_MAX_SIZE', '100M');
define('MAX_FILE_UPLOADS', 50);
define('MEMORY_LIMIT', '256M');
define('MAX_EXECUTION_TIME', 300);

// Domyślna skórka
define('DEFAULT_THEME', 'zrywbud');

// Ustawienia bezpieczeństwa
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg']);
define('ALLOWED_FILE_TYPES', ['pdf', 'doc', 'docx', 'txt', 'zip', 'rar']);
define('MAX_FILE_SIZE_BYTES', 10 * 1024 * 1024); // 10MB

// Ustawienia PHP
ini_set('upload_max_filesize', UPLOAD_MAX_SIZE);
ini_set('post_max_size', POST_MAX_SIZE);
ini_set('max_file_uploads', MAX_FILE_UPLOADS);
ini_set('memory_limit', MEMORY_LIMIT);
ini_set('max_execution_time', MAX_EXECUTION_TIME);

// Włącz raportowanie błędów w trybie deweloperskim
if (defined('CMS_DEBUG') && CMS_DEBUG) {
    ini_set('display_errors', 1);
    ini_set('display_startup_errors', 1);
    error_reporting(E_ALL);
    ini_set('log_errors', 1);
    ini_set('error_log', '/tmp/cms_debug.log');
}