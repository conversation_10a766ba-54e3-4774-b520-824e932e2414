/* 
 * Plik CSS dla strony głównej
 * Zawiera style specyficzne dla strony głównej
 */

/* <PERSON><PERSON><PERSON><PERSON> statystyk */
.stats-item {
  text-align: center;
  padding: 1.5rem;
}

.stats-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-light);
  margin-bottom: 0.5rem;
  display: block;
}

.stats-label {
  color: var(--text-light);
  font-weight: 500;
}

/* <PERSON><PERSON><PERSON> akt<PERSON> */
.news-card {
  height: 100%;
  margin-bottom: 1.5rem;
}

.news-card .card-img-container {
  position: relative;
}

.news-card .badge-container {
  position: absolute;
  top: 1rem;
  left: 1rem;
}

.news-card .type-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
}

.news-card .card-meta {
  display: flex;
  align-items: center;
  color: var(--text-light);
  font-size: 0.875rem;
  margin-bottom: 0.75rem;
}

.news-card .card-meta-divider {
  margin: 0 0.5rem;
}

.news-card .card-title {
  font-weight: 600;
  color: var(--secondary);
  transition: color 0.2s;
}

.news-card:hover .card-title {
  color: var(--primary-light);
}

.news-card .card-link {
  color: var(--primary-light);
  font-weight: 500;
  font-size: 0.875rem;
  display: inline-flex;
  align-items: center;
  transition: color 0.2s;
}

.news-card .card-link:hover {
  color: var(--primary);
  text-decoration: none;
}

.news-card .card-link-icon {
  margin-left: 0.25rem;
  transition: transform 0.2s;
}

.news-card:hover .card-link-icon {
  transform: translateX(3px);
}

/* Sekcja O nas */
.about-image {
  border-radius: 0.5rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Przycisk "Dowiedz się więcej" */
.btn-link {
  color: var(--primary) !important;
  font-weight: 500;
  text-decoration: none;
  padding: 0;
  transition: color 0.2s;
}

.btn-link:hover {
  color: var(--primary-light) !important;
}

.btn-link .fa-arrow-right {
  transition: transform 0.2s;
}

.btn-link:hover .fa-arrow-right {
  transform: translateX(3px);
}

/* Responsywność */
@media (max-width: 768px) {
  .stats-number {
    font-size: 2rem;
  }
  
  .hero-section .btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }
} 