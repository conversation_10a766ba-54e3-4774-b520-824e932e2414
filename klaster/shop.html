<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sklep - Innowacyjna Medycyna</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="css/main.css" rel="stylesheet">
    <style>
        .shop-header {
            background-color: var(--white);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            padding: 3rem 0;
            text-align: center;
        }
        
        .shop-filter {
            background-color: var(--white);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            padding: 1.5rem 0;
        }
        
        .filter-btn {
            padding: 0.5rem 1rem;
            border-radius: 50rem;
            font-size: 0.875rem;
            font-weight: 500;
            transition: all 0.2s;
            margin-bottom: 0.5rem;
        }
        
        .filter-btn.active {
            background-color: var(--primary);
            color: var(--white);
        }
        
        .product-card {
            height: 100%;
            transition: transform 0.3s, box-shadow 0.3s;
            border: none;
            border-radius: 0.5rem;
            overflow: hidden;
        }
        
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
        
        .product-img-container {
            position: relative;
            overflow: hidden;
        }
        
        .product-img {
            height: 250px;
            object-fit: cover;
            transition: transform 0.3s;
        }
        
        .product-card:hover .product-img {
            transform: scale(1.05);
        }
        
        .product-badge {
            position: absolute;
            top: 1rem;
            right: 1rem;
            padding: 0.25rem 0.75rem;
            border-radius: 50rem;
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .product-badge-new {
            background-color: #dc3545;
            color: white;
        }
        
        .product-badge-sale {
            background-color: #28a745;
            color: white;
        }
        
        .product-info {
            padding: 1.5rem;
        }
        
        .product-category {
            color: var(--text-light);
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
            display: block;
        }
        
        .product-title {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .product-description {
            color: var(--text-light);
            font-size: 0.875rem;
            margin-bottom: 1rem;
        }
        
        .product-price {
            font-weight: 700;
            color: var(--primary);
            font-size: 1.25rem;
            margin-bottom: 1rem;
        }
        
        .product-price-old {
            text-decoration: line-through;
            color: var(--text-light);
            font-size: 1rem;
            margin-right: 0.5rem;
        }
        
        .product-actions {
            display: flex;
            gap: 0.5rem;
        }
        
        .product-btn {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }
        
        .wishlist-btn {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background-color: #f8f9fa;
            color: var(--text-light);
            border: none;
            transition: all 0.2s;
        }
        
        .wishlist-btn:hover {
            background-color: #e9ecef;
            color: var(--primary);
        }
        
        .wishlist-btn.active {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <!-- Nagłówek -->
    <header class="navbar navbar-expand-lg navbar-custom sticky-top">
        <div class="container">
            <a class="navbar-brand" href="index.html">
                <img src="http://innowacyjnamedycyna.eu/wp-content/uploads/2013/11/logo.png" alt="Innowacyjna Medycyna">
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarMain" 
                    aria-controls="navbarMain" aria-expanded="false" aria-label="Przełącz nawigację">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarMain">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">Strona główna</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdownAbout" role="button" 
                           data-bs-toggle="dropdown" aria-expanded="false">
                            O nas
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="navbarDropdownAbout">
                            <li><a class="dropdown-item" href="#">Historia</a></li>
                            <li><a class="dropdown-item" href="#">Misja i wizja</a></li>
                            <li><a class="dropdown-item" href="#">Zespół</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="articles.html">Artykuły</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="gallery.html">Galeria</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="shop.html">Sklep</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="downloads.html">Materiały do pobrania</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="members.html">Członkowie klastra</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.html">Kontakt</a>
                    </li>
                </ul>
            </div>
        </div>
    </header>

    <!-- Nagłówek strony -->
    <section class="shop-header">
        <div class="container">
            <h1 class="display-4 fw-bold">Sklep</h1>
            <p class="lead text-muted mx-auto" style="max-width: 700px;">
                Publikacje, materiały edukacyjne i gadżety związane z działalnością klastra
            </p>
        </div>
    </section>

    <!-- Filtry -->
    <section class="shop-filter">
        <div class="container">
            <div class="row">
                <!-- Wyszukiwarka -->
                <div class="col-lg-4 mb-3 mb-lg-0">
                    <div class="position-relative">
                        <i class="fas fa-search position-absolute" style="top: 12px; left: 15px; color: var(--text-light);"></i>
                        <input type="text" id="searchInput" class="form-control ps-4" placeholder="Szukaj produktów...">
                    </div>
                </div>
                
                <!-- Filtry -->
                <div class="col-lg-8">
                    <div class="d-flex flex-wrap gap-2">
                        <button class="btn filter-btn active" data-filter="all">Wszystkie</button>
                        <button class="btn filter-btn" data-filter="books">Książki</button>
                        <button class="btn filter-btn" data-filter="ebooks">E-booki</button>
                        <button class="btn filter-btn" data-filter="courses">Kursy</button>
                        <button class="btn filter-btn" data-filter="gadgets">Gadżety</button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Lista produktów -->
    <section class="section">
        <div class="container">
            <div class="row g-4" id="productsContainer">
                <!-- Produkt 1 -->
                <div class="col-lg-3 col-md-6 product-item-wrapper" data-category="books">
                    <div class="card product-card">
                        <div class="product-img-container">
                            <img src="https://images.unsplash.com/photo-1544947950-fa07a98d237f?w=400&h=300&fit=crop" 
                                 class="card-img-top product-img" alt="Książka: Innowacje w Kardiochirurgii">
                            <span class="product-badge product-badge-new">Nowość</span>
                        </div>
                        <div class="product-info">
                            <span class="product-category">Książki</span>
                            <h5 class="product-title">Innowacje w Medycynie</h5>
                            <p class="product-description">Kompleksowy przewodnik po najnowszych innowacjach w dziedzinie medycyny i technologii zdrowotnych.</p>
                            <div class="product-price">
                                <span>79,00 zł</span>
                            </div>
                            <div class="product-actions">
                                <button class="btn btn-primary-custom product-btn">
                                    <i class="fas fa-shopping-cart"></i>
                                    <span>Dodaj do koszyka</span>
                                </button>
                                <button class="wishlist-btn">
                                    <i class="far fa-heart"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Produkt 2 -->
                <div class="col-lg-3 col-md-6 product-item-wrapper" data-category="ebooks">
                    <div class="card product-card">
                        <div class="product-img-container">
                            <img src="https://images.unsplash.com/photo-1526628953301-3e589a6a8b74?w=400&h=300&fit=crop" 
                                 class="card-img-top product-img" alt="Kubek Innowacyjna Medycyna">
                        </div>
                        <div class="product-info">
                            <span class="product-category">E-booki</span>
                            <h5 class="product-title">E-book: Telemedycyna</h5>
                            <p class="product-description">Praktyczny przewodnik po telemedycynie i jej zastosowaniach w nowoczesnej opiece zdrowotnej.</p>
                            <div class="product-price">
                                <span>39,00 zł</span>
                            </div>
                            <div class="product-actions">
                                <button class="btn btn-primary-custom product-btn">
                                    <i class="fas fa-shopping-cart"></i>
                                    <span>Dodaj do koszyka</span>
                                </button>
                                <button class="wishlist-btn">
                                    <i class="far fa-heart"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Produkt 3 -->
                <div class="col-lg-3 col-md-6 product-item-wrapper" data-category="courses">
                    <div class="card product-card">
                        <div class="product-img-container">
                            <img src="https://images.unsplash.com/photo-1540575467063-178a50c2df87?w=400&h=300&fit=crop" 
                                 class="card-img-top product-img" alt="Kurs: AI w Medycynie">
                        </div>
                        <div class="product-info">
                            <span class="product-category">Kursy</span>
                            <h5 class="product-title">Kurs: AI w Medycynie</h5>
                            <p class="product-description">Kompleksowy kurs online na temat zastosowania sztucznej inteligencji w diagnostyce i terapii.</p>
                            <div class="product-price">
                                <span class="product-price-old">299,00 zł</span>
                                <span>199,00 zł</span>
                            </div>
                            <div class="product-actions">
                                <button class="btn btn-primary-custom product-btn">
                                    <i class="fas fa-shopping-cart"></i>
                                    <span>Dodaj do koszyka</span>
                                </button>
                                <button class="wishlist-btn">
                                    <i class="far fa-heart"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Produkt 4 -->
                <div class="col-lg-3 col-md-6 product-item-wrapper" data-category="gadgets">
                    <div class="card product-card">
                        <div class="product-img-container">
                            <img src="https://images.unsplash.com/photo-1526628953301-3e589a6a8b74?w=400&h=300&fit=crop" 
                                 class="card-img-top product-img" alt="Kubek Innowacyjna Medycyna">
                        </div>
                        <div class="product-info">
                            <span class="product-category">Gadżety</span>
                            <h5 class="product-title">Kubek Innowacyjna Medycyna</h5>
                            <p class="product-description">Wysokiej jakości kubek ceramiczny z logo klastra Innowacyjna Medycyna.</p>
                            <div class="product-price">
                                <span>29,00 zł</span>
                            </div>
                            <div class="product-actions">
                                <button class="btn btn-primary-custom product-btn">
                                    <i class="fas fa-shopping-cart"></i>
                                    <span>Dodaj do koszyka</span>
                                </button>
                                <button class="wishlist-btn">
                                    <i class="far fa-heart"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Produkt 5 -->
                <div class="col-lg-3 col-md-6 product-item-wrapper" data-category="books">
                    <div class="card product-card">
                        <div class="product-img-container">
                            <img src="https://images.unsplash.com/photo-1544947950-fa07a98d237f?w=400&h=300&fit=crop" 
                                 class="card-img-top product-img" alt="Medycyna Personalizowana">
                        </div>
                        <div class="product-info">
                            <span class="product-category">Książki</span>
                            <h5 class="product-title">Medycyna Personalizowana</h5>
                            <p class="product-description">Kompleksowe omówienie koncepcji medycyny personalizowanej i jej wpływu na przyszłość ochrony zdrowia.</p>
                            <div class="product-price">
                                <span>89,00 zł</span>
                            </div>
                            <div class="product-actions">
                                <button class="btn btn-primary-custom product-btn">
                                    <i class="fas fa-shopping-cart"></i>
                                    <span>Dodaj do koszyka</span>
                                </button>
                                <button class="wishlist-btn">
                                    <i class="far fa-heart"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Produkt 6 -->
                <div class="col-lg-3 col-md-6 product-item-wrapper" data-category="ebooks">
                    <div class="card product-card">
                        <div class="product-img-container">
                            <img src="https://images.unsplash.com/photo-1544947950-fa07a98d237f?w=400&h=300&fit=crop" 
                                 class="card-img-top product-img" alt="E-book: Genomika">
                            <span class="product-badge product-badge-sale">Promocja</span>
                        </div>
                        <div class="product-info">
                            <span class="product-category">E-booki</span>
                            <h5 class="product-title">E-book: Genomika</h5>
                            <p class="product-description">Przewodnik po genomice i jej zastosowaniach w diagnostyce i terapii chorób genetycznych.</p>
                            <div class="product-price">
                                <span class="product-price-old">49,00 zł</span>
                                <span>29,00 zł</span>
                            </div>
                            <div class="product-actions">
                                <button class="btn btn-primary-custom product-btn">
                                    <i class="fas fa-shopping-cart"></i>
                                    <span>Dodaj do koszyka</span>
                                </button>
                                <button class="wishlist-btn">
                                    <i class="far fa-heart"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Produkt 7 -->
                <div class="col-lg-3 col-md-6 product-item-wrapper" data-category="courses">
                    <div class="card product-card">
                        <div class="product-img-container">
                            <img src="https://images.unsplash.com/photo-1540575467063-178a50c2df87?w=400&h=300&fit=crop" 
                                 class="card-img-top product-img" alt="Kurs: Telemedycyna">
                        </div>
                        <div class="product-info">
                            <span class="product-category">Kursy</span>
                            <h5 class="product-title">Kurs: Telemedycyna</h5>
                            <p class="product-description">Praktyczny kurs online na temat wdrażania rozwiązań telemedycznych w codziennej praktyce lekarskiej.</p>
                            <div class="product-price">
                                <span>249,00 zł</span>
                            </div>
                            <div class="product-actions">
                                <button class="btn btn-primary-custom product-btn">
                                    <i class="fas fa-shopping-cart"></i>
                                    <span>Dodaj do koszyka</span>
                                </button>
                                <button class="wishlist-btn">
                                    <i class="far fa-heart"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Produkt 8 -->
                <div class="col-lg-3 col-md-6 product-item-wrapper" data-category="gadgets">
                    <div class="card product-card">
                        <div class="product-img-container">
                            <img src="https://images.unsplash.com/photo-1526628953301-3e589a6a8b74?w=400&h=300&fit=crop" 
                                 class="card-img-top product-img" alt="Torba Innowacyjna Medycyna">
                        </div>
                        <div class="product-info">
                            <span class="product-category">Gadżety</span>
                            <h5 class="product-title">Torba Innowacyjna Medycyna</h5>
                            <p class="product-description">Ekologiczna torba bawełniana z logo klastra Innowacyjna Medycyna.</p>
                            <div class="product-price">
                                <span>19,00 zł</span>
                            </div>
                            <div class="product-actions">
                                <button class="btn btn-primary-custom product-btn">
                                    <i class="fas fa-shopping-cart"></i>
                                    <span>Dodaj do koszyka</span>
                                </button>
                                <button class="wishlist-btn">
                                    <i class="far fa-heart"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Produkt 9 -->
                <div class="col-lg-3 col-md-6 product-item-wrapper" data-category="books">
                    <div class="card product-card">
                        <div class="product-img-container">
                            <img src="https://images.unsplash.com/photo-1544947950-fa07a98d237f?w=400&h=300&fit=crop" 
                                 class="card-img-top product-img" alt="Terapie Genowe">
                        </div>
                        <div class="product-info">
                            <span class="product-category">Książki</span>
                            <h5 class="product-title">Terapie Genowe</h5>
                            <p class="product-description">Kompleksowe omówienie terapii genowych i ich zastosowań w leczeniu chorób genetycznych.</p>
                            <div class="product-price">
                                <span>99,00 zł</span>
                            </div>
                            <div class="product-actions">
                                <button class="btn btn-primary-custom product-btn">
                                    <i class="fas fa-shopping-cart"></i>
                                    <span>Dodaj do koszyka</span>
                                </button>
                                <button class="wishlist-btn">
                                    <i class="far fa-heart"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Brak wyników -->
            <div class="text-center py-5 d-none" id="noResults">
                <div class="text-muted mb-4">
                    <i class="fas fa-search fa-3x"></i>
                </div>
                <h3 class="h4 mb-3">Nie znaleziono produktów</h3>
                <p class="text-muted">Spróbuj zmienić kryteria wyszukiwania.</p>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <!-- Logo i opis -->
                <div class="col-lg-6 mb-4 mb-lg-0">
                    <img src="http://innowacyjnamedycyna.eu/wp-content/uploads/2013/11/logo.png" 
                         alt="Innowacyjna Medycyna" 
                         class="mb-4" style="height: 48px; filter: brightness(0) invert(1);">
                    <p class="mb-4" style="color: var(--light);">
                        Innowacyjna Medycyna to wiodący klaster medyczny w Polsce, 
                        skupiający się na rozwoju nowoczesnych technologii medycznych 
                        i promocji innowacyjnych rozwiązań w ochronie zdrowia.
                    </p>
                    <div class="mb-4">
                        <a href="#" class="social-icon"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-linkedin"></i></a>
                    </div>
                </div>
                
                <!-- Linki -->
                <div class="col-md-3 col-6 mb-4 mb-md-0">
                    <h5>Szybkie linki</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="articles.html">Artykuły</a></li>
                        <li class="mb-2"><a href="gallery.html">Galeria</a></li>
                        <li class="mb-2"><a href="shop.html">Sklep</a></li>
                        <li><a href="members.html">Członkowie klastra</a></li>
                    </ul>
                </div>
                
                <!-- Kontakt -->
                <div class="col-md-3 col-6">
                    <h5>Kontakt</h5>
                    <address>
                        <p class="mb-2">ul. Przykładowa 123</p>
                        <p class="mb-2">00-001 Warszawa</p>
                        <p class="mb-2">
                            <a href="tel:+48123456789">+48 123 456 789</a>
                        </p>
                        <p>
                            <a href="mailto:<EMAIL>"><EMAIL></a>
                        </p>
                    </address>
                </div>
            </div>
            
            <div class="footer-bottom">
                <div class="row">
                    <div class="col-md-6 mb-3 mb-md-0">
                        <p>© 2024 Innowacyjna Medycyna. Wszystkie prawa zastrzeżone.</p>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <a href="#" class="me-3">Polityka prywatności</a>
                        <a href="#">Regulamin</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Main JS -->
    <script src="js/main.js"></script>
    
    <!-- Shop JS -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Filtrowanie produktów
            const filterButtons = document.querySelectorAll('.filter-btn');
            const products = document.querySelectorAll('#productsContainer > div');
            const searchInput = document.getElementById('searchInput');
            const noResults = document.getElementById('noResults');
            
            // Funkcja do aktualizacji wyników
            function updateResults() {
                const searchTerm = searchInput.value.toLowerCase();
                const activeFilter = document.querySelector('.filter-btn.active').getAttribute('data-filter');
                
                let visibleCount = 0;
                
                products.forEach(product => {
                    const type = product.getAttribute('data-type');
                    const title = product.querySelector('.product-title').textContent.toLowerCase();
                    const description = product.querySelector('.product-description').textContent.toLowerCase();
                    
                    const matchesFilter = activeFilter === 'all' || type === activeFilter;
                    const matchesSearch = title.includes(searchTerm) || description.includes(searchTerm);
                    
                    if (matchesFilter && matchesSearch) {
                        product.style.display = 'block';
                        visibleCount++;
                    } else {
                        product.style.display = 'none';
                    }
                });
                
                // Pokaż lub ukryj komunikat o braku wyników
                if (visibleCount === 0) {
                    noResults.classList.remove('d-none');
                } else {
                    noResults.classList.add('d-none');
                }
            }
            
            // Obsługa kliknięć na przyciski filtrów
            filterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    filterButtons.forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');
                    updateResults();
                });
            });
            
            // Obsługa wyszukiwania
            searchInput.addEventListener('input', updateResults);
            
            // Obsługa przycisków "Ulubione"
            const wishlistButtons = document.querySelectorAll('.wishlist-btn');
            wishlistButtons.forEach(button => {
                button.addEventListener('click', function() {
                    this.classList.toggle('active');
                    const icon = this.querySelector('i');
                    if (this.classList.contains('active')) {
                        icon.classList.remove('far');
                        icon.classList.add('fas');
                    } else {
                        icon.classList.remove('fas');
                        icon.classList.add('far');
                    }
                });
            });
        });
    </script>
</body>
</html> 