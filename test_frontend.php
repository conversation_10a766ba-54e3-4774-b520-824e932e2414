<?php
// Test frontendu - sprawdź czy wszystkie klasy się ładują

echo "=== Test Frontendu CMS ===\n\n";

// De<PERSON><PERSON><PERSON>ż<PERSON>
define('FRONTEND_PATH', __DIR__ . '/frontend');
define('BASE_PATH', __DIR__);

try {
    echo "1. Ładowanie klas backendu...\n";
    
    // Załaduj klasy backendu
    require_once BASE_PATH . '/php/app/Database.php';
    require_once BASE_PATH . '/php/models/Article.php';
    require_once BASE_PATH . '/php/models/StaticPage.php';
    require_once BASE_PATH . '/php/models/Gallery.php';
    require_once BASE_PATH . '/php/models/Menu.php';
    require_once BASE_PATH . '/php/models/Category.php';
    require_once BASE_PATH . '/php/models/Banner.php';
    
    echo "   ✓ Klasy backendu załadowane\n";
    
    echo "2. Ładowanie klas frontendu...\n";
    
    // Załaduj klasy frontendu
    require_once FRONTEND_PATH . '/app/FrontendRouter.php';
    require_once FRONTEND_PATH . '/app/TemplateEngine.php';
    require_once FRONTEND_PATH . '/app/FrontendController.php';
    
    echo "   ✓ Klasy frontendu załadowane\n";
    
    echo "3. Test połączenia z bazą danych...\n";
    
    // Inicjalizacja bazy danych
    $database = new Database();
    $db = $database->getConnection();
    
    echo "   ✓ Połączenie z bazą danych działa\n";
    
    echo "4. Test modeli...\n";
    
    // Test modeli
    $articleModel = new Article($db);
    $articles = $articleModel->getPublishedArticles(3);
    echo "   ✓ Model Article - pobrano " . count($articles) . " opublikowanych artykułów\n";
    
    $staticPageModel = new StaticPage($db);
    $pages = $staticPageModel->getPublishedPages();
    echo "   ✓ Model StaticPage - pobrano " . count($pages) . " opublikowanych stron\n";
    
    $galleryModel = new Gallery($db);
    $images = $galleryModel->getPublishedImages(null, 5);
    echo "   ✓ Model Gallery - pobrano " . count($images) . " zdjęć\n";
    
    $menuModel = new Menu($db);
    $mainMenu = $menuModel->getPublishedMenuBySlug('main-menu');
    if ($mainMenu) {
        $menuItems = $menuModel->getPublishedMenuItems($mainMenu['id']);
        echo "   ✓ Model Menu - pobrano " . count($menuItems) . " elementów menu\n";
    } else {
        echo "   ! Menu 'main-menu' nie znalezione\n";
    }
    
    $bannerModel = new Banner($db);
    $banners = $bannerModel->getActiveBanners();
    echo "   ✓ Model Banner - pobrano " . count($banners) . " aktywnych bannerów\n";
    
    echo "5. Test template engine...\n";
    
    // Test template engine
    $skinPath = FRONTEND_PATH . '/skins/zrywbud';
    if (is_dir($skinPath)) {
        $templateEngine = new TemplateEngine($skinPath);
        echo "   ✓ TemplateEngine zainicjalizowany dla skórki zrywbud\n";
        echo "   ✓ Ścieżka skórki: " . $templateEngine->getSkinPath() . "\n";
        echo "   ✓ URL skórki: " . $templateEngine->getSkinUrl() . "\n";
    } else {
        echo "   ! Katalog skórki nie istnieje: $skinPath\n";
    }
    
    echo "6. Test struktury plików...\n";
    
    // Sprawdź czy istnieją kluczowe pliki
    $requiredFiles = [
        FRONTEND_PATH . '/index.php',
        FRONTEND_PATH . '/.htaccess',
        FRONTEND_PATH . '/skins/zrywbud/templates/layout.php',
        FRONTEND_PATH . '/skins/zrywbud/templates/home.php',
        FRONTEND_PATH . '/skins/zrywbud/partials/header.php',
        FRONTEND_PATH . '/skins/zrywbud/partials/footer.php',
        FRONTEND_PATH . '/skins/zrywbud/style.css',
        FRONTEND_PATH . '/skins/zrywbud/script.js'
    ];
    
    foreach ($requiredFiles as $file) {
        if (file_exists($file)) {
            echo "   ✓ " . basename($file) . "\n";
        } else {
            echo "   ✗ BRAK: " . basename($file) . "\n";
        }
    }
    
    echo "\n=== Test zakończony pomyślnie! ===\n";
    echo "\nAby uruchomić frontend:\n";
    echo "1. Skonfiguruj serwer web aby wskazywał na katalog 'frontend/'\n";
    echo "2. Upewnij się że mod_rewrite jest włączony\n";
    echo "3. Otwórz frontend w przeglądarce\n";
    
} catch (Exception $e) {
    echo "\n✗ BŁĄD: " . $e->getMessage() . "\n";
    echo "Plik: " . $e->getFile() . "\n";
    echo "Linia: " . $e->getLine() . "\n";
}
